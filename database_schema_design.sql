-- AVINI Labs Database Schema Design
-- SQLite Database Schema for migrating from JSON files
-- Generated based on analysis of 72 JSON files with 5,385 total records

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Core Entity Tables

-- 1. Tenants (Franchises/Labs)
CREATE TABLE tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    site_code TEXT UNIQUE NOT NULL,
    address TEXT,
    city TEXT,
    state TEXT,
    pincode TEXT,
    contact_phone TEXT,
    email TEXT,
    contact_person TEXT,
    contact_person_phone TEXT,
    license_number TEXT,
    is_hub BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    use_site_code_prefix BOOLEAN DEFAULT TRUE,
    established_date DATE,
    franchise_fee DECIMAL(10,2),
    monthly_fee DECIMAL(10,2),
    commission_rate DECIMAL(5,2),
    -- Enhanced Payment Configuration
    default_payment_terms TEXT CHECK (default_payment_terms IN ('prepaid', 'credit')) DEFAULT 'prepaid',
    default_credit_period_days INTEGER DEFAULT 30,
    credit_limit DECIMAL(12,2) DEFAULT 0,
    current_outstanding DECIMAL(12,2) DEFAULT 0,
    credit_hold BOOLEAN DEFAULT FALSE,
    auto_approve_requests BOOLEAN DEFAULT FALSE,
    procurement_enabled BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Users
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    email TEXT UNIQUE,
    first_name TEXT,
    last_name TEXT,
    role TEXT NOT NULL CHECK (role IN ('admin', 'hub_admin', 'franchise_admin', 'lab_technician', 'receptionist')),
    tenant_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 3. Patients
CREATE TABLE patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT,
    gender TEXT CHECK (gender IN ('Male', 'Female', 'Other')),
    date_of_birth DATE,
    phone TEXT,
    email TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    blood_group TEXT,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Master Data Tables

-- 4. Departments
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. Test Categories
CREATE TABLE test_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. Sample Types
CREATE TABLE sample_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_name TEXT NOT NULL,
    type_code TEXT UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. Containers
CREATE TABLE containers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    container_name TEXT NOT NULL,
    code TEXT UNIQUE,
    description TEXT,
    color_code TEXT,
    volume_required DECIMAL(8,2),
    additive TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. Test Master
CREATE TABLE test_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_name TEXT NOT NULL,
    test_code TEXT UNIQUE,
    hms_code TEXT,
    department_id INTEGER,
    category_id INTEGER,
    short_name TEXT,
    display_name TEXT,
    method TEXT,
    specimen TEXT,
    container_id INTEGER,
    reference_range TEXT,
    result_unit TEXT,
    decimals INTEGER DEFAULT 2,
    test_price DECIMAL(10,2),
    critical_low DECIMAL(10,2),
    critical_high DECIMAL(10,2),
    instructions TEXT,
    interpretation TEXT,
    notes TEXT,
    min_sample_qty DECIMAL(8,2),
    service_time INTEGER,
    reporting_days INTEGER,
    cutoff_time TIME,
    emergency_process_time INTEGER,
    emergency_process_period TEXT,
    expiry_time INTEGER,
    expiry_period TEXT,
    test_done_on TEXT,
    applicable_to TEXT,
    emr_classification TEXT,
    international_code TEXT,
    alert_message TEXT,
    alert_period TEXT,
    alert_sms BOOLEAN DEFAULT FALSE,
    special_report BOOLEAN DEFAULT FALSE,
    unacceptable_conditions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (category_id) REFERENCES test_categories(id),
    FOREIGN KEY (container_id) REFERENCES containers(id)
);

-- 9. Samples
CREATE TABLE samples (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sample_id TEXT UNIQUE NOT NULL,
    patient_id INTEGER NOT NULL,
    sample_type_id INTEGER,
    container_id INTEGER,
    collection_date DATE,
    collection_time TIME,
    status TEXT DEFAULT 'collected' CHECK (status IN ('collected', 'processing', 'completed', 'rejected')),
    tenant_id INTEGER NOT NULL,
    collected_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (sample_type_id) REFERENCES sample_types(id),
    FOREIGN KEY (container_id) REFERENCES containers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (collected_by) REFERENCES users(id)
);

-- 10. Billing
CREATE TABLE billings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT UNIQUE NOT NULL,
    sid_number TEXT UNIQUE,
    patient_id INTEGER NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    tax DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    balance DECIMAL(10,2) DEFAULT 0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid', 'refunded')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'completed')),
    invoice_date DATE NOT NULL,
    due_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 11. Billing Items
CREATE TABLE billing_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    billing_id INTEGER NOT NULL,
    test_id INTEGER,
    test_name TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (billing_id) REFERENCES billings(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES test_master(id)
);

-- 12. Results
CREATE TABLE results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    result_id TEXT UNIQUE,
    sample_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    value TEXT,
    unit TEXT,
    reference_range TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'verified', 'rejected')),
    result_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    verified_by INTEGER,
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (test_id) REFERENCES test_master(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (verified_by) REFERENCES users(id)
);

-- Additional Master Data Tables

-- 13. Payment Methods
CREATE TABLE payment_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    method_name TEXT NOT NULL,
    method_code TEXT UNIQUE,
    description TEXT,
    is_online BOOLEAN DEFAULT FALSE,
    processing_fee DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. Roles
CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    permission_ids TEXT, -- JSON array of permission IDs
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 15. Permissions
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    module TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for Performance
CREATE INDEX idx_patients_tenant_id ON patients(tenant_id);
CREATE INDEX idx_patients_patient_id ON patients(patient_id);
CREATE INDEX idx_samples_patient_id ON samples(patient_id);
CREATE INDEX idx_samples_tenant_id ON samples(tenant_id);
CREATE INDEX idx_billings_patient_id ON billings(patient_id);
CREATE INDEX idx_billings_tenant_id ON billings(tenant_id);
CREATE INDEX idx_billings_sid_number ON billings(sid_number);
CREATE INDEX idx_billing_items_billing_id ON billing_items(billing_id);
CREATE INDEX idx_results_sample_id ON results(sample_id);
CREATE INDEX idx_results_test_id ON results(test_id);
CREATE INDEX idx_results_tenant_id ON results(tenant_id);
CREATE INDEX idx_test_master_department_id ON test_master(department_id);
CREATE INDEX idx_test_master_category_id ON test_master(category_id);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_username ON users(username);

-- Triggers for updated_at timestamps
CREATE TRIGGER update_tenants_timestamp 
    AFTER UPDATE ON tenants 
    BEGIN 
        UPDATE tenants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_users_timestamp 
    AFTER UPDATE ON users 
    BEGIN 
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_patients_timestamp 
    AFTER UPDATE ON patients 
    BEGIN 
        UPDATE patients SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_samples_timestamp 
    AFTER UPDATE ON samples 
    BEGIN 
        UPDATE samples SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_billings_timestamp 
    AFTER UPDATE ON billings 
    BEGIN 
        UPDATE billings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_results_timestamp
    AFTER UPDATE ON results
    BEGIN
        UPDATE results SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Additional Master Data Tables (Extended Schema)

-- 16. Instruments
CREATE TABLE instruments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    model TEXT,
    manufacturer TEXT,
    serial_number TEXT UNIQUE,
    installation_date DATE,
    calibration_due DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 17. Reagents
CREATE TABLE reagents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    lot_number TEXT,
    expiry_date DATE,
    manufacturer TEXT,
    storage_temperature TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 18. Suppliers
CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 19. Inventory
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    sku TEXT UNIQUE,
    category TEXT,
    description TEXT,
    quantity INTEGER DEFAULT 0,
    unit TEXT,
    reorder_level INTEGER DEFAULT 0,
    cost_price DECIMAL(10,2),
    selling_price DECIMAL(10,2),
    supplier_id INTEGER,
    location TEXT,
    expiry_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 20. Test Methods
CREATE TABLE test_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    principle TEXT,
    procedure TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 21. Test Parameters
CREATE TABLE test_parameters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parameter_name TEXT NOT NULL,
    parameter_code TEXT UNIQUE,
    unit TEXT,
    reference_range TEXT,
    method TEXT,
    category_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES test_categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 22. Units of Measurement
CREATE TABLE units_of_measurement (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    symbol TEXT,
    type TEXT,
    conversion_factor DECIMAL(10,6) DEFAULT 1.0,
    technical BOOLEAN DEFAULT FALSE,
    inventory BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 23. Specimen Master
CREATE TABLE specimen_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    specimen TEXT NOT NULL,
    container TEXT,
    disposable BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 24. Container Master
CREATE TABLE container_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_name TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 25. Purchase Requests
CREATE TABLE purchase_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number TEXT UNIQUE NOT NULL,
    requesting_tenant_id INTEGER NOT NULL,
    hub_tenant_id INTEGER NOT NULL DEFAULT 1, -- Mayiladuthurai hub
    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    status TEXT CHECK (status IN ('draft', 'submitted', 'reviewing', 'approved', 'rejected', 'fulfilled', 'cancelled')) DEFAULT 'draft',
    request_date DATE NOT NULL,
    required_date DATE,
    notes TEXT,
    total_estimated_amount DECIMAL(12,2) DEFAULT 0,
    approved_by INTEGER,
    approved_at TIMESTAMP,
    approval_notes TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requesting_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (hub_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 26. Purchase Request Items
CREATE TABLE purchase_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_request_id INTEGER NOT NULL,
    inventory_item_id INTEGER,
    item_name TEXT NOT NULL,
    item_description TEXT,
    requested_quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    estimated_unit_price DECIMAL(10,2),
    estimated_total_price DECIMAL(12,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id)
);

-- 27. Purchase Orders (to External Suppliers)
CREATE TABLE purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    po_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER NOT NULL,
    tenant_id INTEGER NOT NULL, -- Hub that's ordering
    status TEXT CHECK (status IN ('draft', 'sent', 'acknowledged', 'partial_delivered', 'delivered', 'cancelled')) DEFAULT 'draft',
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    payment_terms TEXT,
    delivery_address TEXT,
    notes TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 28. Purchase Order Items
CREATE TABLE purchase_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_order_id INTEGER NOT NULL,
    inventory_item_id INTEGER,
    item_name TEXT NOT NULL,
    item_description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    received_quantity INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id)
);

-- 29. Proforma Invoices
CREATE TABLE proforma_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_number TEXT UNIQUE NOT NULL,
    purchase_request_id INTEGER,
    from_tenant_id INTEGER NOT NULL, -- Hub
    to_tenant_id INTEGER NOT NULL, -- Requesting franchise
    status TEXT CHECK (status IN ('draft', 'sent', 'approved', 'paid', 'expired', 'cancelled')) DEFAULT 'draft',
    invoice_date DATE NOT NULL,
    valid_until DATE NOT NULL,
    payment_terms TEXT CHECK (payment_terms IN ('prepaid', 'credit')) DEFAULT 'prepaid',
    credit_period_days INTEGER DEFAULT 0,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 18,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    balance_amount DECIMAL(12,2) DEFAULT 0,
    payment_due_date DATE,
    notes TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 30. Proforma Invoice Items
CREATE TABLE proforma_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_invoice_id INTEGER NOT NULL,
    inventory_item_id INTEGER,
    item_name TEXT NOT NULL,
    item_description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proforma_invoice_id) REFERENCES proforma_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id)
);

-- 31. Delivery Notes
CREATE TABLE delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_number TEXT UNIQUE NOT NULL,
    proforma_invoice_id INTEGER,
    purchase_order_id INTEGER, -- If delivery is from supplier
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    delivery_type TEXT CHECK (delivery_type IN ('hub_to_franchise', 'supplier_to_hub')) DEFAULT 'hub_to_franchise',
    status TEXT CHECK (status IN ('prepared', 'dispatched', 'in_transit', 'delivered', 'cancelled')) DEFAULT 'prepared',
    dispatch_date DATE,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    delivery_address TEXT,
    delivery_contact_person TEXT,
    delivery_contact_phone TEXT,
    vehicle_number TEXT,
    driver_name TEXT,
    driver_phone TEXT,
    notes TEXT,
    recipient_signature TEXT, -- Base64 encoded signature
    recipient_name TEXT,
    received_at TIMESTAMP,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proforma_invoice_id) REFERENCES proforma_invoices(id),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 32. Delivery Note Items
CREATE TABLE delivery_note_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_note_id INTEGER NOT NULL,
    inventory_item_id INTEGER,
    item_name TEXT NOT NULL,
    item_description TEXT,
    ordered_quantity INTEGER NOT NULL,
    delivered_quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2),
    total_price DECIMAL(12,2),
    batch_number TEXT,
    expiry_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id)
);

-- 33. Payment Transactions
CREATE TABLE payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_number TEXT UNIQUE NOT NULL,
    proforma_invoice_id INTEGER,
    final_invoice_id INTEGER, -- Links to existing billings table
    from_tenant_id INTEGER NOT NULL, -- Paying franchise
    to_tenant_id INTEGER NOT NULL, -- Receiving hub
    payment_type TEXT CHECK (payment_type IN ('proforma_payment', 'final_payment', 'partial_payment')) DEFAULT 'final_payment',
    payment_method TEXT CHECK (payment_method IN ('cash', 'bank_transfer', 'cheque', 'upi', 'card')) DEFAULT 'bank_transfer',
    amount DECIMAL(12,2) NOT NULL,
    payment_date DATE NOT NULL,
    reference_number TEXT,
    bank_details TEXT,
    status TEXT CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    notes TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proforma_invoice_id) REFERENCES proforma_invoices(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 34. Inventory Transfers (Inter-franchise movements)
CREATE TABLE inventory_transfers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transfer_number TEXT UNIQUE NOT NULL,
    delivery_note_id INTEGER,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    transfer_type TEXT CHECK (transfer_type IN ('hub_to_franchise', 'franchise_to_hub', 'supplier_to_hub')) DEFAULT 'hub_to_franchise',
    status TEXT CHECK (status IN ('pending', 'approved', 'dispatched', 'received', 'cancelled')) DEFAULT 'pending',
    transfer_date DATE NOT NULL,
    received_date DATE,
    notes TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 35. Inventory Transfer Items
CREATE TABLE inventory_transfer_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_transfer_id INTEGER NOT NULL,
    inventory_item_id INTEGER NOT NULL,
    transferred_quantity INTEGER NOT NULL,
    received_quantity INTEGER DEFAULT 0,
    unit TEXT NOT NULL,
    batch_number TEXT,
    expiry_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_transfer_id) REFERENCES inventory_transfers(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id)
);

-- 25. Method Master
CREATE TABLE method_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    method TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 26. Organism Master
CREATE TABLE organism_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    no_growth BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 27. Antibiotic Master
CREATE TABLE antibiotic_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    antibiotic_code TEXT UNIQUE NOT NULL,
    antibiotic_group TEXT,
    antibiotic_description TEXT NOT NULL,
    antibiotic_content TEXT,
    order_sequence INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 28. Organism vs Antibiotic
CREATE TABLE organism_vs_antibiotic (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    organism_id INTEGER NOT NULL,
    antibiotic_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organism_id) REFERENCES organism_master(id),
    FOREIGN KEY (antibiotic_id) REFERENCES antibiotic_master(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE(organism_id, antibiotic_id)
);

-- 29. Main Department Master
CREATE TABLE main_department_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    major_department TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    department TEXT NOT NULL,
    order_sequence INTEGER,
    short_name TEXT,
    queue TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 30. Storerooms
CREATE TABLE storerooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    storeroom_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    tenant_id INTEGER NOT NULL,
    location_details TEXT,
    capacity DECIMAL(10,2),
    status TEXT CHECK (status IN ('active', 'inactive')) DEFAULT 'active',
    manager_name TEXT,
    manager_contact TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 31. Purchase Requests
CREATE TABLE purchase_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number TEXT UNIQUE NOT NULL,
    requesting_tenant_id INTEGER NOT NULL,
    hub_tenant_id INTEGER NOT NULL,
    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    status TEXT CHECK (status IN ('draft', 'submitted', 'approved', 'rejected', 'processing', 'completed', 'cancelled')) DEFAULT 'draft',
    request_date DATE NOT NULL,
    required_date DATE NOT NULL,
    notes TEXT,
    total_estimated_amount DECIMAL(12,2) DEFAULT 0,
    storeroom_id INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requesting_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (hub_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 32. Purchase Request Items
CREATE TABLE purchase_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_request_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    requested_quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    estimated_unit_price DECIMAL(10,2) DEFAULT 0,
    total_estimated_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id) ON DELETE CASCADE
);

-- 33. Purchase Orders
CREATE TABLE purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    po_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER,
    tenant_id INTEGER NOT NULL,
    status TEXT CHECK (status IN ('draft', 'sent', 'confirmed', 'received', 'cancelled')) DEFAULT 'draft',
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    payment_terms TEXT,
    delivery_address TEXT,
    notes TEXT,
    purchase_request_id INTEGER,
    received_at TIMESTAMP,
    received_by INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (received_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 34. Purchase Order Items
CREATE TABLE purchase_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_order_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE
);

-- 35. Proforma Invoices
CREATE TABLE proforma_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_number TEXT UNIQUE NOT NULL,
    purchase_request_id INTEGER,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    status TEXT CHECK (status IN ('draft', 'sent', 'accepted', 'rejected', 'expired')) DEFAULT 'draft',
    invoice_date DATE NOT NULL,
    valid_until DATE,
    payment_terms TEXT,
    credit_period_days INTEGER DEFAULT 30,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    balance_amount DECIMAL(12,2) DEFAULT 0,
    payment_due_date DATE,
    notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 36. Proforma Invoice Items
CREATE TABLE proforma_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_invoice_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proforma_invoice_id) REFERENCES proforma_invoices(id) ON DELETE CASCADE
);

-- 37. Delivery Notes
CREATE TABLE delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_number TEXT UNIQUE NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    delivery_type TEXT CHECK (delivery_type IN ('hub_to_franchise', 'supplier_to_hub')) NOT NULL,
    status TEXT CHECK (status IN ('prepared', 'dispatched', 'delivered', 'cancelled')) DEFAULT 'prepared',
    delivery_date DATE,
    expected_delivery_date DATE,
    delivery_address TEXT,
    total_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    purchase_request_id INTEGER,
    purchase_order_id INTEGER,
    tracking_number TEXT,
    carrier TEXT,
    dispatched_at TIMESTAMP,
    dispatched_by INTEGER,
    delivered_at TIMESTAMP,
    received_by INTEGER,
    receiver_signature TEXT,
    delivery_notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (dispatched_by) REFERENCES users(id),
    FOREIGN KEY (received_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 38. Delivery Note Items
CREATE TABLE delivery_note_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_note_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE
);

-- 39. Payment Transactions
CREATE TABLE payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_number TEXT UNIQUE NOT NULL,
    transaction_type TEXT CHECK (transaction_type IN ('payment', 'refund', 'adjustment')) NOT NULL,
    reference_type TEXT CHECK (reference_type IN ('proforma_invoice', 'purchase_order', 'delivery_note')) NOT NULL,
    reference_id INTEGER NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_method TEXT,
    payment_date DATE NOT NULL,
    status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    notes TEXT,
    transaction_reference TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 40. Inventory Transfers
CREATE TABLE inventory_transfers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transfer_number TEXT UNIQUE NOT NULL,
    transfer_type TEXT CHECK (transfer_type IN ('hub_to_franchise', 'franchise_to_hub', 'franchise_to_franchise')) NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    from_storeroom_id INTEGER,
    to_storeroom_id INTEGER,
    status TEXT CHECK (status IN ('draft', 'submitted', 'approved', 'in_transit', 'delivered', 'cancelled')) DEFAULT 'draft',
    transfer_date DATE,
    expected_delivery_date DATE,
    notes TEXT,
    total_value DECIMAL(12,2) DEFAULT 0,
    purchase_request_id INTEGER,
    delivery_note_id INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (from_storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (to_storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 41. Inventory Transfer Items
CREATE TABLE inventory_transfer_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_transfer_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_value DECIMAL(10,2) DEFAULT 0,
    total_value DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_transfer_id) REFERENCES inventory_transfers(id) ON DELETE CASCADE
);

-- 30. Department Settings
CREATE TABLE department_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    main TEXT,
    code TEXT UNIQUE NOT NULL,
    sub_name TEXT,
    service_time INTEGER,
    room TEXT,
    order_sequence INTEGER,
    dept_amt DECIMAL(10,2),
    short TEXT,
    collect BOOLEAN DEFAULT FALSE,
    process_receive BOOLEAN DEFAULT FALSE,
    receive BOOLEAN DEFAULT FALSE,
    no INTEGER,
    pending BOOLEAN DEFAULT FALSE,
    dept TEXT,
    barcode BOOLEAN DEFAULT FALSE,
    appt BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Additional indexes for new tables
CREATE INDEX idx_instruments_serial_number ON instruments(serial_number);
CREATE INDEX idx_reagents_lot_number ON reagents(lot_number);
CREATE INDEX idx_inventory_sku ON inventory(sku);
CREATE INDEX idx_inventory_tenant_id ON inventory(tenant_id);
CREATE INDEX idx_inventory_supplier_id ON inventory(supplier_id);
CREATE INDEX idx_test_parameters_category_id ON test_parameters(category_id);
CREATE INDEX idx_organism_vs_antibiotic_organism_id ON organism_vs_antibiotic(organism_id);
CREATE INDEX idx_organism_vs_antibiotic_antibiotic_id ON organism_vs_antibiotic(antibiotic_id);

-- Additional triggers for new tables
CREATE TRIGGER update_instruments_timestamp
    AFTER UPDATE ON instruments
    BEGIN
        UPDATE instruments SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_reagents_timestamp
    AFTER UPDATE ON reagents
    BEGIN
        UPDATE reagents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_suppliers_timestamp
    AFTER UPDATE ON suppliers
    BEGIN
        UPDATE suppliers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_inventory_timestamp
    AFTER UPDATE ON inventory
    BEGIN
        UPDATE inventory SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
