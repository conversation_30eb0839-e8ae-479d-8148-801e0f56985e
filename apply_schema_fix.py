#!/usr/bin/env python3
"""
Apply database schema fixes for procurement delivery functionality
"""
import sys
import os
import sqlite3

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def apply_schema_fixes():
    """Apply the schema fixes to the database"""
    try:
        print("🔧 Applying Database Schema Fixes")
        print("=" * 50)
        
        db_path = os.path.join('backend', 'data', 'avini_labs.db')
        
        if not os.path.exists(db_path):
            print(f"❌ Database not found: {db_path}")
            return False
        
        # Read the SQL fix file
        with open('fix_inventory_schema.sql', 'r') as f:
            sql_commands = f.read()
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Split and execute each command
            commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]
            
            for i, command in enumerate(commands, 1):
                if command:
                    try:
                        print(f"📄 Executing command {i}: {command[:50]}...")
                        cursor.execute(command)
                        print(f"✅ Command {i} executed successfully")
                    except Exception as e:
                        if "duplicate column name" in str(e).lower():
                            print(f"⚠️  Command {i} skipped - column already exists")
                        else:
                            print(f"❌ Command {i} failed: {e}")
                            return False
            
            # Commit changes
            conn.commit()
            print("\n✅ All schema fixes applied successfully!")
            
            # Verify the fixes
            print("\n🔍 Verifying schema fixes...")
            
            # Check inventory table structure
            cursor.execute("PRAGMA table_info(inventory)")
            inventory_columns = [col[1] for col in cursor.fetchall()]
            
            required_columns = ['storeroom_id', 'is_active']
            for col in required_columns:
                if col in inventory_columns:
                    print(f"✅ inventory.{col} column exists")
                else:
                    print(f"❌ inventory.{col} column missing")
            
            # Check storerooms table structure
            cursor.execute("PRAGMA table_info(storerooms)")
            storerooms_columns = [col[1] for col in cursor.fetchall()]
            
            if 'is_active' in storerooms_columns:
                print(f"✅ storerooms.is_active column exists")
            else:
                print(f"❌ storerooms.is_active column missing")
            
            return True
            
        except Exception as e:
            conn.rollback()
            print(f"❌ Error applying fixes: {e}")
            return False
        
        finally:
            conn.close()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_default_storeroom():
    """Create a default storeroom for tenant 3 if it doesn't exist"""
    try:
        print("\n🏪 Creating Default Storeroom")
        print("-" * 30)
        
        from database_manager import db_manager
        
        # Check if tenant 3 has any storerooms
        storeroom_query = "SELECT * FROM storerooms WHERE tenant_id = 3"
        storerooms = db_manager.execute_query(storeroom_query)
        
        if not storerooms:
            print("📦 No storerooms found for tenant 3, creating default...")
            
            # Create default storeroom
            insert_query = """
                INSERT INTO storerooms (name, description, tenant_id, is_active, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            db_manager.execute_query(insert_query, (
                'Main Storeroom',
                'Default storeroom for AVINI Labs Thanjavur',
                3,
                1
            ))
            
            print("✅ Default storeroom created for tenant 3")
        else:
            print(f"✅ Found {len(storerooms)} existing storerooms for tenant 3")
            for sr in storerooms:
                print(f"  - {sr['name']} (ID: {sr['id']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating storeroom: {e}")
        return False

def update_delivery_note_6():
    """Update delivery note 6 to have a valid storeroom_id"""
    try:
        print("\n📝 Updating Delivery Note 6")
        print("-" * 30)
        
        from database_manager import db_manager
        
        # Get a storeroom for tenant 3
        storeroom_query = "SELECT id FROM storerooms WHERE tenant_id = 3 AND is_active = 1 LIMIT 1"
        storerooms = db_manager.execute_query(storeroom_query)
        
        if not storerooms:
            print("❌ No active storerooms found for tenant 3")
            return False
        
        storeroom_id = storerooms[0]['id']
        
        # Update delivery note 6 to reference a purchase request with storeroom
        # First, let's create a dummy purchase request if needed
        pr_query = "SELECT id FROM purchase_requests WHERE requesting_tenant_id = 3 LIMIT 1"
        prs = db_manager.execute_query(pr_query)
        
        if prs:
            pr_id = prs[0]['id']
            print(f"✅ Found existing purchase request: {pr_id}")
        else:
            # Create a dummy purchase request
            pr_insert = """
                INSERT INTO purchase_requests (
                    request_number, requesting_tenant_id, hub_tenant_id, 
                    storeroom_id, status, priority, total_amount, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            db_manager.execute_query(pr_insert, (
                'PR-DUMMY-001', 3, 1, storeroom_id, 'approved', 'medium', 1050.00
            ))
            
            # Get the new PR ID
            prs = db_manager.execute_query("SELECT id FROM purchase_requests WHERE request_number = 'PR-DUMMY-001'")
            pr_id = prs[0]['id']
            print(f"✅ Created dummy purchase request: {pr_id}")
        
        # Update delivery note 6
        update_query = """
            UPDATE delivery_notes 
            SET purchase_request_id = ?
            WHERE id = 6
        """
        
        db_manager.execute_query(update_query, (pr_id,))
        print("✅ Updated delivery note 6 with purchase request reference")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating delivery note: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Fixing Procurement Delivery 500 Error")
    print("=" * 50)
    
    steps = [
        ("Apply Schema Fixes", apply_schema_fixes),
        ("Create Default Storeroom", create_default_storeroom),
        ("Update Delivery Note 6", update_delivery_note_6),
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n📋 Step: {step_name}")
        print("-" * 30)
        success = step_func()
        results.append((step_name, success))
        
        if not success:
            print(f"❌ Step '{step_name}' failed. Stopping.")
            break
    
    print("\n" + "=" * 50)
    print("📊 Fix Results Summary:")
    for step_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {step_name}: {status}")
    
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n🎉 All fixes applied successfully!")
        print("💡 Now test the delivery confirmation endpoint again")
    else:
        print("\n⚠️  Some fixes failed. Check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    main()
