"""
AVINI Labs Accounting Module API Routes
Provides REST API endpoints for accounting functionality
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, date
import logging

from services.accounting_service import AccountingService
from services.accounts_payable_service import AccountsPayableService
from services.accounts_receivable_service import AccountsReceivableService
from services.financial_reporting_service import FinancialReportingService
from services.accounting_integration_service import AccountingIntegrationService
from services.tax_service import TaxService
from services.inventory_accounting_service import InventoryAccountingService
from utils import token_required, require_module_access

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint
accounting_bp = Blueprint('accounting', __name__, url_prefix='/api/accounting')

# Initialize services
accounting_service = AccountingService()
ap_service = AccountsPayableService()
ar_service = AccountsReceivableService()
reporting_service = FinancialReportingService()
integration_service = AccountingIntegrationService()
tax_service = TaxService()
inventory_service = InventoryAccountingService()

# ============================================================================
# CHART OF ACCOUNTS ENDPOINTS
# ============================================================================

@accounting_bp.route('/chart-of-accounts', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_chart_of_accounts():
    """Get chart of accounts"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        account_type = request.args.get('account_type')
        
        if account_type:
            accounts = accounting_service.get_accounts_by_type(account_type, tenant_id)
        else:
            # Get all accounts for the tenant
            accounts = accounting_service.db.execute_query(
                "SELECT * FROM chart_of_accounts WHERE tenant_id = ? AND is_active = 1 ORDER BY account_code",
                (tenant_id,)
            )
        
        return jsonify({
            'success': True,
            'data': accounts,
            'count': len(accounts)
        })
        
    except Exception as e:
        logger.error(f"Error getting chart of accounts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/chart-of-accounts', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_account():
    """Create a new account"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        account_id = accounting_service.create_account(data)
        
        return jsonify({
            'success': True,
            'message': 'Account created successfully',
            'account_id': account_id
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/chart-of-accounts/<int:account_id>', methods=['PUT'])
@token_required
@require_module_access('accounting')
def update_account(account_id):
    """Update an existing account"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Add tenant_id from user context
        user = getattr(request, 'current_user', {})
        tenant_id = user.get('tenant_id')
        if not tenant_id:
            return jsonify({'error': 'Tenant ID not found'}), 400

        data['tenant_id'] = tenant_id

        success = accounting_service.update_account(account_id, data)

        if success:
            return jsonify({
                'success': True,
                'message': 'Account updated successfully'
            }), 200
        else:
            return jsonify({'error': 'Failed to update account'}), 500

    except ValueError as e:
        # Business logic errors (account not found, has transactions, etc.)
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error updating account {account_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/chart-of-accounts/<account_code>', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_account_by_code(account_code):
    """Get account by code"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        account = accounting_service.get_account_by_code(account_code, tenant_id)
        
        if not account:
            return jsonify({'error': 'Account not found'}), 404
        
        return jsonify({
            'success': True,
            'data': account
        })
        
    except Exception as e:
        logger.error(f"Error getting account: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# JOURNAL ENTRIES ENDPOINTS
# ============================================================================

@accounting_bp.route('/journal-entries', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_journal_entries():
    """Get journal entries"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        journal_type = request.args.get('journal_type')
        status = request.args.get('status')
        
        # Build query
        where_conditions = ['tenant_id = ?']
        params = [tenant_id]
        
        if journal_type:
            where_conditions.append('journal_type = ?')
            params.append(journal_type)
        
        if status:
            where_conditions.append('status = ?')
            params.append(status)
        
        where_clause = ' AND '.join(where_conditions)
        
        # Get total count
        count_query = f"SELECT COUNT(*) as total FROM journal_entries WHERE {where_clause}"
        total_result = accounting_service.db.execute_query(count_query, params)
        total = total_result[0]['total'] if total_result else 0
        
        # Get paginated results
        offset = (page - 1) * per_page
        query = f"""
            SELECT * FROM journal_entries 
            WHERE {where_clause}
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])
        
        journal_entries = accounting_service.db.execute_query(query, params)
        
        return jsonify({
            'success': True,
            'data': journal_entries,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting journal entries: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/journal-entries', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_journal_entry():
    """Create a new journal entry"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        journal_data = data.get('journal_entry', {})
        line_items = data.get('line_items', [])
        
        if not line_items:
            return jsonify({'error': 'Line items are required'}), 400
        
        journal_id = accounting_service.create_journal_entry(journal_data, line_items)
        
        return jsonify({
            'success': True,
            'message': 'Journal entry created successfully',
            'journal_id': journal_id
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating journal entry: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/journal-entries/<int:journal_id>/post', methods=['POST'])
@token_required
@require_module_access('accounting')
def post_journal_entry(journal_id):
    """Post a journal entry"""
    try:
        data = request.get_json()
        posted_by = data.get('posted_by')
        
        if not posted_by:
            return jsonify({'error': 'posted_by is required'}), 400
        
        success = accounting_service.post_journal_entry(journal_id, posted_by)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Journal entry posted successfully'
            })
        else:
            return jsonify({'error': 'Failed to post journal entry'}), 500
        
    except Exception as e:
        logger.error(f"Error posting journal entry: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/journal-entries/<int:journal_id>/reverse', methods=['POST'])
@token_required
@require_module_access('accounting')
def reverse_journal_entry(journal_id):
    """Reverse a journal entry"""
    try:
        data = request.get_json()
        reversal_reason = data.get('reversal_reason', '')
        created_by = data.get('created_by')
        
        if not created_by:
            return jsonify({'error': 'created_by is required'}), 400
        
        reversal_id = accounting_service.reverse_journal_entry(journal_id, reversal_reason, created_by)
        
        return jsonify({
            'success': True,
            'message': 'Journal entry reversed successfully',
            'reversal_id': reversal_id
        })
        
    except Exception as e:
        logger.error(f"Error reversing journal entry: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# VENDORS ENDPOINTS
# ============================================================================

@accounting_bp.route('/vendors', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_vendors():
    """Get vendors"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        vendors = ap_service.get_active_vendors(tenant_id)
        
        return jsonify({
            'success': True,
            'data': vendors,
            'count': len(vendors)
        })
        
    except Exception as e:
        logger.error(f"Error getting vendors: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/vendors', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_vendor():
    """Create a new vendor"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        vendor_id = ap_service.create_vendor(data)
        
        return jsonify({
            'success': True,
            'message': 'Vendor created successfully',
            'vendor_id': vendor_id
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating vendor: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# CUSTOMERS ENDPOINTS
# ============================================================================

@accounting_bp.route('/customers', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_customers():
    """Get customers"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        customers = ar_service.get_active_customers(tenant_id)
        
        return jsonify({
            'success': True,
            'data': customers,
            'count': len(customers)
        })
        
    except Exception as e:
        logger.error(f"Error getting customers: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/customers', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_customer():
    """Create a new customer"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        customer_id = ar_service.create_customer(data)

        return jsonify({
            'success': True,
            'message': 'Customer created successfully',
            'customer_id': customer_id
        }), 201

    except Exception as e:
        logger.error(f"Error creating customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/customers/<int:customer_id>', methods=['PUT'])
@token_required
@require_module_access('accounting')
def update_customer(customer_id):
    """Update a customer"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        result = ar_service.update_customer(customer_id, data)
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"Error updating customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# SALES INVOICES ENDPOINTS
# ============================================================================

@accounting_bp.route('/sales-invoices', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_sales_invoices():
    """Get sales invoices"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        invoices = ar_service.get_sales_invoices(tenant_id)
        return jsonify({
            'success': True,
            'invoices': invoices
        })

    except Exception as e:
        logger.error(f"Error getting sales invoices: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/sales-invoices', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_sales_invoice():
    """Create a new sales invoice"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        result = ar_service.create_sales_invoice(data)
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"Error creating sales invoice: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/sales-invoices/<int:invoice_id>', methods=['PUT'])
@token_required
@require_module_access('accounting')
def update_sales_invoice(invoice_id):
    """Update a sales invoice"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        result = ar_service.update_sales_invoice(invoice_id, data)
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"Error updating sales invoice: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# CUSTOMER PAYMENTS ENDPOINTS
# ============================================================================

@accounting_bp.route('/customer-payments', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_customer_payments():
    """Get customer payments"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        payments = ar_service.get_customer_payments(tenant_id)
        return jsonify({
            'success': True,
            'payments': payments
        })

    except Exception as e:
        logger.error(f"Error getting customer payments: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/customer-payments', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_customer_payment():
    """Create a new customer payment"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        result = ar_service.create_customer_payment(data)
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"Error creating customer payment: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# REPORTS ENDPOINTS
# ============================================================================

@accounting_bp.route('/reports/aged-payables', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_aged_payables_report():
    """Get aged payables report"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        as_of_date = request.args.get('as_of_date')
        
        report_data = ap_service.get_aged_payables_report(tenant_id, as_of_date)
        
        return jsonify({
            'success': True,
            'data': report_data,
            'count': len(report_data),
            'as_of_date': as_of_date or date.today().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error generating aged payables report: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/aged-receivables', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_aged_receivables_report():
    """Get aged receivables report"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400
        
        as_of_date = request.args.get('as_of_date')
        
        report_data = ar_service.get_aged_receivables_report(tenant_id, as_of_date)
        
        return jsonify({
            'success': True,
            'data': report_data,
            'count': len(report_data),
            'as_of_date': as_of_date or date.today().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error generating aged receivables report: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/aged-receivables', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_aged_receivables():
    """Get aged receivables (alias for reports/aged-receivables)"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        as_of_date = request.args.get('as_of_date')

        report_data = ar_service.get_aged_receivables_report(tenant_id, as_of_date)

        return jsonify({
            'success': True,
            'data': report_data,
            'count': len(report_data),
            'as_of_date': as_of_date or date.today().isoformat()
        })

    except Exception as e:
        logger.error(f"Error generating aged receivables: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/trial-balance', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_trial_balance():
    """Get trial balance report"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        as_of_date = request.args.get('as_of_date')

        report_data = reporting_service.generate_trial_balance(tenant_id, as_of_date)

        return jsonify({
            'success': True,
            'data': report_data
        })

    except Exception as e:
        logger.error(f"Error generating trial balance: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/profit-loss', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_profit_loss():
    """Get profit & loss statement"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if not start_date or not end_date:
            return jsonify({'error': 'start_date and end_date are required'}), 400

        report_data = reporting_service.generate_profit_loss(tenant_id, start_date, end_date)

        return jsonify({
            'success': True,
            'data': report_data
        })

    except Exception as e:
        logger.error(f"Error generating profit & loss: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/balance-sheet', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_balance_sheet():
    """Get balance sheet"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        as_of_date = request.args.get('as_of_date')

        report_data = reporting_service.generate_balance_sheet(tenant_id, as_of_date)

        return jsonify({
            'success': True,
            'data': report_data
        })

    except Exception as e:
        logger.error(f"Error generating balance sheet: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/general-ledger', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_general_ledger():
    """Get general ledger report"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        account_id = request.args.get('account_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        report_data = reporting_service.generate_general_ledger(tenant_id, account_id, start_date, end_date)

        return jsonify({
            'success': True,
            'data': report_data
        })

    except Exception as e:
        logger.error(f"Error generating general ledger: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/reports/cash-flow', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_cash_flow_statement():
    """Get cash flow statement"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        result = reporting_service.generate_cash_flow_statement(tenant_id, start_date, end_date)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error generating cash flow statement: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# INTEGRATION ENDPOINTS
# ============================================================================

@accounting_bp.route('/integration/sync-billing', methods=['POST'])
@token_required
@require_module_access('accounting')
def sync_billing_to_accounting():
    """Sync existing billing data to accounting system"""
    try:
        data = request.get_json()
        tenant_id = data.get('tenant_id')
        created_by = data.get('created_by')

        if not tenant_id or not created_by:
            return jsonify({'error': 'tenant_id and created_by are required'}), 400

        results = integration_service.sync_billing_to_accounting(tenant_id, created_by)

        return jsonify({
            'success': True,
            'message': 'Billing data synced successfully',
            'data': results
        })

    except Exception as e:
        logger.error(f"Error syncing billing data: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/integration/sync-procurement', methods=['POST'])
@token_required
@require_module_access('accounting')
def sync_procurement_to_accounting():
    """Sync existing procurement data to accounting system"""
    try:
        data = request.get_json()
        tenant_id = data.get('tenant_id')
        created_by = data.get('created_by')

        if not tenant_id or not created_by:
            return jsonify({'error': 'tenant_id and created_by are required'}), 400

        results = integration_service.sync_procurement_to_accounting(tenant_id, created_by)

        return jsonify({
            'success': True,
            'message': 'Procurement data synced successfully',
            'data': results
        })

    except Exception as e:
        logger.error(f"Error syncing procurement data: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/integration/complete-setup', methods=['POST'])
@token_required
@require_module_access('accounting')
def complete_accounting_setup():
    """Complete accounting setup for a tenant"""
    try:
        data = request.get_json()
        tenant_id = data.get('tenant_id')
        created_by = data.get('created_by')

        if not tenant_id or not created_by:
            return jsonify({'error': 'tenant_id and created_by are required'}), 400

        results = integration_service.complete_accounting_setup(tenant_id, created_by)

        return jsonify({
            'success': True,
            'message': 'Accounting setup completed successfully',
            'data': results
        })

    except Exception as e:
        logger.error(f"Error completing accounting setup: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# PURCHASE INVOICES (ACCOUNTS PAYABLE)
# ============================================================================

@accounting_bp.route('/purchase-invoices', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_purchase_invoices():
    """Get purchase invoices"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        status = request.args.get('status')
        vendor_id = request.args.get('vendor_id', type=int)

        # Build query conditions
        conditions = ['pi.tenant_id = ?']
        params = [tenant_id]

        if status:
            conditions.append('pi.status = ?')
            params.append(status)

        if vendor_id:
            conditions.append('pi.vendor_id = ?')
            params.append(vendor_id)

        where_clause = ' AND '.join(conditions)

        # Get total count
        count_query = f"SELECT COUNT(*) as count FROM purchase_invoices pi WHERE {where_clause}"
        count_result = ap_service.db.execute_query(count_query, params)
        total_count = count_result[0]['count'] if count_result else 0

        # Get invoices with pagination
        offset = (page - 1) * per_page
        query = f"""
            SELECT pi.*, v.vendor_name, v.vendor_code
            FROM purchase_invoices pi
            LEFT JOIN vendors v ON pi.vendor_id = v.id
            WHERE {where_clause}
            ORDER BY pi.created_at DESC
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])

        invoices = ap_service.db.execute_query(query, params)

        return jsonify({
            'success': True,
            'data': invoices,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            }
        })

    except Exception as e:
        logger.error(f"Error getting purchase invoices: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/purchase-invoices', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_purchase_invoice():
    """Create a new purchase invoice"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        invoice_data = data.get('invoice_data', {})
        line_items = data.get('line_items', [])

        if not line_items:
            return jsonify({'error': 'Line items are required'}), 400

        invoice_id = ap_service.create_purchase_invoice(invoice_data, line_items)

        return jsonify({
            'success': True,
            'message': 'Purchase invoice created successfully',
            'invoice_id': invoice_id
        }), 201

    except Exception as e:
        logger.error(f"Error creating purchase invoice: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/purchase-invoices/<int:invoice_id>/approve', methods=['POST'])
@token_required
@require_module_access('accounting')
def approve_purchase_invoice(invoice_id):
    """Approve a purchase invoice"""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by')

        if not approved_by:
            return jsonify({'error': 'approved_by is required'}), 400

        success = ap_service.approve_purchase_invoice(invoice_id, approved_by)

        if success:
            return jsonify({
                'success': True,
                'message': 'Purchase invoice approved successfully'
            })
        else:
            return jsonify({'error': 'Failed to approve purchase invoice'}), 500

    except Exception as e:
        logger.error(f"Error approving purchase invoice: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# HEALTH CHECK
# ============================================================================

@accounting_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'Accounting module is running',
        'timestamp': datetime.now().isoformat()
    })

# ============================================================================
# TAX MANAGEMENT ENDPOINTS
# ============================================================================

@accounting_bp.route('/tax-types', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_tax_types():
    """Get all tax types"""
    try:
        user = request.current_user
        tenant_id = user.get('tenant_id')

        tax_service = TaxService()
        tax_types = tax_service.get_tax_types(tenant_id)

        return jsonify({
            'success': True,
            'data': tax_types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/tax/calculate-gst', methods=['POST'])
@token_required
@require_module_access('accounting')
def calculate_gst():
    """Calculate GST on amount"""
    try:
        data = request.get_json()
        amount = float(data.get('amount', 0))
        gst_rate = float(data.get('gst_rate', 18))
        include_tax = data.get('include_tax', False)

        tax_service = TaxService()
        result = tax_service.calculate_gst(amount, gst_rate, include_tax)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/tax/calculate-tds', methods=['POST'])
@token_required
@require_module_access('accounting')
def calculate_tds():
    """Calculate TDS on amount"""
    try:
        data = request.get_json()
        amount = float(data.get('amount', 0))
        tds_rate = float(data.get('tds_rate', 10))
        threshold = data.get('threshold')

        tax_service = TaxService()
        result = tax_service.calculate_tds(amount, tds_rate, threshold)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/tax/calculate-composite', methods=['POST'])
@token_required
@require_module_access('accounting')
def calculate_composite_tax():
    """Calculate multiple taxes on amount"""
    try:
        data = request.get_json()
        amount = float(data.get('amount', 0))
        tax_config = data.get('tax_config', [])

        tax_service = TaxService()
        result = tax_service.calculate_composite_tax(amount, tax_config)

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/tax/summary-report', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_tax_summary_report():
    """Get tax summary report"""
    try:
        user = request.current_user
        tenant_id = user.get('tenant_id')

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if not start_date or not end_date:
            return jsonify({
                'success': False,
                'error': 'start_date and end_date are required'
            }), 400

        tax_service = TaxService()
        report = tax_service.get_tax_summary_report(tenant_id, start_date, end_date)

        return jsonify({
            'success': True,
            'data': report
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/tax/gst-dashboard', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_gst_dashboard():
    """Get GST dashboard data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        dashboard_data = tax_service.get_gst_dashboard(tenant_id, start_date, end_date)

        return jsonify(dashboard_data)

    except Exception as e:
        logger.error(f"Error getting GST dashboard: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/tax/gst-returns', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_gst_returns():
    """Get GST returns data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        returns_data = tax_service.get_gst_returns_data(tenant_id, start_date, end_date)

        return jsonify(returns_data)

    except Exception as e:
        logger.error(f"Error getting GST returns: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/tax/tds-data', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_tds_data():
    """Get TDS data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        tds_data = tax_service.get_tds_data(tenant_id, start_date, end_date)

        return jsonify(tds_data)

    except Exception as e:
        logger.error(f"Error getting TDS data: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/tax/tcs-data', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_tcs_data():
    """Get TCS data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        tcs_data = tax_service.get_tcs_data(tenant_id, start_date, end_date)

        return jsonify(tcs_data)

    except Exception as e:
        logger.error(f"Error getting TCS data: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# INVENTORY ACCOUNTING ENDPOINTS
# ============================================================================

@accounting_bp.route('/inventory/valuation-methods', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_inventory_valuation_methods():
    """Get inventory valuation methods"""
    try:
        inventory_service = InventoryAccountingService()
        methods = inventory_service.get_inventory_valuation_methods()

        return jsonify({
            'success': True,
            'data': methods
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/inventory/calculate-cost', methods=['POST'])
@token_required
@require_module_access('accounting')
def calculate_inventory_cost():
    """Calculate inventory cost using specified method"""
    try:
        data = request.get_json()
        user = request.current_user
        tenant_id = user.get('tenant_id')

        item_id = data.get('item_id')
        quantity = data.get('quantity')
        method = data.get('method', 'FIFO')

        if not item_id or not quantity:
            return jsonify({
                'success': False,
                'error': 'item_id and quantity are required'
            }), 400

        inventory_service = InventoryAccountingService()

        if method == 'FIFO':
            result = inventory_service.calculate_fifo_cost(item_id, quantity, tenant_id)
        elif method == 'LIFO':
            result = inventory_service.calculate_lifo_cost(item_id, quantity, tenant_id)
        elif method == 'AVERAGE':
            result = inventory_service.calculate_average_cost(item_id, quantity, tenant_id)
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid method. Use FIFO, LIFO, or AVERAGE'
            }), 400

        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/inventory/valuation-report', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_inventory_valuation_report():
    """Get inventory valuation report"""
    try:
        user = request.current_user
        tenant_id = user.get('tenant_id')

        as_of_date = request.args.get('as_of_date')
        method = request.args.get('method', 'FIFO')

        inventory_service = InventoryAccountingService()
        report = inventory_service.get_inventory_valuation_report(tenant_id, as_of_date, method)

        return jsonify({
            'success': True,
            'data': report
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/inventory/transactions', methods=['POST'])
@token_required
@require_module_access('accounting')
def create_inventory_transaction():
    """Create inventory transaction with accounting entries"""
    try:
        data = request.get_json()
        user = request.current_user

        # Add user and tenant info
        data['created_by'] = user.get('id')
        data['tenant_id'] = user.get('tenant_id')

        inventory_service = InventoryAccountingService()
        transaction_id = inventory_service.create_inventory_transaction(data)

        return jsonify({
            'success': True,
            'message': 'Inventory transaction created successfully',
            'transaction_id': transaction_id
        }), 201
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@accounting_bp.route('/inventory/valuation', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_inventory_valuation():
    """Get inventory valuation data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        costing_method = request.args.get('costing_method', 'FIFO')
        as_of_date = request.args.get('as_of_date')

        valuation_data = inventory_service.get_inventory_valuation(tenant_id, costing_method, as_of_date)

        return jsonify(valuation_data)

    except Exception as e:
        logger.error(f"Error getting inventory valuation: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/inventory/movements', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_inventory_movements():
    """Get inventory movements data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        item_id = request.args.get('item_id', type=int)

        movements_data = inventory_service.get_inventory_movements(tenant_id, start_date, end_date, item_id)

        return jsonify(movements_data)

    except Exception as e:
        logger.error(f"Error getting inventory movements: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/inventory/cogs-analysis', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_cogs_analysis():
    """Get COGS analysis data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        costing_method = request.args.get('costing_method', 'FIFO')

        cogs_data = inventory_service.get_cogs_analysis(tenant_id, start_date, end_date, costing_method)

        return jsonify(cogs_data)

    except Exception as e:
        logger.error(f"Error getting COGS analysis: {str(e)}")
        return jsonify({'error': str(e)}), 500

@accounting_bp.route('/inventory/adjustments', methods=['GET'])
@token_required
@require_module_access('accounting')
def get_inventory_adjustments():
    """Get inventory adjustments data"""
    try:
        tenant_id = request.args.get('tenant_id', type=int)
        if not tenant_id:
            return jsonify({'error': 'tenant_id is required'}), 400

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        adjustments_data = inventory_service.get_inventory_adjustments(tenant_id, start_date, end_date)

        return jsonify(adjustments_data)

    except Exception as e:
        logger.error(f"Error getting inventory adjustments: {str(e)}")
        return jsonify({'error': str(e)}), 500
