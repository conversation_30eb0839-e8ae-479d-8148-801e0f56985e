#!/usr/bin/env python3
"""
Migration script to transfer procurement data from JSON files to SQL database tables.

This script migrates data from the following JSON files to their corresponding SQL tables:
- purchase_requests.json -> purchase_requests + purchase_request_items
- purchase_orders.json -> purchase_orders + purchase_order_items  
- proforma_invoices.json -> proforma_invoices + proforma_invoice_items
- delivery_notes.json -> delivery_notes + delivery_note_items
- payment_transactions.json -> payment_transactions
- inventory_transfers.json -> inventory_transfers + inventory_transfer_items

Usage:
    python migrate_procurement_data.py [--dry-run] [--force]
    
Options:
    --dry-run    Show what would be migrated without actually doing it
    --force      Force migration even if tables already contain data
"""

import os
import sys
import json
import argparse
from datetime import datetime
from database_manager import db_manager

def load_json_file(filename):
    """Load data from a JSON file"""
    filepath = os.path.join('data', filename)
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
            return data if isinstance(data, list) else []
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Warning: Could not load {filename}: {e}")
        return []

def migrate_purchase_requests(data, dry_run=False):
    """Migrate purchase requests and their items"""
    if not data:
        print("No purchase requests to migrate")
        return 0, 0
    
    requests_migrated = 0
    items_migrated = 0
    
    for request in data:
        if dry_run:
            print(f"Would migrate purchase request: {request.get('request_number', 'N/A')}")
            items_migrated += len(request.get('items', []))
        else:
            # Insert main request record
            request_data = {
                'request_number': request.get('request_number'),
                'priority': request.get('priority', 'medium'),
                'required_date': request.get('required_date'),
                'storeroom_id': request.get('storeroom_id'),
                'notes': request.get('notes'),
                'status': request.get('status', 'pending'),
                'total_estimated_amount': request.get('total_estimated_amount', 0),
                'created_by': request.get('created_by', 1),
                'tenant_id': request.get('tenant_id', 1)
            }
            
            request_id = db_manager.insert_record('purchase_requests', request_data)
            
            # Insert items
            for item in request.get('items', []):
                item_data = {
                    'purchase_request_id': request_id,
                    'item_name': item.get('item_name'),
                    'item_description': item.get('item_description'),
                    'requested_quantity': item.get('requested_quantity'),
                    'unit': item.get('unit'),
                    'estimated_unit_price': item.get('estimated_unit_price', 0),
                    'notes': item.get('notes')
                }
                db_manager.insert_record('purchase_request_items', item_data)
                items_migrated += 1
        
        requests_migrated += 1
    
    return requests_migrated, items_migrated

def migrate_purchase_orders(data, dry_run=False):
    """Migrate purchase orders and their items"""
    if not data:
        print("No purchase orders to migrate")
        return 0, 0
    
    orders_migrated = 0
    items_migrated = 0
    
    for order in data:
        if dry_run:
            print(f"Would migrate purchase order: {order.get('order_number', 'N/A')}")
            items_migrated += len(order.get('items', []))
        else:
            # Insert main order record
            order_data = {
                'order_number': order.get('order_number'),
                'purchase_request_id': order.get('purchase_request_id'),
                'supplier_name': order.get('supplier_name'),
                'supplier_contact': order.get('supplier_contact'),
                'order_date': order.get('order_date'),
                'expected_delivery_date': order.get('expected_delivery_date'),
                'status': order.get('status', 'pending'),
                'total_amount': order.get('total_amount', 0),
                'notes': order.get('notes'),
                'created_by': order.get('created_by', 1),
                'tenant_id': order.get('tenant_id', 1)
            }
            
            order_id = db_manager.insert_record('purchase_orders', order_data)
            
            # Insert items
            for item in order.get('items', []):
                item_data = {
                    'purchase_order_id': order_id,
                    'item_name': item.get('item_name'),
                    'item_description': item.get('item_description'),
                    'ordered_quantity': item.get('ordered_quantity'),
                    'unit': item.get('unit'),
                    'unit_price': item.get('unit_price', 0),
                    'total_price': item.get('total_price', 0),
                    'received_quantity': item.get('received_quantity', 0),
                    'notes': item.get('notes')
                }
                db_manager.insert_record('purchase_order_items', item_data)
                items_migrated += 1
        
        orders_migrated += 1
    
    return orders_migrated, items_migrated

def migrate_simple_table(data, table_name, dry_run=False):
    """Migrate data to a simple table (no child items)"""
    if not data:
        print(f"No {table_name} to migrate")
        return 0
    
    migrated = 0
    
    for record in data:
        if dry_run:
            print(f"Would migrate {table_name} record: {record.get('id', 'N/A')}")
        else:
            db_manager.insert_record(table_name, record)
        migrated += 1
    
    return migrated

def check_table_data(table_name):
    """Check if a table already contains data"""
    try:
        result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
        return result[0]['count'] if result else 0
    except Exception:
        return 0

def main():
    parser = argparse.ArgumentParser(description='Migrate procurement data from JSON to SQL')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be migrated without doing it')
    parser.add_argument('--force', action='store_true', help='Force migration even if tables contain data')
    
    args = parser.parse_args()
    
    print("Procurement Data Migration Script")
    print("=" * 40)
    
    if args.dry_run:
        print("DRY RUN MODE - No actual changes will be made")
        print()
    
    # Check if tables already contain data
    tables_to_check = [
        'purchase_requests', 'purchase_orders', 'proforma_invoices',
        'delivery_notes', 'payment_transactions', 'inventory_transfers'
    ]
    
    has_data = False
    for table in tables_to_check:
        count = check_table_data(table)
        if count > 0:
            print(f"Warning: {table} already contains {count} records")
            has_data = True
    
    if has_data and not args.force and not args.dry_run:
        print("\nTables already contain data. Use --force to proceed anyway or --dry-run to see what would be migrated.")
        return 1
    
    print()
    
    # Load JSON data
    purchase_requests = load_json_file('purchase_requests.json')
    purchase_orders = load_json_file('purchase_orders.json')
    proforma_invoices = load_json_file('proforma_invoices.json')
    delivery_notes = load_json_file('delivery_notes.json')
    payment_transactions = load_json_file('payment_transactions.json')
    inventory_transfers = load_json_file('inventory_transfers.json')
    
    # Migrate data
    total_migrated = 0
    
    try:
        # Purchase Requests
        req_count, req_items = migrate_purchase_requests(purchase_requests, args.dry_run)
        print(f"Purchase Requests: {req_count} records, {req_items} items")
        total_migrated += req_count
        
        # Purchase Orders
        order_count, order_items = migrate_purchase_orders(purchase_orders, args.dry_run)
        print(f"Purchase Orders: {order_count} records, {order_items} items")
        total_migrated += order_count
        
        # Simple tables
        pi_count = migrate_simple_table(proforma_invoices, 'proforma_invoices', args.dry_run)
        print(f"Proforma Invoices: {pi_count} records")
        total_migrated += pi_count
        
        dn_count = migrate_simple_table(delivery_notes, 'delivery_notes', args.dry_run)
        print(f"Delivery Notes: {dn_count} records")
        total_migrated += dn_count
        
        pt_count = migrate_simple_table(payment_transactions, 'payment_transactions', args.dry_run)
        print(f"Payment Transactions: {pt_count} records")
        total_migrated += pt_count
        
        it_count = migrate_simple_table(inventory_transfers, 'inventory_transfers', args.dry_run)
        print(f"Inventory Transfers: {it_count} records")
        total_migrated += it_count
        
        print(f"\nTotal records migrated: {total_migrated}")
        
        if not args.dry_run and total_migrated > 0:
            print(f"\nMigration completed successfully at {datetime.now()}")
        elif args.dry_run:
            print(f"\nDry run completed. {total_migrated} records would be migrated.")
        else:
            print("\nNo data found to migrate.")
            
    except Exception as e:
        print(f"\nError during migration: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
