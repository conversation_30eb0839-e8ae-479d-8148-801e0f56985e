-- Migration script for Delivery Note Status Enhancement
-- This script updates existing delivery_notes and delivery_note_items tables
-- to support comprehensive status management and inventory integration

-- 1. Add new status columns to delivery_notes table
ALTER TABLE delivery_notes ADD COLUMN hub_status TEXT CHECK (hub_status IN (
    'prepared', 'dispatched', 'delivered', 'returned', 
    'reprocessed', 'cancelled', 'partially_delivered', 'in_transit'
)) DEFAULT 'prepared';

ALTER TABLE delivery_notes ADD COLUMN franchise_status TEXT CHECK (franchise_status IN (
    'pending_receipt', 'received', 'confirmed', 'rejected', 'partially_accepted'
)) DEFAULT 'pending_receipt';

-- 2. Add new tracking fields to delivery_notes table
ALTER TABLE delivery_notes ADD COLUMN confirmed_at TIMESTAMP;
ALTER TABLE delivery_notes ADD COLUMN confirmed_by INTEGER;
ALTER TABLE delivery_notes ADD COLUMN rejected_at TIMESTAMP;
ALTER TABLE delivery_notes ADD COLUMN rejected_by INTEGER;
ALTER TABLE delivery_notes ADD COLUMN rejection_reason TEXT;
ALTER TABLE delivery_notes ADD COLUMN returned_at TIMESTAMP;
ALTER TABLE delivery_notes ADD COLUMN returned_by INTEGER;
ALTER TABLE delivery_notes ADD COLUMN return_reason TEXT;
ALTER TABLE delivery_notes ADD COLUMN reprocessed_at TIMESTAMP;
ALTER TABLE delivery_notes ADD COLUMN reprocessed_by INTEGER;

-- 3. Add foreign key constraints for new user references
-- Note: SQLite doesn't support adding foreign key constraints to existing tables
-- These will be enforced in the application logic

-- 4. Update existing delivery_notes to set initial hub_status and franchise_status
UPDATE delivery_notes SET 
    hub_status = CASE 
        WHEN status = 'prepared' THEN 'prepared'
        WHEN status = 'dispatched' THEN 'dispatched'
        WHEN status = 'in_transit' THEN 'in_transit'
        WHEN status = 'delivered' THEN 'delivered'
        WHEN status = 'cancelled' THEN 'cancelled'
        ELSE 'prepared'
    END,
    franchise_status = CASE 
        WHEN status = 'prepared' THEN 'pending_receipt'
        WHEN status = 'dispatched' THEN 'pending_receipt'
        WHEN status = 'in_transit' THEN 'pending_receipt'
        WHEN status = 'delivered' THEN 'received'
        WHEN status = 'cancelled' THEN 'pending_receipt'
        ELSE 'pending_receipt'
    END
WHERE hub_status IS NULL OR franchise_status IS NULL;

-- 5. Add new columns to delivery_note_items table for inventory integration
ALTER TABLE delivery_note_items ADD COLUMN inventory_item_id INTEGER;
ALTER TABLE delivery_note_items ADD COLUMN storeroom_id INTEGER;
ALTER TABLE delivery_note_items ADD COLUMN sku TEXT;
ALTER TABLE delivery_note_items ADD COLUMN category TEXT;
ALTER TABLE delivery_note_items ADD COLUMN requested_quantity INTEGER;
ALTER TABLE delivery_note_items ADD COLUMN delivered_quantity INTEGER DEFAULT 0;
ALTER TABLE delivery_note_items ADD COLUMN accepted_quantity INTEGER DEFAULT 0;
ALTER TABLE delivery_note_items ADD COLUMN rejected_quantity INTEGER DEFAULT 0;
ALTER TABLE delivery_note_items ADD COLUMN available_stock INTEGER DEFAULT 0;
ALTER TABLE delivery_note_items ADD COLUMN stock_validated_at TIMESTAMP;
ALTER TABLE delivery_note_items ADD COLUMN item_status TEXT CHECK (item_status IN (
    'pending', 'dispatched', 'delivered', 'accepted', 'rejected', 'partially_accepted'
)) DEFAULT 'pending';
ALTER TABLE delivery_note_items ADD COLUMN batch_number TEXT;
ALTER TABLE delivery_note_items ADD COLUMN expiry_date DATE;
ALTER TABLE delivery_note_items ADD COLUMN rejection_reason TEXT;

-- 6. Update existing delivery_note_items to set initial values
UPDATE delivery_note_items SET 
    requested_quantity = quantity,
    delivered_quantity = quantity,
    item_status = 'pending'
WHERE requested_quantity IS NULL;

-- 7. Create delivery_note_status_history table
CREATE TABLE IF NOT EXISTS delivery_note_status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_note_id INTEGER NOT NULL,
    status_type TEXT CHECK (status_type IN ('hub_status', 'franchise_status', 'legacy_status')) NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_by INTEGER NOT NULL,
    change_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id)
);

-- 8. Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_delivery_notes_hub_status ON delivery_notes(hub_status);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_franchise_status ON delivery_notes(franchise_status);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_inventory ON delivery_note_items(inventory_item_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_storeroom ON delivery_note_items(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_status ON delivery_note_items(item_status);
CREATE INDEX IF NOT EXISTS idx_delivery_note_status_history_dn ON delivery_note_status_history(delivery_note_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_status_history_type ON delivery_note_status_history(status_type);

-- 9. Create initial status history records for existing delivery notes
INSERT INTO delivery_note_status_history (delivery_note_id, status_type, new_status, changed_by, change_reason, created_at)
SELECT 
    id,
    'legacy_status',
    status,
    COALESCE(created_by, 1),
    'Initial status from migration',
    created_at
FROM delivery_notes
WHERE id NOT IN (SELECT DISTINCT delivery_note_id FROM delivery_note_status_history WHERE status_type = 'legacy_status');

-- 10. Update the status check constraint to include new statuses
-- Note: SQLite doesn't support modifying check constraints
-- The new constraint will be enforced in the application logic

COMMIT;
