#!/usr/bin/env python3
"""
Test script to check which routes are registered
"""
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_flask_routes():
    """Test which routes are registered in Flask"""
    try:
        print("🔍 Testing Flask route registration...")
        
        # Import Flask app
        from app import app
        
        # Get all registered routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # Filter procurement routes
        procurement_routes = [r for r in routes if 'procurement' in r['rule']]
        
        print(f"📊 Total routes registered: {len(routes)}")
        print(f"📊 Procurement routes found: {len(procurement_routes)}")
        
        print("\n📄 Procurement routes:")
        for route in procurement_routes:
            methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
            print(f"  {route['rule']} [{', '.join(methods)}] -> {route['endpoint']}")
        
        # Check specifically for delivery note routes
        delivery_routes = [r for r in procurement_routes if 'delivery-notes' in r['rule']]
        print(f"\n📄 Delivery note routes ({len(delivery_routes)}):")
        for route in delivery_routes:
            methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
            print(f"  {route['rule']} [{', '.join(methods)}] -> {route['endpoint']}")
        
        # Check for confirm route specifically
        confirm_routes = [r for r in delivery_routes if 'confirm' in r['rule']]
        print(f"\n📄 Confirm routes ({len(confirm_routes)}):")
        for route in confirm_routes:
            methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
            print(f"  {route['rule']} [{', '.join(methods)}] -> {route['endpoint']}")
        
        return len(confirm_routes) > 0
        
    except Exception as e:
        print(f"❌ Error testing routes: {e}")
        return False

def test_blueprint_import():
    """Test if the procurement blueprint imports correctly"""
    try:
        print("\n🔍 Testing blueprint import...")
        
        from routes.procurement_routes_db import procurement_bp
        print("✅ procurement_routes_db imported successfully")
        
        # Check blueprint routes
        blueprint_routes = []
        for rule in procurement_bp.deferred_functions:
            print(f"📄 Deferred function: {rule}")
        
        return True
        
    except Exception as e:
        print(f"❌ Blueprint import error: {e}")
        return False

def test_decorators():
    """Test if decorators are working"""
    try:
        print("\n🔍 Testing decorators...")
        
        from utils import token_required, require_module_access
        print("✅ Decorators imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Decorator import error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Routes Debug Test")
    print("=" * 50)
    
    tests = [
        ("Blueprint Import", test_blueprint_import),
        ("Decorators", test_decorators),
        ("Flask Routes", test_flask_routes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    # Check if confirm route is registered
    if results[2][1]:  # Flask routes test passed
        print("\n💡 If confirm route is not found:")
        print("  1. Check for import errors in procurement_routes_db.py")
        print("  2. Check if decorators are working correctly")
        print("  3. Restart the Flask server")
    
    return all(success for _, success in results)

if __name__ == "__main__":
    main()
