[{"timestamp": "2025-09-01T11:34:00.616928", "table": "tenants", "action": "migrated", "count": 12, "error": null}, {"timestamp": "2025-09-01T11:34:00.617518", "table": "users", "action": "migrated", "count": 16, "error": null}, {"timestamp": "2025-09-01T11:34:00.618883", "table": "patients", "action": "migrated", "count": 89, "error": null}, {"timestamp": "2025-09-01T11:34:00.624409", "table": "departments", "action": "migrated", "count": 1004, "error": null}, {"timestamp": "2025-09-01T11:34:00.637304", "table": "test_master", "action": "migrated", "count": 299, "error": null}, {"timestamp": "2025-09-01T11:34:00.638817", "table": "samples", "action": "migrated", "count": 100, "error": null}, {"timestamp": "2025-09-01T11:34:00.643832", "table": "billings", "action": "migrated", "count": 109, "error": null}, {"timestamp": "2025-09-01T11:34:00.646602", "table": "results", "action": "migrated", "count": 212, "error": null}]