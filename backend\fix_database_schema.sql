-- Fix Database Schema Issues for Accounting Module
-- Add only the missing columns that are causing 500 errors

-- 1. Add missing transaction_date column to inventory_transactions table
ALTER TABLE inventory_transactions ADD COLUMN transaction_date DATE;

-- 2. Update existing records to have default values
UPDATE customers SET address = 'Not Specified' WHERE address IS NULL OR address = '';
UPDATE inventory_transactions SET transaction_date = date('now') WHERE transaction_date IS NULL;
