import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, Card, Badge, <PERSON><PERSON>, Alert, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faShoppingCart, 
  faArrowLeft, 
  faCalendar, 
  faUser, 
  faBoxes,
  faExclamationTriangle,
  faEdit,
  faCheck,
  faTimes,
  faBuilding,
  faWarehouse,
  faTruck
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';

const PurchaseOrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [purchaseOrder, setPurchaseOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPurchaseOrder();
  }, [id]);

  const loadPurchaseOrder = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getPurchaseOrder(id);
      setPurchaseOrder(response.data);
    } catch (err) {
      console.error('Error loading purchase order:', err);
      setError(err.response?.data?.message || 'Failed to load purchase order');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'secondary';
      case 'sent': return 'warning';
      case 'confirmed': return 'info';
      case 'received': return 'success';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading purchase order details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/orders')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Purchase Orders
        </Button>
      </Container>
    );
  }

  if (!purchaseOrder) {
    return (
      <Container fluid className="py-4">
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Purchase order not found
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/orders')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Purchase Orders
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/procurement/orders')}
            className="mb-2"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Purchase Orders
          </Button>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faShoppingCart} className="me-2 text-primary" />
            Purchase Order Details
          </h2>
          <p className="text-muted mb-0">View purchase order information and items</p>
        </div>
        <div>
          <Badge bg={getStatusBadgeVariant(purchaseOrder.status)} className="fs-6">
            {purchaseOrder.status?.toUpperCase()}
          </Badge>
        </div>
      </div>

      <Row>
        {/* Purchase Order Information */}
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faShoppingCart} className="me-2" />
                Order Information
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">PO Number</label>
                    <div className="fw-semibold">{purchaseOrder.po_number}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Order Date
                    </label>
                    <div>{new Date(purchaseOrder.order_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faTruck} className="me-1" />
                      Expected Delivery
                    </label>
                    <div>
                      {purchaseOrder.expected_delivery_date 
                        ? new Date(purchaseOrder.expected_delivery_date).toLocaleDateString()
                        : 'Not specified'
                      }
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Supplier</label>
                    <div>{purchaseOrder.supplier_name || 'N/A'}</div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Status</label>
                    <div>
                      <Badge bg={getStatusBadgeVariant(purchaseOrder.status)}>
                        {purchaseOrder.status?.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Subtotal</label>
                    <div>₹{parseFloat(purchaseOrder.subtotal || 0).toLocaleString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Tax ({purchaseOrder.tax_rate || 0}%)</label>
                    <div>₹{parseFloat(purchaseOrder.tax_amount || 0).toLocaleString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Total Amount</label>
                    <div className="fw-semibold text-success fs-5">
                      ₹{parseFloat(purchaseOrder.total_amount || 0).toLocaleString()}
                    </div>
                  </div>
                </Col>
              </Row>
              
              {purchaseOrder.delivery_address && (
                <div className="mt-3">
                  <label className="form-label text-muted">Delivery Address</label>
                  <div className="bg-light p-3 rounded">{purchaseOrder.delivery_address}</div>
                </div>
              )}
              
              {purchaseOrder.payment_terms && (
                <div className="mt-3">
                  <label className="form-label text-muted">Payment Terms</label>
                  <div className="bg-light p-3 rounded">{purchaseOrder.payment_terms}</div>
                </div>
              )}
              
              {purchaseOrder.notes && (
                <div className="mt-3">
                  <label className="form-label text-muted">Notes</label>
                  <div className="bg-light p-3 rounded">{purchaseOrder.notes}</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary Card */}
        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faBoxes} className="me-2" />
                Summary
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Items:</span>
                <span className="fw-semibold">{purchaseOrder.items?.length || 0}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Quantity:</span>
                <span className="fw-semibold">
                  {purchaseOrder.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0}
                </span>
              </div>
              <hr />
              <div className="d-flex justify-content-between mb-2">
                <span>Subtotal:</span>
                <span>₹{parseFloat(purchaseOrder.subtotal || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Tax:</span>
                <span>₹{parseFloat(purchaseOrder.tax_amount || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between">
                <span className="fw-semibold">Total:</span>
                <span className="fw-semibold text-success">
                  ₹{parseFloat(purchaseOrder.total_amount || 0).toLocaleString()}
                </span>
              </div>
              
              {purchaseOrder.created_at && (
                <div className="mt-3 pt-3 border-top">
                  <small className="text-muted">
                    <FontAwesomeIcon icon={faUser} className="me-1" />
                    Created: {new Date(purchaseOrder.created_at).toLocaleString()}
                  </small>
                  {purchaseOrder.created_by_username && (
                    <div className="mt-1">
                      <small className="text-muted">
                        By: {purchaseOrder.created_by_username}
                      </small>
                    </div>
                  )}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Order Items */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faBoxes} className="me-2" />
            Order Items
          </h5>
        </Card.Header>
        <Card.Body className="p-0">
          {purchaseOrder.items && purchaseOrder.items.length > 0 ? (
            <div className="table-responsive">
              <Table striped hover className="mb-0">
                <thead className="table-dark">
                  <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                  </tr>
                </thead>
                <tbody>
                  {purchaseOrder.items.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="fw-semibold">{item.item_name}</td>
                      <td>{item.item_description || '-'}</td>
                      <td>{item.quantity}</td>
                      <td>{item.unit}</td>
                      <td>₹{parseFloat(item.unit_price || 0).toFixed(2)}</td>
                      <td className="fw-semibold">₹{parseFloat(item.total_price || 0).toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faBoxes} className="text-muted fa-2x mb-3" />
              <p className="text-muted">No items found for this purchase order</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default PurchaseOrderDetail;
