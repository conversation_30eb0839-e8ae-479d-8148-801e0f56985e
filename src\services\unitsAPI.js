import api from './api';

const unitsAPI = {
  // Get all units
  getUnits: (params = {}) => api.get('/units', { params }),

  // Get units by category
  getUnitsByCategory: () => api.get('/units/by-category'),

  // Get unit categories
  getUnitCategories: () => api.get('/units/categories'),

  // Get specific unit
  getUnit: (id) => api.get(`/units/${id}`),

  // Create new unit
  createUnit: (data) => api.post('/units', data),

  // Update unit
  updateUnit: (id, data) => api.put(`/units/${id}`, data),

  // Delete unit
  deleteUnit: (id) => api.delete(`/units/${id}`)
};

export default unitsAPI;
