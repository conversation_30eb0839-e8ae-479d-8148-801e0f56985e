/* Accounting Modules Styles */

.accounting-dashboard {
  padding: 20px;
}

.accounts-receivable,
.financial-reports,
.tax-management,
.inventory-accounting {
  padding: 20px;
}

/* Dashboard Cards */
.accounting-dashboard .card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 10px;
}

.accounting-dashboard .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accounting-dashboard .card-body {
  padding: 1.5rem;
}

.accounting-dashboard .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.accounting-dashboard .card-text {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

/* Tab Styles */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  color: #495057;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  color: #007bff;
  background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  background-color: transparent;
  border-color: #007bff;
  border-bottom-color: #007bff;
}

/* Table Styles */
.table {
  font-size: 0.9rem;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  padding: 0.75rem;
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: #f5f5f5;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Button Styles */
.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

/* Modal Styles */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  font-weight: 600;
  color: #495057;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* Form Styles */
.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
}

.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  padding: 1rem 1.25rem;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
}

/* Card Styles */
.card {
  border-radius: 10px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 1.25rem;
  border-radius: 10px 10px 0 0;
}

.card-body {
  padding: 1.25rem;
}

/* Financial Reports Specific */
.financial-reports .table-primary td {
  background-color: #cce5ff;
  font-weight: 600;
}

.financial-reports .table-success td {
  background-color: #d4edda;
  font-weight: 600;
}

.financial-reports .table-warning td {
  background-color: #fff3cd;
  font-weight: 600;
}

.financial-reports .table-danger td {
  background-color: #f8d7da;
  font-weight: 600;
}

.financial-reports .table-dark td {
  background-color: #343a40;
  color: white;
  font-weight: 700;
}

/* Tax Management Specific */
.tax-management .card.text-center {
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tax-management .card.text-center h4 {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Inventory Accounting Specific */
.inventory-accounting .card.text-center {
  border: none;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.inventory-accounting .card.text-center h4 {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Accounts Receivable Specific */
.accounts-receivable .table th {
  background-color: #e3f2fd;
  color: #1976d2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .accounting-dashboard,
  .accounts-receivable,
  .financial-reports,
  .tax-management,
  .inventory-accounting {
    padding: 10px;
  }
  
  .table-responsive {
    font-size: 0.8rem;
  }
  
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .card-body {
    padding: 1rem;
  }
}

/* Loading Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Status Indicators */
.status-active {
  color: #28a745;
}

.status-inactive {
  color: #6c757d;
}

.status-overdue {
  color: #dc3545;
}

.status-pending {
  color: #ffc107;
}

/* Print Styles */
@media print {
  .btn,
  .nav-tabs,
  .modal {
    display: none !important;
  }
  
  .card {
    border: 1px solid #000;
    box-shadow: none;
  }
  
  .table {
    font-size: 12px;
  }
}
