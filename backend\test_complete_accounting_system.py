#!/usr/bin/env python3
"""
Comprehensive Accounting System Test Suite
Tests all accounting functionality including frontend integration
"""

import requests
import json
import time
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:5002"
FRONTEND_URL = "http://localhost:3001"

def test_backend_health():
    """Test backend health check"""
    print("🔍 Testing Backend Health...")
    try:
        response = requests.get(f"{BASE_URL}/api/accounting/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data['message']}")
            return True
        else:
            print(f"❌ Backend Health Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Health Error: {e}")
        return False

def test_frontend_health():
    """Test frontend health check"""
    print("🔍 Testing Frontend Health...")
    try:
        response = requests.get(FRONTEND_URL)
        if response.status_code == 200:
            print("✅ Frontend is running")
            return True
        else:
            print(f"❌ Frontend Health Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend Health Error: {e}")
        return False

def get_auth_token():
    """Get authentication token for API calls"""
    print("🔍 Getting Authentication Token...")
    try:
        # Try to get token from login
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print("✅ Authentication successful")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_chart_of_accounts(token):
    """Test chart of accounts functionality"""
    print("🔍 Testing Chart of Accounts...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Get chart of accounts
        response = requests.get(f"{BASE_URL}/api/accounting/chart-of-accounts", 
                              headers=headers, params={"tenant_id": 1})
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('data', [])
            print(f"✅ Chart of Accounts: Found {len(accounts)} accounts")
            
            # Test specific account types
            asset_accounts = [acc for acc in accounts if acc['account_type'] == 'ASSET']
            liability_accounts = [acc for acc in accounts if acc['account_type'] == 'LIABILITY']
            revenue_accounts = [acc for acc in accounts if acc['account_type'] == 'REVENUE']
            
            print(f"   - Assets: {len(asset_accounts)}")
            print(f"   - Liabilities: {len(liability_accounts)}")
            print(f"   - Revenue: {len(revenue_accounts)}")
            
            return True
        else:
            print(f"❌ Chart of Accounts Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chart of Accounts Error: {e}")
        return False

def test_journal_entries(token):
    """Test journal entries functionality"""
    print("🔍 Testing Journal Entries...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Get journal entries
        response = requests.get(f"{BASE_URL}/api/accounting/journal-entries", 
                              headers=headers, params={"tenant_id": 1})
        
        if response.status_code == 200:
            data = response.json()
            entries = data.get('data', [])
            print(f"✅ Journal Entries: Found {len(entries)} entries")
            
            # Count by status
            draft_entries = [e for e in entries if e['status'] == 'DRAFT']
            posted_entries = [e for e in entries if e['status'] == 'POSTED']
            
            print(f"   - Draft: {len(draft_entries)}")
            print(f"   - Posted: {len(posted_entries)}")
            
            return True
        else:
            print(f"❌ Journal Entries Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Journal Entries Error: {e}")
        return False

def test_tax_calculations(token):
    """Test tax calculation functionality"""
    print("🔍 Testing Tax Calculations...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test GST calculation
        gst_data = {
            "amount": 1000,
            "gst_rate": 18,
            "include_tax": False
        }
        response = requests.post(f"{BASE_URL}/api/accounting/tax/calculate-gst", 
                               headers=headers, json=gst_data)
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('data', {})
            print(f"✅ GST Calculation: ₹{result.get('base_amount', 0)} + ₹{result.get('gst_amount', 0)} = ₹{result.get('total_amount', 0)}")
            
            # Test TDS calculation
            tds_data = {
                "amount": 10000,
                "tds_rate": 10
            }
            response = requests.post(f"{BASE_URL}/api/accounting/tax/calculate-tds", 
                                   headers=headers, json=tds_data)
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('data', {})
                print(f"✅ TDS Calculation: ₹{result.get('gross_amount', 0)} - ₹{result.get('tds_amount', 0)} = ₹{result.get('net_amount', 0)}")
                return True
            
        print(f"❌ Tax Calculations Failed: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Tax Calculations Error: {e}")
        return False

def test_inventory_accounting(token):
    """Test inventory accounting functionality"""
    print("🔍 Testing Inventory Accounting...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Get inventory valuation methods
        response = requests.get(f"{BASE_URL}/api/accounting/inventory/valuation-methods", 
                              headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            methods = data.get('data', [])
            print(f"✅ Inventory Valuation Methods: Found {len(methods)} methods")
            
            # Test inventory valuation report
            response = requests.get(f"{BASE_URL}/api/accounting/inventory/valuation-report", 
                                  headers=headers, params={"method": "FIFO"})
            
            if response.status_code == 200:
                data = response.json()
                report = data.get('data', {})
                print(f"✅ Inventory Valuation Report: {report.get('total_items', 0)} items, ₹{report.get('total_value', 0)}")
                return True
            
        print(f"❌ Inventory Accounting Failed: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Inventory Accounting Error: {e}")
        return False

def test_financial_reports(token):
    """Test financial reports functionality"""
    print("🔍 Testing Financial Reports...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test trial balance
        response = requests.get(f"{BASE_URL}/api/accounting/reports/trial-balance", 
                              headers=headers, params={"tenant_id": 1})
        
        if response.status_code == 200:
            data = response.json()
            report = data.get('data', {})
            accounts = report.get('accounts', [])
            print(f"✅ Trial Balance: {len(accounts)} accounts")
            
            # Test P&L report
            today = date.today()
            start_date = f"{today.year}-01-01"
            end_date = today.isoformat()
            
            response = requests.get(f"{BASE_URL}/api/accounting/reports/profit-loss", 
                                  headers=headers, 
                                  params={"tenant_id": 1, "start_date": start_date, "end_date": end_date})
            
            if response.status_code == 200:
                data = response.json()
                report = data.get('data', {})
                print(f"✅ Profit & Loss: Revenue ₹{report.get('total_revenue', 0)}, Expenses ₹{report.get('total_expenses', 0)}")
                return True
            
        print(f"❌ Financial Reports Failed: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Financial Reports Error: {e}")
        return False

def test_integration_services(token):
    """Test integration services"""
    print("🔍 Testing Integration Services...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test billing sync
        sync_data = {
            "tenant_id": 1,
            "created_by": 1
        }
        response = requests.post(f"{BASE_URL}/api/accounting/integration/sync-billing", 
                               headers=headers, json=sync_data)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Billing Sync: {data.get('message', 'Success')}")
            
            # Test procurement sync
            response = requests.post(f"{BASE_URL}/api/accounting/integration/sync-procurement", 
                                   headers=headers, json=sync_data)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Procurement Sync: {data.get('message', 'Success')}")
                return True
            
        print(f"❌ Integration Services Failed: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Integration Services Error: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive accounting system test"""
    print("🚀 Starting Comprehensive Accounting System Test")
    print("=" * 60)
    
    test_results = []
    
    # Test backend health
    test_results.append(("Backend Health", test_backend_health()))
    
    # Test frontend health
    test_results.append(("Frontend Health", test_frontend_health()))
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Run all tests
    test_results.append(("Chart of Accounts", test_chart_of_accounts(token)))
    test_results.append(("Journal Entries", test_journal_entries(token)))
    test_results.append(("Tax Calculations", test_tax_calculations(token)))
    test_results.append(("Inventory Accounting", test_inventory_accounting(token)))
    test_results.append(("Financial Reports", test_financial_reports(token)))
    test_results.append(("Integration Services", test_integration_services(token)))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Accounting system is fully functional!")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    run_comprehensive_test()
