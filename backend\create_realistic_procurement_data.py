#!/usr/bin/env python3
"""
Create realistic procurement data for testing
"""

import sqlite3
import os
from datetime import datetime, timed<PERSON>ta

def get_db_connection():
    """Get database connection"""
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'avini_labs.db')
    return sqlite3.connect(db_path)

def execute_query(query, params=None):
    """Execute a query and return results"""
    conn = get_db_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            results = [dict(row) for row in cursor.fetchall()]
        else:
            conn.commit()
            results = cursor.lastrowid
        
        return results
    finally:
        conn.close()

def create_realistic_data():
    """Create realistic procurement workflow data"""
    print("Creating realistic procurement workflow data...")
    
    # Sample data for different scenarios
    scenarios = [
        {
            'type': 'urgent_restock',
            'requesting_tenant': 2,  # <PERSON><PERSON><PERSON>
            'hub_tenant': 1,  # Mayiladuthurai
            'priority': 'urgent',
            'items': [
                {
                    'name': 'Blood Collection Tubes (EDTA)',
                    'description': 'Purple top tubes for blood collection - urgent restock',
                    'quantity': 100,
                    'unit': 'pieces',
                    'unit_price': 2.50,
                    'total': 250.00
                },
                {
                    'name': 'Disposable Syringes 5ml',
                    'description': 'Sterile disposable syringes',
                    'quantity': 200,
                    'unit': 'pieces',
                    'unit_price': 1.25,
                    'total': 250.00
                }
            ]
        },
        {
            'type': 'monthly_reagents',
            'requesting_tenant': 3,  # Thanjavur
            'hub_tenant': 1,  # Mayiladuthurai
            'priority': 'medium',
            'items': [
                {
                    'name': 'Glucose Reagent Kit',
                    'description': 'Reagent kit for glucose testing',
                    'quantity': 5,
                    'unit': 'kits',
                    'unit_price': 150.00,
                    'total': 750.00
                },
                {
                    'name': 'Cholesterol Reagent Kit',
                    'description': 'Reagent kit for cholesterol testing',
                    'quantity': 3,
                    'unit': 'kits',
                    'unit_price': 200.00,
                    'total': 600.00
                }
            ]
        },
        {
            'type': 'equipment_supplies',
            'requesting_tenant': 4,  # Kuthalam
            'hub_tenant': 1,  # Mayiladuthurai
            'priority': 'high',
            'items': [
                {
                    'name': 'Microscope Slides',
                    'description': 'Glass slides for microscopy',
                    'quantity': 500,
                    'unit': 'pieces',
                    'unit_price': 0.50,
                    'total': 250.00
                },
                {
                    'name': 'Cover Slips',
                    'description': 'Glass cover slips for microscopy',
                    'quantity': 1000,
                    'unit': 'pieces',
                    'unit_price': 0.25,
                    'total': 250.00
                }
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"\nCreating scenario {i+1}: {scenario['type']}")
        
        # Create Purchase Request
        pr_number = f"PR-{scenario['requesting_tenant']:02d}-{datetime.now().strftime('%Y%m%d')}-{i+1:03d}"
        total_amount = sum(item['total'] for item in scenario['items'])
        
        pr_id = execute_query("""
            INSERT INTO purchase_requests (
                request_number, requesting_tenant_id, hub_tenant_id, priority, status,
                request_date, required_date, notes, total_estimated_amount, tenant_id, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            pr_number, scenario['requesting_tenant'], scenario['hub_tenant'], 
            scenario['priority'], 'approved',
            datetime.now().date().isoformat(),
            (datetime.now() + timedelta(days=7)).date().isoformat(),
            f"Purchase request for {scenario['type']} - {len(scenario['items'])} items",
            total_amount, scenario['requesting_tenant'], 1
        ))
        
        print(f"  ✓ Created PR: {pr_number} (ID: {pr_id})")
        
        # Add items to Purchase Request
        for item in scenario['items']:
            execute_query("""
                INSERT INTO purchase_request_items (
                    purchase_request_id, item_name, description, requested_quantity, 
                    unit, estimated_unit_price, total_estimated_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                pr_id, item['name'], item['description'], item['quantity'],
                item['unit'], item['unit_price'], item['total']
            ))
        
        print(f"  ✓ Added {len(scenario['items'])} items to PR")
        
        # Create Purchase Order
        po_number = f"PO-{scenario['requesting_tenant']:02d}-{datetime.now().strftime('%Y%m%d')}-{i+1:03d}"
        subtotal = total_amount
        tax_rate = 18.0
        tax_amount = subtotal * (tax_rate / 100)
        po_total = subtotal + tax_amount
        
        po_id = execute_query("""
            INSERT INTO purchase_orders (
                po_number, tenant_id, status, order_date, expected_delivery_date,
                subtotal, tax_rate, tax_amount, total_amount, purchase_request_id, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            po_number, scenario['hub_tenant'], 'confirmed', 
            datetime.now().date().isoformat(),
            (datetime.now() + timedelta(days=5)).date().isoformat(),
            subtotal, tax_rate, tax_amount, po_total, pr_id, 1
        ))
        
        print(f"  ✓ Created PO: {po_number} (ID: {po_id})")
        
        # Add items to Purchase Order
        for item in scenario['items']:
            execute_query("""
                INSERT INTO purchase_order_items (
                    purchase_order_id, item_name, description, quantity, 
                    unit, unit_price, total_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                po_id, item['name'], item['description'], item['quantity'],
                item['unit'], item['unit_price'], item['total']
            ))
        
        print(f"  ✓ Added {len(scenario['items'])} items to PO")
        
        # Create Delivery Note
        dn_number = f"DN-{scenario['requesting_tenant']:02d}-{datetime.now().strftime('%Y%m%d')}-{i+1:03d}"
        
        dn_id = execute_query("""
            INSERT INTO delivery_notes (
                delivery_number, purchase_order_id, from_tenant_id, to_tenant_id, 
                delivery_type, status, delivery_date, expected_delivery_date, 
                total_amount, tracking_number, carrier, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            dn_number, po_id, scenario['hub_tenant'], scenario['requesting_tenant'], 
            'hub_to_franchise', 'dispatched',
            datetime.now().date().isoformat(),
            (datetime.now() + timedelta(days=2)).date().isoformat(),
            po_total, f"TRK-{datetime.now().strftime('%Y%m%d%H%M%S')}-{i+1}", 
            'AVINI Logistics', 1
        ))
        
        print(f"  ✓ Created DN: {dn_number} (ID: {dn_id})")
        
        # Add items to Delivery Note
        for item in scenario['items']:
            execute_query("""
                INSERT INTO delivery_note_items (
                    delivery_note_id, item_name, description, quantity, 
                    unit, unit_price, total_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                dn_id, item['name'], item['description'], item['quantity'],
                item['unit'], item['unit_price'], item['total']
            ))
        
        print(f"  ✓ Added {len(scenario['items'])} items to DN")

def verify_created_data():
    """Verify the created data"""
    print("\n" + "="*60)
    print("VERIFYING CREATED PROCUREMENT DATA")
    print("="*60)
    
    # Check counts
    pr_count = execute_query("SELECT COUNT(*) as count FROM purchase_requests")[0]['count']
    po_count = execute_query("SELECT COUNT(*) as count FROM purchase_orders")[0]['count']
    dn_count = execute_query("SELECT COUNT(*) as count FROM delivery_notes")[0]['count']
    
    print(f"\n📊 RECORD COUNTS:")
    print(f"  • Purchase Requests: {pr_count}")
    print(f"  • Purchase Orders: {po_count}")
    print(f"  • Delivery Notes: {dn_count}")
    
    # Show recent records
    print(f"\n📋 RECENT PURCHASE REQUESTS:")
    recent_prs = execute_query("""
        SELECT pr.request_number, pr.status, pr.total_estimated_amount, 
               t.name as tenant_name, pr.priority
        FROM purchase_requests pr
        LEFT JOIN tenants t ON pr.requesting_tenant_id = t.id
        ORDER BY pr.created_at DESC LIMIT 5
    """)
    for pr in recent_prs:
        print(f"  • {pr['request_number']} - {pr['status']} - ${pr['total_estimated_amount']} ({pr['tenant_name']}) [{pr['priority']}]")
    
    print(f"\n📦 RECENT PURCHASE ORDERS:")
    recent_pos = execute_query("""
        SELECT po.po_number, po.status, po.total_amount, 
               t.name as tenant_name
        FROM purchase_orders po
        LEFT JOIN tenants t ON po.tenant_id = t.id
        ORDER BY po.created_at DESC LIMIT 5
    """)
    for po in recent_pos:
        print(f"  • {po['po_number']} - {po['status']} - ${po['total_amount']} ({po['tenant_name']})")
    
    print(f"\n🚚 RECENT DELIVERY NOTES:")
    recent_dns = execute_query("""
        SELECT dn.delivery_number, dn.status, dn.total_amount,
               ft.name as from_tenant, tt.name as to_tenant
        FROM delivery_notes dn
        LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
        LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
        ORDER BY dn.created_at DESC LIMIT 5
    """)
    for dn in recent_dns:
        print(f"  • {dn['delivery_number']} - {dn['status']} - ${dn['total_amount']} ({dn['from_tenant']} → {dn['to_tenant']})")

def main():
    """Main function"""
    print("🚀 CREATING REALISTIC PROCUREMENT WORKFLOW DATA")
    print("="*60)
    
    create_realistic_data()
    verify_created_data()
    
    print("\n✅ REALISTIC PROCUREMENT DATA CREATED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
