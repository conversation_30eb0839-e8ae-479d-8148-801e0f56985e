"""
Delivery Note Status Management Service

This service handles comprehensive status management for delivery notes,
including state transitions, validation, and audit trail.
"""

import logging
from datetime import datetime
from database_manager import DatabaseManager
from services.inventory_quantity_service import InventoryQuantityService

class DeliveryNoteStatusService:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.inventory_service = InventoryQuantityService()
        
        # Define valid status transitions
        self.hub_status_transitions = {
            'prepared': ['dispatched', 'cancelled'],
            'dispatched': ['in_transit', 'delivered', 'cancelled'],
            'in_transit': ['delivered', 'returned', 'cancelled'],
            'delivered': ['returned', 'reprocessed'],
            'returned': ['reprocessed', 'cancelled'],
            'reprocessed': ['dispatched'],
            'cancelled': [],  # Terminal state
            'partially_delivered': ['delivered', 'returned', 'cancelled']
        }
        
        self.franchise_status_transitions = {
            'pending_receipt': ['received', 'rejected'],
            'received': ['confirmed', 'rejected', 'partially_accepted'],
            'confirmed': [],  # Terminal state
            'rejected': [],   # Terminal state
            'partially_accepted': ['confirmed', 'rejected']
        }
    
    def validate_hub_status_transition(self, current_status, new_status):
        """Validate if hub status transition is allowed"""
        if current_status not in self.hub_status_transitions:
            return False, f"Invalid current hub status: {current_status}"
        
        allowed_transitions = self.hub_status_transitions[current_status]
        if new_status not in allowed_transitions:
            return False, f"Cannot transition from {current_status} to {new_status}. Allowed: {allowed_transitions}"
        
        return True, "Valid transition"
    
    def validate_franchise_status_transition(self, current_status, new_status):
        """Validate if franchise status transition is allowed"""
        if current_status not in self.franchise_status_transitions:
            return False, f"Invalid current franchise status: {current_status}"
        
        allowed_transitions = self.franchise_status_transitions[current_status]
        if new_status not in allowed_transitions:
            return False, f"Cannot transition from {current_status} to {new_status}. Allowed: {allowed_transitions}"
        
        return True, "Valid transition"
    
    def update_hub_status(self, delivery_note_id, new_status, user_id, reason=None, notes=None):
        """Update hub status with validation and audit trail"""
        try:
            # Get current delivery note
            dn_query = "SELECT * FROM delivery_notes WHERE id = ?"
            dn_result = self.db_manager.execute_query(dn_query, (delivery_note_id,))
            
            if not dn_result:
                raise Exception("Delivery note not found")
            
            delivery_note = dn_result[0]
            current_status = delivery_note.get('hub_status', 'prepared')
            
            # Validate transition
            is_valid, message = self.validate_hub_status_transition(current_status, new_status)
            if not is_valid:
                raise Exception(message)
            
            # Update delivery note
            update_query = """
                UPDATE delivery_notes 
                SET hub_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            self.db_manager.execute_update(update_query, (new_status, delivery_note_id))
            
            # Add status-specific fields
            self._update_status_specific_fields(delivery_note_id, 'hub', new_status, user_id)

            # Handle inventory quantity transitions
            self._handle_inventory_quantity_transition(delivery_note_id, current_status, new_status)

            # Record status change in history
            self._record_status_change(
                delivery_note_id, 'hub_status', current_status, new_status, user_id, reason, notes
            )
            
            # Update legacy status for backward compatibility
            legacy_status = self._map_to_legacy_status(new_status, delivery_note.get('franchise_status'))
            if legacy_status != delivery_note.get('status'):
                self.db_manager.execute_update(
                    "UPDATE delivery_notes SET status = ? WHERE id = ?",
                    (legacy_status, delivery_note_id)
                )
            
            self.logger.info(f"Hub status updated for delivery note {delivery_note_id}: {current_status} -> {new_status}")
            return True, "Status updated successfully"
            
        except Exception as e:
            self.logger.error(f"Error updating hub status: {e}")
            raise e
    
    def update_franchise_status(self, delivery_note_id, new_status, user_id, reason=None, notes=None):
        """Update franchise status with validation and audit trail"""
        try:
            # Get current delivery note
            dn_query = "SELECT * FROM delivery_notes WHERE id = ?"
            dn_result = self.db_manager.execute_query(dn_query, (delivery_note_id,))
            
            if not dn_result:
                raise Exception("Delivery note not found")
            
            delivery_note = dn_result[0]
            current_status = delivery_note.get('franchise_status', 'pending_receipt')
            
            # Validate transition
            is_valid, message = self.validate_franchise_status_transition(current_status, new_status)
            if not is_valid:
                raise Exception(message)
            
            # Update delivery note
            update_query = """
                UPDATE delivery_notes 
                SET franchise_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            self.db_manager.execute_update(update_query, (new_status, delivery_note_id))
            
            # Add status-specific fields
            self._update_status_specific_fields(delivery_note_id, 'franchise', new_status, user_id)
            
            # Record status change in history
            self._record_status_change(
                delivery_note_id, 'franchise_status', current_status, new_status, user_id, reason, notes
            )
            
            # Update legacy status for backward compatibility
            legacy_status = self._map_to_legacy_status(delivery_note.get('hub_status'), new_status)
            if legacy_status != delivery_note.get('status'):
                self.db_manager.execute_update(
                    "UPDATE delivery_notes SET status = ? WHERE id = ?",
                    (legacy_status, delivery_note_id)
                )
            
            self.logger.info(f"Franchise status updated for delivery note {delivery_note_id}: {current_status} -> {new_status}")
            return True, "Status updated successfully"
            
        except Exception as e:
            self.logger.error(f"Error updating franchise status: {e}")
            raise e
    
    def _update_status_specific_fields(self, delivery_note_id, status_type, new_status, user_id):
        """Update status-specific timestamp and user fields"""
        timestamp_field = None
        user_field = None
        
        if status_type == 'hub':
            if new_status == 'dispatched':
                timestamp_field, user_field = 'dispatched_at', 'dispatched_by'
            elif new_status == 'delivered':
                timestamp_field, user_field = 'delivered_at', 'received_by'
            elif new_status == 'returned':
                timestamp_field, user_field = 'returned_at', 'returned_by'
            elif new_status == 'reprocessed':
                timestamp_field, user_field = 'reprocessed_at', 'reprocessed_by'
        
        elif status_type == 'franchise':
            if new_status == 'confirmed':
                timestamp_field, user_field = 'confirmed_at', 'confirmed_by'
            elif new_status == 'rejected':
                timestamp_field, user_field = 'rejected_at', 'rejected_by'
        
        if timestamp_field and user_field:
            update_query = f"""
                UPDATE delivery_notes 
                SET {timestamp_field} = CURRENT_TIMESTAMP, {user_field} = ?
                WHERE id = ?
            """
            self.db_manager.execute_update(update_query, (user_id, delivery_note_id))
    
    def _record_status_change(self, delivery_note_id, status_type, old_status, new_status, user_id, reason, notes):
        """Record status change in history table"""
        try:
            history_query = """
                INSERT INTO delivery_note_status_history 
                (delivery_note_id, status_type, old_status, new_status, changed_by, change_reason, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            self.db_manager.execute_insert(history_query, (
                delivery_note_id, status_type, old_status, new_status, user_id, reason, notes
            ))
        except Exception as e:
            self.logger.warning(f"Failed to record status change history: {e}")
    
    def _map_to_legacy_status(self, hub_status, franchise_status):
        """Map new status fields to legacy status for backward compatibility"""
        # Priority: franchise status takes precedence for final states
        if franchise_status in ['confirmed', 'rejected']:
            return 'delivered' if franchise_status == 'confirmed' else 'cancelled'
        
        # Otherwise use hub status
        if hub_status in ['prepared', 'dispatched', 'in_transit', 'delivered', 'cancelled']:
            return hub_status
        elif hub_status == 'returned':
            return 'cancelled'
        elif hub_status == 'reprocessed':
            return 'prepared'
        elif hub_status == 'partially_delivered':
            return 'delivered'
        
        return 'prepared'  # Default
    
    def get_status_history(self, delivery_note_id):
        """Get status change history for a delivery note"""
        try:
            history_query = """
                SELECT dsh.*, u.username as changed_by_username
                FROM delivery_note_status_history dsh
                LEFT JOIN users u ON dsh.changed_by = u.id
                WHERE dsh.delivery_note_id = ?
                ORDER BY dsh.created_at DESC
            """
            return self.db_manager.execute_query(history_query, (delivery_note_id,))
        except Exception as e:
            self.logger.error(f"Error getting status history: {e}")
            return []
    
    def get_allowed_transitions(self, delivery_note_id, user_role, user_tenant_id):
        """Get allowed status transitions for a user based on their role and current status"""
        try:
            # Get current delivery note
            dn_query = "SELECT * FROM delivery_notes WHERE id = ?"
            dn_result = self.db_manager.execute_query(dn_query, (delivery_note_id,))
            
            if not dn_result:
                return {'hub_transitions': [], 'franchise_transitions': []}
            
            delivery_note = dn_result[0]
            hub_status = delivery_note.get('hub_status', 'prepared')
            franchise_status = delivery_note.get('franchise_status', 'pending_receipt')
            
            result = {'hub_transitions': [], 'franchise_transitions': []}
            
            # Hub users can manage hub status ONLY
            if user_role in ['admin', 'hub_admin']:
                result['hub_transitions'] = self.hub_status_transitions.get(hub_status, [])
                # Hub users CANNOT manage franchise status
                result['franchise_transitions'] = []

            # ONLY franchise users can manage franchise status for their deliveries
            if (user_role == 'franchise_admin' and delivery_note.get('to_tenant_id') == user_tenant_id):
                result['franchise_transitions'] = self.franchise_status_transitions.get(franchise_status, [])
                # Franchise users cannot manage hub status
                result['hub_transitions'] = []
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting allowed transitions: {e}")
            return {'hub_transitions': [], 'franchise_transitions': []}

    def _handle_inventory_quantity_transition(self, delivery_note_id, from_status, to_status):
        """Handle inventory quantity transitions based on status changes"""
        try:
            # Get delivery note items
            items_query = """
                SELECT dni.inventory_item_id, dni.requested_quantity
                FROM delivery_note_items dni
                WHERE dni.delivery_note_id = ? AND dni.inventory_item_id IS NOT NULL
            """
            items_result = self.db_manager.execute_query(items_query, (delivery_note_id,))

            if not items_result:
                return  # No inventory items to manage

            # Convert to format expected by inventory service
            items = [
                {
                    'inventory_item_id': item['inventory_item_id'],
                    'requested_quantity': item['requested_quantity']
                }
                for item in items_result
            ]

            # Handle specific status transitions
            if from_status == 'prepared' and to_status == 'dispatched':
                # Move from reserved to shipped
                success = self.inventory_service.dispatch_inventory(delivery_note_id, items)
                if success:
                    self.logger.info(f"Moved inventory from reserved to shipped for DN {delivery_note_id}")
                else:
                    self.logger.error(f"Failed to move inventory from reserved to shipped for DN {delivery_note_id}")

            elif from_status == 'dispatched' and to_status == 'delivered':
                # This could trigger final consumption, but we'll handle it when franchise confirms receipt
                pass

            elif to_status == 'cancelled':
                # Return quantities to available inventory
                success = self.inventory_service.restore_inventory(delivery_note_id, items, from_status)
                if success:
                    self.logger.info(f"Restored inventory quantities for cancelled DN {delivery_note_id}")
                else:
                    self.logger.error(f"Failed to restore inventory quantities for cancelled DN {delivery_note_id}")

        except Exception as e:
            self.logger.error(f"Error handling inventory quantity transition for DN {delivery_note_id}: {e}")
            # Don't raise the exception to avoid breaking the status update
