#!/usr/bin/env python3
"""
Test script to verify procurement module fixes
"""

import sys
import os
sys.path.append('backend')

import sqlite3
import json
from datetime import datetime

def test_database_connection():
    """Test database connectivity"""
    try:
        conn = sqlite3.connect('backend/data/avini_labs.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Test proforma invoices
        cursor.execute("SELECT COUNT(*) as count FROM proforma_invoices")
        pi_count = cursor.fetchone()['count']
        print(f"✅ Proforma invoices in database: {pi_count}")
        
        # Test delivery notes
        cursor.execute("SELECT COUNT(*) as count FROM delivery_notes")
        dn_count = cursor.fetchone()['count']
        print(f"✅ Delivery notes in database: {dn_count}")
        
        # Test delivery note items table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_note_items'")
        table_exists = cursor.fetchone()
        if table_exists:
            print("✅ delivery_note_items table exists")
        else:
            print("❌ delivery_note_items table missing")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_route_imports():
    """Test if the route files can be imported"""
    try:
        from routes.procurement_routes_db import procurement_bp
        print("✅ procurement_routes_db imported successfully")
        
        from services.delivery_service import DeliveryService
        print("✅ DeliveryService imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_delivery_service():
    """Test delivery service functionality"""
    try:
        from services.delivery_service import DeliveryService
        from database_manager import db_manager
        
        service = DeliveryService()
        
        # Test delivery number generation
        delivery_number = service._generate_delivery_number(2)
        print(f"✅ Generated delivery number: {delivery_number}")
        
        return True
        
    except Exception as e:
        print(f"❌ Delivery service error: {e}")
        return False

def create_missing_table():
    """Create delivery_note_items table if missing"""
    try:
        conn = sqlite3.connect('backend/data/avini_labs.db')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_note_items'")
        if not cursor.fetchone():
            print("Creating delivery_note_items table...")
            cursor.execute('''
                CREATE TABLE delivery_note_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    delivery_note_id INTEGER NOT NULL,
                    item_name TEXT NOT NULL,
                    description TEXT,
                    quantity INTEGER NOT NULL,
                    unit TEXT NOT NULL,
                    unit_price DECIMAL(10,2) DEFAULT 0,
                    total_price DECIMAL(10,2) DEFAULT 0,
                    quantity_received INTEGER,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id)
                )
            ''')
            conn.commit()
            print("✅ delivery_note_items table created")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Table creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing Procurement Module Fixes")
    print("=" * 50)
    
    # Test 1: Database connectivity
    print("\n1. Testing Database Connection:")
    db_ok = test_database_connection()
    
    # Test 2: Create missing table if needed
    print("\n2. Checking/Creating Required Tables:")
    table_ok = create_missing_table()
    
    # Test 3: Import tests
    print("\n3. Testing Route Imports:")
    import_ok = test_route_imports()
    
    # Test 4: Service tests
    print("\n4. Testing Delivery Service:")
    service_ok = test_delivery_service()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"Database Connection: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"Required Tables: {'✅ PASS' if table_ok else '❌ FAIL'}")
    print(f"Route Imports: {'✅ PASS' if import_ok else '❌ FAIL'}")
    print(f"Delivery Service: {'✅ PASS' if service_ok else '❌ FAIL'}")
    
    all_passed = db_ok and table_ok and import_ok and service_ok
    print(f"\n🎯 Overall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🚀 Procurement module fixes are ready for testing!")
        print("You can now test the following endpoints:")
        print("- POST /api/procurement/delivery-notes (Create delivery note)")
        print("- POST /api/procurement/delivery-notes/<id>/confirm (Confirm delivery)")
        print("- GET /api/procurement/proforma-invoices (List proforma invoices)")
    
    return all_passed

if __name__ == "__main__":
    main()
