import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ge, Modal, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faPlus, 
  faExclamationTriangle,
  faInfoCircle,
  faWarning,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import api from '../../services/api';
import { useAuth } from '../../context/AuthContext';

const InventoryItemSelector = ({ 
  storeroomId, 
  selectedItems = [], 
  onItemsChange, 
  disabled = false 
}) => {
  const { currentUser } = useAuth();
  const [availableItems, setAvailableItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showItemModal, setShowItemModal] = useState(false);
  const [validationResults, setValidationResults] = useState({});

  useEffect(() => {
    if (storeroomId) {
      loadInventoryItems();
    } else {
      setAvailableItems([]);
    }
  }, [storeroomId]);

  useEffect(() => {
    if (selectedItems.length > 0) {
      validateSelectedItems();
    }
  }, [selectedItems]);

  const loadInventoryItems = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        active_only: true,
        in_stock_only: false,
        search: searchTerm
      };
      
      const response = await api.get(`/inventory-db/storeroom/${storeroomId}/items`, { params });
      
      if (response.data.success) {
        setAvailableItems(response.data.data.items);
      } else {
        setError(response.data.error || 'Failed to load inventory items');
      }
    } catch (err) {
      console.error('Error loading inventory items:', err);
      setError('Failed to load inventory items');
    } finally {
      setLoading(false);
    }
  };

  const validateSelectedItems = async () => {
    try {
      const itemsToValidate = selectedItems
        .filter(item => item.inventory_item_id)
        .map(item => ({
          inventory_item_id: item.inventory_item_id,
          requested_quantity: item.requested_quantity || item.quantity
        }));

      if (itemsToValidate.length === 0) return;

      const response = await api.post('/inventory-db/items/bulk-validate', {
        items: itemsToValidate
      });

      if (response.data.success) {
        const results = {};
        response.data.data.validations.forEach(validation => {
          results[validation.item_data.inventory_item_id] = validation;
        });
        setValidationResults(results);
      }
    } catch (err) {
      console.error('Error validating items:', err);
    }
  };

  const handleAddItem = (inventoryItem) => {
    const newItem = {
      inventory_item_id: inventoryItem.id,
      item_name: inventoryItem.name,
      description: inventoryItem.description,
      sku: inventoryItem.sku,
      category: inventoryItem.category,
      unit: inventoryItem.unit,
      unit_price: inventoryItem.selling_price || inventoryItem.cost_price,
      requested_quantity: 1,
      quantity: 1, // For backward compatibility
      available_stock: inventoryItem.quantity,
      batch_number: inventoryItem.batch_number,
      expiry_date: inventoryItem.expiry_date,
      notes: ''
    };

    const updatedItems = [...selectedItems, newItem];
    onItemsChange(updatedItems);
    setShowItemModal(false);
  };

  const handleUpdateItem = (index, field, value) => {
    const updatedItems = [...selectedItems];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    
    // Update quantity for backward compatibility
    if (field === 'requested_quantity') {
      updatedItems[index].quantity = value;
    }
    
    onItemsChange(updatedItems);
  };

  const handleRemoveItem = (index) => {
    const updatedItems = selectedItems.filter((_, i) => i !== index);
    onItemsChange(updatedItems);
  };

  const filteredItems = availableItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStockStatusBadge = (item) => {
    if (item.quantity <= 0) {
      return <Badge bg="danger">Out of Stock</Badge>;
    } else if (item.is_low_stock) {
      return <Badge bg="warning">Low Stock</Badge>;
    } else {
      return <Badge bg="success">In Stock</Badge>;
    }
  };

  const getValidationBadge = (inventoryItemId, requestedQuantity) => {
    const validation = validationResults[inventoryItemId];
    if (!validation) return null;

    if (!validation.is_valid) {
      return (
        <Badge bg="danger" className="ms-2">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-1" />
          Insufficient Stock
        </Badge>
      );
    } else if (validation.available_stock <= validation.requested_quantity * 1.2) {
      return (
        <Badge bg="warning" className="ms-2">
          <FontAwesomeIcon icon={faWarning} className="me-1" />
          Low Stock Warning
        </Badge>
      );
    } else {
      return (
        <Badge bg="success" className="ms-2">
          <FontAwesomeIcon icon={faCheck} className="me-1" />
          Stock OK
        </Badge>
      );
    }
  };

  if (!storeroomId) {
    return (
      <Alert variant="info">
        <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
        Please select a storeroom first to choose inventory items.
      </Alert>
    );
  }

  return (
    <div>
      {/* Selected Items Table */}
      {selectedItems.length > 0 && (
        <div className="mb-3">
          <h6>Selected Items</h6>
          <div className="table-responsive">
            <Table striped bordered hover size="sm">
              <thead>
                <tr>
                  <th>Item Name</th>
                  <th>SKU</th>
                  <th>Unit</th>
                  <th>Available</th>
                  <th>Requested Qty</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {selectedItems.map((item, index) => (
                  <tr key={index}>
                    <td>
                      {item.item_name}
                      {item.inventory_item_id && getValidationBadge(item.inventory_item_id, item.requested_quantity)}
                    </td>
                    <td>{item.sku || '-'}</td>
                    <td>{item.unit}</td>
                    <td>
                      {item.available_stock !== undefined ? (
                        <span className={item.available_stock <= 0 ? 'text-danger' : item.available_stock <= 10 ? 'text-warning' : 'text-success'}>
                          {item.available_stock}
                        </span>
                      ) : '-'}
                    </td>
                    <td>
                      <Form.Control
                        type="number"
                        size="sm"
                        min="1"
                        value={item.requested_quantity || item.quantity}
                        onChange={(e) => handleUpdateItem(index, 'requested_quantity', parseInt(e.target.value) || 1)}
                        disabled={disabled}
                        style={{ width: '80px' }}
                      />
                    </td>
                    <td>
                      <Form.Control
                        type="number"
                        size="sm"
                        step="0.01"
                        min="0"
                        value={item.unit_price || 0}
                        onChange={(e) => handleUpdateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                        disabled={disabled}
                        style={{ width: '100px' }}
                      />
                    </td>
                    <td>
                      ₹{((item.unit_price || 0) * (item.requested_quantity || item.quantity || 0)).toFixed(2)}
                    </td>
                    <td>
                      {item.inventory_item_id ? (
                        <Badge bg="primary">From Inventory</Badge>
                      ) : (
                        <Badge bg="secondary">Manual Entry</Badge>
                      )}
                    </td>
                    <td>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleRemoveItem(index)}
                        disabled={disabled}
                      >
                        Remove
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        </div>
      )}

      {/* Add Item Button */}
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6>Add Items</h6>
        <Button
          variant="outline-primary"
          size="sm"
          onClick={() => setShowItemModal(true)}
          disabled={disabled || loading}
        >
          <FontAwesomeIcon icon={faPlus} className="me-1" />
          Add from Inventory
        </Button>
      </div>

      {error && (
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Inventory Item Selection Modal */}
      <Modal show={showItemModal} onHide={() => setShowItemModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Select Inventory Items</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Search */}
          <Form.Group className="mb-3">
            <Form.Label>Search Items</Form.Label>
            <div className="input-group">
              <Form.Control
                type="text"
                placeholder="Search by name, SKU, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button variant="outline-secondary" onClick={loadInventoryItems}>
                <FontAwesomeIcon icon={faSearch} />
              </Button>
            </div>
          </Form.Group>

          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" />
              <div className="mt-2">Loading inventory items...</div>
            </div>
          ) : (
            <div className="table-responsive" style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Table striped hover size="sm">
                <thead className="sticky-top bg-light">
                  <tr>
                    <th>Item Name</th>
                    <th>SKU</th>
                    <th>Category</th>
                    <th>Stock</th>
                    <th>Unit</th>
                    <th>Price</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredItems.map((item) => (
                    <tr key={item.id}>
                      <td>
                        <div>
                          <strong>{item.name}</strong>
                          {item.description && (
                            <div className="text-muted small">{item.description}</div>
                          )}
                        </div>
                      </td>
                      <td>{item.sku}</td>
                      <td>{item.category}</td>
                      <td>
                        <span className={item.quantity <= 0 ? 'text-danger' : item.is_low_stock ? 'text-warning' : 'text-success'}>
                          {item.quantity} {item.unit}
                        </span>
                      </td>
                      <td>{item.unit}</td>
                      <td>₹{item.selling_price || item.cost_price}</td>
                      <td>{getStockStatusBadge(item)}</td>
                      <td>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleAddItem(item)}
                          disabled={selectedItems.some(selected => selected.inventory_item_id === item.id)}
                        >
                          {selectedItems.some(selected => selected.inventory_item_id === item.id) ? 'Added' : 'Add'}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
              
              {filteredItems.length === 0 && !loading && (
                <div className="text-center py-4 text-muted">
                  No inventory items found
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowItemModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default InventoryItemSelector;
