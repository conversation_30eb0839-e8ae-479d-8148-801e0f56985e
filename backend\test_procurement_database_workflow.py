#!/usr/bin/env python3
"""
Direct Database Testing for Complete Procurement Workflow
Tests the complete cycle: PR → PO → Delivery → Inventory Update without requiring server
"""

import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import db_manager
from services.purchase_order_service import PurchaseOrderService
from services.delivery_service import DeliveryService

class DirectProcurementTester:
    def __init__(self):
        self.po_service = PurchaseOrderService()
        self.delivery_service = DeliveryService()
        self.test_results = {
            'purchase_requests': [],
            'purchase_orders': [],
            'delivery_notes': [],
            'inventory_updates': []
        }
    
    def setup_test_environment(self):
        """Setup test data and environment"""
        print("=== SETTING UP TEST ENVIRONMENT ===")
        
        # Create procurement tables if they don't exist
        try:
            with open('create_procurement_tables.sql', 'r') as f:
                sql_script = f.read()
            
            # Execute the SQL script
            statements = sql_script.split(';')
            for statement in statements:
                if statement.strip():
                    try:
                        db_manager.execute_query(statement.strip())
                    except Exception as e:
                        if "already exists" not in str(e):
                            print(f"Warning: {e}")
            
            print("✓ Procurement tables created/verified")
        except Exception as e:
            print(f"✗ Error setting up tables: {e}")
            return False
        
        # Create test inventory items in storerooms
        self._create_test_inventory()
        
        return True
    
    def _create_test_inventory(self):
        """Create test inventory items for procurement testing"""
        print("\n--- Creating Test Inventory Items ---")
        
        # Get available storerooms
        storerooms = db_manager.execute_query("""
            SELECT s.*, t.name as tenant_name 
            FROM storerooms s 
            LEFT JOIN tenants t ON s.tenant_id = t.id 
            WHERE s.status = 'active'
        """)
        
        if not storerooms:
            print("✗ No active storerooms found")
            return
        
        test_items = [
            {
                'name': 'Blood Collection Tubes (EDTA)',
                'sku': 'BCT-EDTA-001',
                'category': 'Laboratory Supplies',
                'description': 'EDTA tubes for blood collection',
                'quantity': 5,  # Low stock to trigger reorder
                'unit': 'pieces',
                'reorder_level': 10,
                'cost_price': 2.50,
                'selling_price': 3.00,
                'supplier': 'MedSupply Co.'
            },
            {
                'name': 'Reagent Kit - Glucose',
                'sku': 'RK-GLU-001',
                'category': 'Reagents',
                'description': 'Glucose testing reagent kit',
                'quantity': 2,  # Very low stock
                'unit': 'kits',
                'reorder_level': 5,
                'cost_price': 45.00,
                'selling_price': 55.00,
                'supplier': 'BioReagents Ltd.'
            }
        ]
        
        for storeroom in storerooms[:2]:  # Create items in first 2 storerooms
            for item in test_items:
                try:
                    # Check if item already exists
                    existing = db_manager.execute_query(
                        "SELECT id FROM inventory WHERE sku = ? AND storeroom_id = ?",
                        (item['sku'], storeroom['id'])
                    )
                    
                    if not existing:
                        insert_query = """
                            INSERT INTO inventory (
                                name, sku, category, description, quantity, unit, reorder_level,
                                cost_price, selling_price, supplier, storeroom_id, tenant_id, created_by
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        db_manager.execute_query(insert_query, (
                            item['name'], item['sku'], item['category'], item['description'],
                            item['quantity'], item['unit'], item['reorder_level'],
                            item['cost_price'], item['selling_price'], item['supplier'],
                            storeroom['id'], storeroom['tenant_id'], 1
                        ))
                        
                        print(f"✓ Created inventory item: {item['name']} in {storeroom['name']}")
                    else:
                        print(f"- Item {item['name']} already exists in {storeroom['name']}")
                        
                except Exception as e:
                    print(f"✗ Error creating inventory item {item['name']}: {e}")
    
    def test_complete_workflow(self):
        """Test the complete procurement workflow directly"""
        print("\n=== TESTING COMPLETE PROCUREMENT WORKFLOW ===")
        
        # Step 1: Create Manual Purchase Request
        print("\n1. CREATING MANUAL PURCHASE REQUEST")
        pr_result = self._create_test_purchase_request()
        if pr_result:
            self.test_results['purchase_requests'].append(pr_result)
            
            # Step 2: Approve Purchase Request
            print("\n2. APPROVING PURCHASE REQUEST")
            approval_result = self._approve_purchase_request(pr_result['pr_id'])
            
            # Step 3: Generate Purchase Order
            if approval_result:
                print("\n3. GENERATING PURCHASE ORDER")
                po_result = self._generate_purchase_order(pr_result['pr_id'])
                if po_result:
                    self.test_results['purchase_orders'].append(po_result)
                    
                    # Step 4: Send Purchase Order
                    print("\n4. SENDING PURCHASE ORDER")
                    send_result = self._send_purchase_order(po_result['po_id'])
                    
                    # Step 5: Confirm Purchase Order
                    if send_result:
                        print("\n5. CONFIRMING PURCHASE ORDER")
                        confirm_result = self._confirm_purchase_order(po_result['po_id'])
                        
                        # Step 6: Create Delivery Note
                        if confirm_result:
                            print("\n6. CREATING DELIVERY NOTE")
                            dn_result = self._create_delivery_note(po_result['po_id'])
                            if dn_result:
                                self.test_results['delivery_notes'].append(dn_result)
                                
                                # Step 7: Dispatch Delivery
                                print("\n7. DISPATCHING DELIVERY")
                                dispatch_result = self._dispatch_delivery(dn_result['dn_id'])
                                
                                # Step 8: Receive Delivery and Update Inventory
                                if dispatch_result:
                                    print("\n8. RECEIVING DELIVERY AND UPDATING INVENTORY")
                                    receive_result = self._receive_delivery(dn_result['dn_id'])
                                    if receive_result:
                                        self.test_results['inventory_updates'].append(receive_result)
        
        # Generate test report
        self._generate_test_report()
    
    def _create_test_purchase_request(self):
        """Create a test purchase request directly in database"""
        try:
            # Get available storerooms
            storerooms = db_manager.execute_query("""
                SELECT s.*, t.name as tenant_name 
                FROM storerooms s 
                LEFT JOIN tenants t ON s.tenant_id = t.id 
                WHERE s.status = 'active' LIMIT 1
            """)
            
            if not storerooms:
                print("✗ No storerooms available")
                return None
            
            storeroom = storerooms[0]
            
            # Generate PR number
            today = datetime.now().strftime('%Y%m%d')
            sequence = 1
            pr_number = f"PR-TEST-{today}-{sequence:03d}"
            
            # Create Purchase Request
            pr_insert_query = """
                INSERT INTO purchase_requests (
                    request_number, requesting_tenant_id, hub_tenant_id, priority, status,
                    request_date, required_date, notes, total_estimated_amount, storeroom_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            pr_params = (
                pr_number, storeroom['tenant_id'], 1, 'high', 'draft',
                datetime.now().date().isoformat(),
                (datetime.now() + timedelta(days=7)).date().isoformat(),
                'Test purchase request for workflow validation',
                575.00, storeroom['id'], 1
            )
            
            pr_id = db_manager.execute_query(pr_insert_query, pr_params)
            
            # Create Purchase Request Items
            test_items = [
                {
                    'item_name': 'Blood Collection Tubes (EDTA)',
                    'description': 'EDTA tubes for blood collection - urgent restock',
                    'requested_quantity': 50,
                    'unit': 'pieces',
                    'estimated_unit_price': 2.50,
                    'total_estimated_amount': 125.00,
                    'notes': 'Urgent restock needed'
                },
                {
                    'item_name': 'Reagent Kit - Glucose',
                    'description': 'Glucose testing reagent kit',
                    'requested_quantity': 10,
                    'unit': 'kits',
                    'estimated_unit_price': 45.00,
                    'total_estimated_amount': 450.00,
                    'notes': 'Monthly stock replenishment'
                }
            ]
            
            for item in test_items:
                item_insert_query = """
                    INSERT INTO purchase_request_items (
                        purchase_request_id, item_name, description, requested_quantity, unit,
                        estimated_unit_price, total_estimated_amount, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                db_manager.execute_query(item_insert_query, (
                    pr_id, item['item_name'], item['description'], item['requested_quantity'],
                    item['unit'], item['estimated_unit_price'], item['total_estimated_amount'],
                    item['notes']
                ))
            
            print(f"✓ Purchase Request created: {pr_number}")
            return {
                'pr_id': pr_id,
                'request_number': pr_number,
                'total_amount': 575.00
            }
            
        except Exception as e:
            print(f"✗ Error creating purchase request: {e}")
            return None
    
    def _approve_purchase_request(self, pr_id):
        """Approve a purchase request"""
        try:
            update_query = """
                UPDATE purchase_requests 
                SET status = 'approved', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """
            db_manager.execute_query(update_query, (pr_id,))
            
            print(f"✓ Purchase Request {pr_id} approved")
            return True
            
        except Exception as e:
            print(f"✗ Error approving purchase request: {e}")
            return False
    
    def _generate_purchase_order(self, pr_id):
        """Generate purchase order from approved PR"""
        try:
            result = self.po_service.generate_po_from_purchase_request(pr_id, supplier_id=1, user_id=1)
            
            if result['success']:
                print(f"✓ Purchase Order created: {result['po_number']}")
                print(f"  - Total Amount: ${result['total_amount']}")
                print(f"  - Items Count: {result['items_count']}")
                return result
            else:
                print(f"✗ Failed to generate purchase order")
                return None
                
        except Exception as e:
            print(f"✗ Error generating purchase order: {e}")
            return None
    
    def _send_purchase_order(self, po_id):
        """Send purchase order to supplier"""
        try:
            result = self.po_service.send_purchase_order(po_id, user_id=1)
            
            if result['success']:
                print(f"✓ Purchase Order {po_id} sent to supplier")
                return True
            else:
                print(f"✗ Failed to send purchase order: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"✗ Error sending purchase order: {e}")
            return False
    
    def _confirm_purchase_order(self, po_id):
        """Confirm purchase order from supplier"""
        try:
            result = self.po_service.confirm_purchase_order(po_id, user_id=1)
            
            if result['success']:
                print(f"✓ Purchase Order {po_id} confirmed by supplier")
                return True
            else:
                print(f"✗ Failed to confirm purchase order: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"✗ Error confirming purchase order: {e}")
            return False

    def _create_delivery_note(self, po_id):
        """Create delivery note from confirmed PO"""
        try:
            delivery_data = {
                'delivery_date': datetime.now().strftime('%Y-%m-%d'),
                'expected_delivery_date': (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d'),
                'notes': 'Test delivery for workflow validation'
            }

            result = self.delivery_service.create_delivery_note_from_po(po_id, delivery_data, user_id=1)

            if result['success']:
                print(f"✓ Delivery Note created: {result['delivery_number']}")
                print(f"  - Total Amount: ${result['total_amount']}")
                print(f"  - Items Count: {result['items_count']}")
                return result
            else:
                print(f"✗ Failed to create delivery note")
                return None

        except Exception as e:
            print(f"✗ Error creating delivery note: {e}")
            return None

    def _dispatch_delivery(self, dn_id):
        """Dispatch delivery note"""
        try:
            dispatch_data = {
                'tracking_number': f'TRK-{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'carrier': 'Test Logistics Co.'
            }

            result = self.delivery_service.dispatch_delivery_note(dn_id, dispatch_data, user_id=1)

            if result['success']:
                print(f"✓ Delivery Note {dn_id} dispatched")
                print(f"  - Tracking: {dispatch_data['tracking_number']}")
                return True
            else:
                print(f"✗ Failed to dispatch delivery: {result.get('error')}")
                return False

        except Exception as e:
            print(f"✗ Error dispatching delivery: {e}")
            return False

    def _receive_delivery(self, dn_id):
        """Receive delivery and update inventory"""
        try:
            receipt_data = {
                'signature': 'Test Receiver Signature',
                'notes': 'All items received in good condition'
            }

            result = self.delivery_service.receive_delivery_note(dn_id, receipt_data, user_id=1)

            if result['success']:
                print(f"✓ Delivery Note {dn_id} received and inventory updated")
                print(f"  - Inventory Updates: {len(result['inventory_updates'])}")
                for update in result['inventory_updates']:
                    print(f"    * {update['item_name']}: +{update['quantity_added']} ({update['action']})")
                return result
            else:
                print(f"✗ Failed to receive delivery: {result.get('error')}")
                return None

        except Exception as e:
            print(f"✗ Error receiving delivery: {e}")
            return None

    def _generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("COMPREHENSIVE PROCUREMENT WORKFLOW TEST REPORT")
        print("="*80)

        print(f"\n📋 PURCHASE REQUESTS CREATED: {len(self.test_results['purchase_requests'])}")
        for pr in self.test_results['purchase_requests']:
            print(f"  - {pr['request_number']}: ${pr['total_amount']}")

        print(f"\n📦 PURCHASE ORDERS GENERATED: {len(self.test_results['purchase_orders'])}")
        for po in self.test_results['purchase_orders']:
            print(f"  - {po['po_number']}: ${po['total_amount']} ({po['items_count']} items)")

        print(f"\n🚚 DELIVERY NOTES CREATED: {len(self.test_results['delivery_notes'])}")
        for dn in self.test_results['delivery_notes']:
            print(f"  - {dn['delivery_number']}: ${dn['total_amount']} ({dn['items_count']} items)")

        print(f"\n📊 INVENTORY UPDATES: {len(self.test_results['inventory_updates'])}")
        for inv in self.test_results['inventory_updates']:
            print(f"  - {len(inv['inventory_updates'])} items updated")

        # Verify database records
        print(f"\n🔍 DATABASE VERIFICATION:")
        self._verify_database_records()

        print(f"\n✅ WORKFLOW TESTING COMPLETE")
        print("="*80)

    def _verify_database_records(self):
        """Verify that all records were created in the database"""
        try:
            # Check purchase requests
            pr_count = db_manager.execute_query("SELECT COUNT(*) as count FROM purchase_requests")[0]['count']
            print(f"  - Purchase Requests in DB: {pr_count}")

            # Check purchase orders
            po_count = db_manager.execute_query("SELECT COUNT(*) as count FROM purchase_orders")[0]['count']
            print(f"  - Purchase Orders in DB: {po_count}")

            # Check delivery notes
            dn_count = db_manager.execute_query("SELECT COUNT(*) as count FROM delivery_notes")[0]['count']
            print(f"  - Delivery Notes in DB: {dn_count}")

            # Check inventory items
            inv_count = db_manager.execute_query("SELECT COUNT(*) as count FROM inventory")[0]['count']
            print(f"  - Inventory Items in DB: {inv_count}")

            # Check inventory transactions
            trans_count = db_manager.execute_query("SELECT COUNT(*) as count FROM inventory_transactions")[0]['count']
            print(f"  - Inventory Transactions in DB: {trans_count}")

            # Show recent records
            print(f"\n📝 RECENT RECORDS (Last 5):")

            recent_prs = db_manager.execute_query("""
                SELECT request_number, status, total_estimated_amount, created_at
                FROM purchase_requests
                ORDER BY created_at DESC LIMIT 5
            """)
            for pr in recent_prs:
                print(f"  PR: {pr['request_number']} - {pr['status']} (${pr['total_estimated_amount']})")

            recent_pos = db_manager.execute_query("""
                SELECT po_number, status, total_amount, created_at
                FROM purchase_orders
                ORDER BY created_at DESC LIMIT 5
            """)
            for po in recent_pos:
                print(f"  PO: {po['po_number']} - {po['status']} (${po['total_amount']})")

            recent_dns = db_manager.execute_query("""
                SELECT delivery_number, status, total_amount, created_at
                FROM delivery_notes
                ORDER BY created_at DESC LIMIT 5
            """)
            for dn in recent_dns:
                print(f"  DN: {dn['delivery_number']} - {dn['status']} (${dn['total_amount']})")

            # Show inventory with recent transactions
            recent_inventory = db_manager.execute_query("""
                SELECT i.name, i.sku, i.quantity, s.name as storeroom_name, t.name as tenant_name
                FROM inventory i
                LEFT JOIN storerooms s ON i.storeroom_id = s.id
                LEFT JOIN tenants t ON i.tenant_id = t.id
                ORDER BY i.updated_at DESC LIMIT 5
            """)
            print(f"\n📦 RECENT INVENTORY UPDATES:")
            for inv in recent_inventory:
                print(f"  - {inv['name']} ({inv['sku']}): {inv['quantity']} in {inv['storeroom_name']} ({inv['tenant_name']})")

        except Exception as e:
            print(f"  ✗ Error verifying database records: {e}")

def main():
    """Main function to run the comprehensive procurement workflow test"""
    print("🚀 STARTING COMPREHENSIVE PROCUREMENT WORKFLOW TESTING (DATABASE DIRECT)")
    print("="*80)

    tester = DirectProcurementTester()

    # Setup test environment
    if not tester.setup_test_environment():
        print("✗ Failed to setup test environment")
        return

    # Run complete workflow test
    tester.test_complete_workflow()

    print("\n🎉 TESTING COMPLETED SUCCESSFULLY!")

if __name__ == "__main__":
    main()
