import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Tabs, Tab, Table, Button, Modal, Form,
  Alert, Spinner, Badge, InputGroup, FormControl
} from 'react-bootstrap';
import {
  FaUsers, FaFileInvoiceDollar, FaMoneyBillWave, FaChartLine,
  FaPlus, FaEdit, FaEye, FaSearch, FaDownload, FaExclamationTriangle
} from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const AccountsReceivable = () => {
  const [activeTab, setActiveTab] = useState('customers');
  const [customers, setCustomers] = useState([]);
  const [salesInvoices, setSalesInvoices] = useState([]);
  const [payments, setPayments] = useState([]);
  const [agedReceivables, setAgedReceivables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = 1; // Get from context

      switch (activeTab) {
        case 'customers':
          const customersData = await accountingService.getCustomers(tenantId);
          setCustomers(customersData.customers || []);
          break;
        case 'invoices':
          const invoicesData = await accountingService.getSalesInvoices(tenantId);
          setSalesInvoices(invoicesData.invoices || []);
          break;
        case 'payments':
          const paymentsData = await accountingService.getCustomerPayments(tenantId);
          setPayments(paymentsData.payments || []);
          break;
        case 'aged-receivables':
          const agedData = await accountingService.getAgedReceivables(tenantId);
          setAgedReceivables(agedData.receivables || []);
          break;
        default:
          break;
      }
    } catch (err) {
      setError(`Failed to load ${activeTab} data: ${err.message}`);
      console.error('AR data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleShowModal = (type, item = null) => {
    setModalType(type);
    setSelectedItem(item);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedItem(null);
    setModalType('');
  };

  const handleSave = async (formData) => {
    try {
      const tenantId = 1;

      switch (modalType) {
        case 'customer':
          if (selectedItem) {
            await accountingService.updateCustomer(selectedItem.id, formData);
          } else {
            await accountingService.createCustomer({ ...formData, tenant_id: tenantId });
          }
          break;
        case 'invoice':
          if (selectedItem) {
            await accountingService.updateSalesInvoice(selectedItem.id, formData);
          } else {
            await accountingService.createSalesInvoice({ ...formData, tenant_id: tenantId });
          }
          break;
        case 'payment':
          await accountingService.createCustomerPayment({ ...formData, tenant_id: tenantId });
          break;
        default:
          break;
      }

      handleCloseModal();
      loadData();
    } catch (err) {
      setError(`Failed to save: ${err.message}`);
    }
  };

  const filteredData = (data) => {
    if (!searchTerm) return data;
    return data.filter(item =>
      Object.values(item).some(value =>
        value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  };

  const CustomersTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text><FaSearch /></InputGroup.Text>
            <FormControl
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="primary"
            onClick={() => handleShowModal('customer')}
          >
            <FaPlus className="me-2" />
            Add Customer
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Customer Code</th>
              <th>Customer Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Credit Limit</th>
              <th>Outstanding</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredData(customers).map((customer) => (
              <tr key={customer.id}>
                <td>{customer.customer_code}</td>
                <td>{customer.customer_name}</td>
                <td>{customer.email}</td>
                <td>{customer.phone}</td>
                <td>₹{customer.credit_limit?.toLocaleString()}</td>
                <td>₹{customer.outstanding_amount?.toLocaleString()}</td>
                <td>
                  <Badge bg={customer.is_active ? 'success' : 'secondary'}>
                    {customer.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </td>
                <td>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-2"
                    onClick={() => handleShowModal('customer', customer)}
                  >
                    <FaEdit />
                  </Button>
                  <Button
                    variant="outline-info"
                    size="sm"
                    onClick={() => handleShowModal('view-customer', customer)}
                  >
                    <FaEye />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  const InvoicesTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text><FaSearch /></InputGroup.Text>
            <FormControl
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="primary"
            onClick={() => handleShowModal('invoice')}
          >
            <FaPlus className="me-2" />
            Create Invoice
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Invoice #</th>
              <th>Customer</th>
              <th>Date</th>
              <th>Due Date</th>
              <th>Amount</th>
              <th>Outstanding</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredData(salesInvoices).map((invoice) => (
              <tr key={invoice.id}>
                <td>{invoice.invoice_number}</td>
                <td>{invoice.customer_name}</td>
                <td>{new Date(invoice.invoice_date).toLocaleDateString()}</td>
                <td>{new Date(invoice.due_date).toLocaleDateString()}</td>
                <td>₹{invoice.total_amount?.toLocaleString()}</td>
                <td>₹{invoice.outstanding_amount?.toLocaleString()}</td>
                <td>
                  <Badge bg={
                    invoice.status === 'PAID' ? 'success' :
                    invoice.status === 'PARTIALLY_PAID' ? 'warning' :
                    invoice.status === 'OVERDUE' ? 'danger' : 'primary'
                  }>
                    {invoice.status}
                  </Badge>
                </td>
                <td>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-2"
                    onClick={() => handleShowModal('invoice', invoice)}
                  >
                    <FaEdit />
                  </Button>
                  <Button
                    variant="outline-info"
                    size="sm"
                    onClick={() => handleShowModal('view-invoice', invoice)}
                  >
                    <FaEye />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  const PaymentsTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text><FaSearch /></InputGroup.Text>
            <FormControl
              placeholder="Search payments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="success"
            onClick={() => handleShowModal('payment')}
          >
            <FaPlus className="me-2" />
            Record Payment
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Payment #</th>
              <th>Customer</th>
              <th>Date</th>
              <th>Amount</th>
              <th>Method</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredData(payments).map((payment) => (
              <tr key={payment.id}>
                <td>{payment.payment_number}</td>
                <td>{payment.customer_name}</td>
                <td>{new Date(payment.payment_date).toLocaleDateString()}</td>
                <td>₹{payment.total_amount?.toLocaleString()}</td>
                <td>{payment.payment_method}</td>
                <td>
                  <Badge bg={payment.status === 'PROCESSED' ? 'success' : 'warning'}>
                    {payment.status}
                  </Badge>
                </td>
                <td>
                  <Button
                    variant="outline-info"
                    size="sm"
                    onClick={() => handleShowModal('view-payment', payment)}
                  >
                    <FaEye />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  const AgedReceivablesTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <h5>Aged Receivables Analysis</h5>
        </Col>
        <Col md={6} className="text-end">
          <Button variant="outline-primary">
            <FaDownload className="me-2" />
            Export Report
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Customer</th>
              <th>Invoice #</th>
              <th>Due Date</th>
              <th>Amount</th>
              <th>Days Overdue</th>
              <th>Aging Bucket</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {agedReceivables.map((item, index) => (
              <tr key={index}>
                <td>{item.customer_name}</td>
                <td>{item.invoice_number}</td>
                <td>{new Date(item.due_date).toLocaleDateString()}</td>
                <td>₹{item.outstanding_amount?.toLocaleString()}</td>
                <td>
                  {item.days_overdue > 0 && (
                    <Badge bg="danger">
                      <FaExclamationTriangle className="me-1" />
                      {Math.floor(item.days_overdue)} days
                    </Badge>
                  )}
                </td>
                <td>
                  <Badge bg={
                    item.aging_bucket === 'Current' ? 'success' :
                    item.aging_bucket === '1-30 Days' ? 'warning' :
                    item.aging_bucket === '31-60 Days' ? 'danger' :
                    'dark'
                  }>
                    {item.aging_bucket}
                  </Badge>
                </td>
                <td>
                  <Button
                    variant="outline-warning"
                    size="sm"
                    onClick={() => handleShowModal('dunning-letter', item)}
                  >
                    Send Reminder
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  const CustomerModal = () => {
    const [formData, setFormData] = useState({
      customer_name: selectedItem?.customer_name || '',
      email: selectedItem?.email || '',
      phone: selectedItem?.phone || '',
      address: selectedItem?.address || '',
      credit_limit: selectedItem?.credit_limit || 0,
      payment_terms: selectedItem?.payment_terms || 'NET_30',
      is_active: selectedItem?.is_active ?? true
    });

    const handleSubmit = (e) => {
      e.preventDefault();
      handleSave(formData);
    };

    return (
      <Modal show={showModal && modalType === 'customer'} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedItem ? 'Edit Customer' : 'Add New Customer'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Customer Name *</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.customer_name}
                    onChange={(e) => setFormData({...formData, customer_name: e.target.value})}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Credit Limit</Form.Label>
                  <Form.Control
                    type="number"
                    value={formData.credit_limit}
                    onChange={(e) => setFormData({...formData, credit_limit: parseFloat(e.target.value)})}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Address</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={formData.address}
                onChange={(e) => setFormData({...formData, address: e.target.value})}
              />
            </Form.Group>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Payment Terms</Form.Label>
                  <Form.Select
                    value={formData.payment_terms}
                    onChange={(e) => setFormData({...formData, payment_terms: e.target.value})}
                  >
                    <option value="IMMEDIATE">Immediate</option>
                    <option value="NET_15">Net 15 Days</option>
                    <option value="NET_30">Net 30 Days</option>
                    <option value="NET_45">Net 45 Days</option>
                    <option value="NET_60">Net 60 Days</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Active Customer"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {selectedItem ? 'Update' : 'Create'} Customer
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    );
  };

  return (
    <div className="accounts-receivable">
      <Card>
        <Card.Header>
          <Row>
            <Col>
              <h5 className="mb-0 text-primary">
                <FaUsers className="me-2" />
                Accounts Receivable
              </h5>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body style={{background:"#f8f9fa"}}> 
          {error && <Alert variant="danger">{error}</Alert>}

          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-3"
          >
            <Tab eventKey="customers" title="Customers">
              <CustomersTab />
            </Tab>
            <Tab eventKey="invoices" title="Sales Invoices">
              <InvoicesTab />
            </Tab>
            <Tab eventKey="payments" title="Payments">
              <PaymentsTab />
            </Tab>
            <Tab eventKey="aged-receivables" title="Aged Receivables">
              <AgedReceivablesTab />
            </Tab>
          </Tabs>
        </Card.Body>
      </Card>

      <CustomerModal />
    </div>
  );
};

export default AccountsReceivable;
