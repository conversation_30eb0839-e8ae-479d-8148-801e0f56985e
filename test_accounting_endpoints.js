const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5002/api/accounting';
const TENANT_ID = 1;

// Mock authentication token (you'll need to get a real one)
const AUTH_TOKEN = 'your-auth-token-here';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testEndpoint(method, endpoint, params = {}, data = null) {
  try {
    console.log(`\n🧪 Testing ${method.toUpperCase()} ${endpoint}`);
    
    let response;
    if (method === 'GET') {
      response = await api.get(endpoint, { params });
    } else if (method === 'POST') {
      response = await api.post(endpoint, data, { params });
    }
    
    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   📊 Data keys: ${Object.keys(response.data).join(', ')}`);
    return true;
  } catch (error) {
    if (error.response) {
      console.log(`   ❌ Status: ${error.response.status} - ${error.response.statusText}`);
      if (error.response.data && error.response.data.error) {
        console.log(`   💬 Error: ${error.response.data.error}`);
      }
    } else {
      console.log(`   ❌ Network Error: ${error.message}`);
    }
    return false;
  }
}

async function runTests() {
  console.log('🚀 TESTING ACCOUNTING SYSTEM ENDPOINTS');
  console.log('=' * 50);
  
  const tests = [
    // Core endpoints
    { method: 'GET', endpoint: '/health' },
    { method: 'GET', endpoint: '/chart-of-accounts', params: { tenant_id: TENANT_ID } },
    { method: 'GET', endpoint: '/journal-entries', params: { tenant_id: TENANT_ID, page: 1, per_page: 10 } },
    
    // Customer management
    { method: 'GET', endpoint: '/customers', params: { tenant_id: TENANT_ID } },
    { method: 'GET', endpoint: '/vendors', params: { tenant_id: TENANT_ID } },
    
    // Sales and payments
    { method: 'GET', endpoint: '/sales-invoices', params: { tenant_id: TENANT_ID } },
    { method: 'GET', endpoint: '/customer-payments', params: { tenant_id: TENANT_ID } },
    { method: 'GET', endpoint: '/aged-receivables', params: { tenant_id: TENANT_ID } },
    
    // Financial reports
    { method: 'GET', endpoint: '/reports/trial-balance', params: { tenant_id: TENANT_ID, as_of_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/reports/profit-loss', params: { tenant_id: TENANT_ID, start_date: '2025-01-01', end_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/reports/balance-sheet', params: { tenant_id: TENANT_ID, as_of_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/reports/cash-flow', params: { tenant_id: TENANT_ID, start_date: '2025-01-01', end_date: '2025-09-16' } },
    
    // Tax management
    { method: 'GET', endpoint: '/tax/gst-dashboard', params: { tenant_id: TENANT_ID, start_date: '2025-08-01', end_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/tax/gst-returns', params: { tenant_id: TENANT_ID, start_date: '2025-08-01', end_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/tax/tds-data', params: { tenant_id: TENANT_ID, start_date: '2025-08-01', end_date: '2025-09-16' } },
    
    // Inventory accounting
    { method: 'GET', endpoint: '/inventory/valuation', params: { tenant_id: TENANT_ID, costing_method: 'FIFO' } },
    { method: 'GET', endpoint: '/inventory/movements', params: { tenant_id: TENANT_ID, start_date: '2025-08-01', end_date: '2025-09-16' } },
    { method: 'GET', endpoint: '/inventory/cogs-analysis', params: { tenant_id: TENANT_ID, start_date: '2025-08-01', end_date: '2025-09-16' } },
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    const success = await testEndpoint(test.method, test.endpoint, test.params, test.data);
    if (success) {
      passed++;
    } else {
      failed++;
    }
  }
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Accounting system is fully operational.');
  } else {
    console.log('\n⚠️  Some endpoints need attention. Check the errors above.');
  }
}

// Run the tests
runTests().catch(console.error);
