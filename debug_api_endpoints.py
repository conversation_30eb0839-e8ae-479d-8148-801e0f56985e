#!/usr/bin/env python3
"""
Test script to check if the API endpoints are working
"""
import requests
import json
import sys

def test_server_connection():
    """Test if the backend server is running"""
    try:
        print("🔍 Testing server connection...")
        response = requests.get('http://localhost:5002/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running on port 5002")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running on port 5002")
        return False
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
        return False

def test_delivery_notes_endpoint():
    """Test the delivery notes API endpoint"""
    try:
        print("\n🔍 Testing delivery notes endpoint...")
        
        # First, let's try without authentication to see what happens
        response = requests.get('http://localhost:5002/api/procurement/delivery-notes', timeout=10)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 401:
            print("⚠️  Authentication required (expected)")
            print("📄 Response:", response.text[:200])
            return True  # This is expected
        elif response.status_code == 200:
            print("✅ Endpoint accessible without auth")
            data = response.json()
            print(f"📄 Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print("📄 Response:", response.text[:200])
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to delivery notes endpoint")
        return False
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def test_frontend_server():
    """Test if the frontend server is running"""
    try:
        print("\n🔍 Testing frontend server...")
        response = requests.get('http://localhost:3001', timeout=5)
        if response.status_code == 200:
            print("✅ Frontend server is running on port 3001")
            return True
        else:
            print(f"⚠️  Frontend responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend server is not running on port 3001")
        return False
    except Exception as e:
        print(f"❌ Error connecting to frontend: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting API Endpoint Debug Test")
    print("=" * 50)
    
    tests = [
        ("Backend Server Connection", test_server_connection),
        ("Frontend Server Connection", test_frontend_server),
        ("Delivery Notes Endpoint", test_delivery_notes_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    # Provide recommendations
    print("\n💡 Recommendations:")
    if not results[0][1]:  # Backend server not running
        print("  1. Start the backend server: cd backend && python app.py")
    if not results[1][1]:  # Frontend server not running
        print("  2. Start the frontend server: npm start")
    if results[0][1] and results[1][1]:  # Both servers running
        print("  1. Check browser console for detailed error messages")
        print("  2. Check browser network tab for failed requests")
        print("  3. Verify authentication token in browser localStorage")

if __name__ == "__main__":
    main()
