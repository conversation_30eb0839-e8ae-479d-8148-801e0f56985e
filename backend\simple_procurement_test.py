#!/usr/bin/env python3
"""
Simple Procurement Database Test
"""

import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database_manager import db_manager
    print("✓ Database manager imported successfully")
except Exception as e:
    print(f"✗ Error importing database manager: {e}")
    sys.exit(1)

def test_database_connection():
    """Test basic database connectivity"""
    print("\n=== TESTING DATABASE CONNECTION ===")
    
    try:
        # Test basic query
        result = db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        print(f"✓ Database connection successful")
        print(f"✓ Found {len(result)} tables in database")
        
        # List tables
        for table in result:
            print(f"  - {table['name']}")
        
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def setup_procurement_tables():
    """Setup procurement tables"""
    print("\n=== SETTING UP PROCUREMENT TABLES ===")
    
    try:
        # Create basic procurement tables
        tables_sql = [
            """
            CREATE TABLE IF NOT EXISTS purchase_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_number TEXT UNIQUE NOT NULL,
                requesting_tenant_id INTEGER NOT NULL,
                hub_tenant_id INTEGER NOT NULL,
                priority TEXT DEFAULT 'medium',
                status TEXT DEFAULT 'draft',
                request_date DATE NOT NULL,
                required_date DATE NOT NULL,
                notes TEXT,
                total_estimated_amount DECIMAL(12,2) DEFAULT 0,
                storeroom_id INTEGER,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS purchase_request_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_request_id INTEGER NOT NULL,
                item_name TEXT NOT NULL,
                description TEXT,
                requested_quantity INTEGER NOT NULL,
                unit TEXT NOT NULL,
                estimated_unit_price DECIMAL(10,2) DEFAULT 0,
                total_estimated_amount DECIMAL(12,2) DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id) ON DELETE CASCADE
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_number TEXT UNIQUE NOT NULL,
                supplier_id INTEGER,
                tenant_id INTEGER NOT NULL,
                status TEXT DEFAULT 'draft',
                order_date DATE NOT NULL,
                expected_delivery_date DATE,
                subtotal DECIMAL(12,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(12,2) DEFAULT 0,
                total_amount DECIMAL(12,2) DEFAULT 0,
                payment_terms TEXT,
                delivery_address TEXT,
                notes TEXT,
                purchase_request_id INTEGER,
                received_at TIMESTAMP,
                received_by INTEGER,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_order_id INTEGER NOT NULL,
                item_name TEXT NOT NULL,
                description TEXT,
                quantity INTEGER NOT NULL,
                unit TEXT NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                total_amount DECIMAL(12,2) NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                sku TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                description TEXT,
                quantity INTEGER DEFAULT 0,
                unit TEXT NOT NULL,
                reorder_level INTEGER DEFAULT 0,
                cost_price DECIMAL(10,2) DEFAULT 0,
                selling_price DECIMAL(10,2) DEFAULT 0,
                supplier TEXT,
                location TEXT,
                expiry_date DATE,
                storeroom_id INTEGER,
                tenant_id INTEGER NOT NULL,
                batch_number TEXT,
                barcode TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS inventory_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                inventory_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                reason TEXT NOT NULL,
                notes TEXT,
                unit_cost DECIMAL(10,2) DEFAULT 0,
                total_cost DECIMAL(12,2) DEFAULT 0,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (inventory_id) REFERENCES inventory(id)
            )
            """
        ]
        
        for sql in tables_sql:
            db_manager.execute_query(sql)
        
        print("✓ Procurement tables created successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error creating procurement tables: {e}")
        return False

def test_procurement_workflow():
    """Test basic procurement workflow"""
    print("\n=== TESTING PROCUREMENT WORKFLOW ===")
    
    try:
        # Step 1: Create a purchase request
        print("\n1. Creating Purchase Request...")
        pr_number = f"PR-TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        pr_insert = """
            INSERT INTO purchase_requests (
                request_number, requesting_tenant_id, hub_tenant_id, priority, status,
                request_date, required_date, notes, total_estimated_amount, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        pr_id = db_manager.execute_query(pr_insert, (
            pr_number, 2, 1, 'high', 'draft',
            datetime.now().date().isoformat(),
            (datetime.now() + timedelta(days=7)).date().isoformat(),
            'Test purchase request for workflow validation',
            575.00, 1
        ))
        
        print(f"✓ Purchase Request created: {pr_number} (ID: {pr_id})")
        
        # Step 2: Add items to purchase request
        print("\n2. Adding Items to Purchase Request...")
        items = [
            ('Blood Collection Tubes', 'EDTA tubes', 50, 'pieces', 2.50, 125.00),
            ('Reagent Kit - Glucose', 'Glucose testing kit', 10, 'kits', 45.00, 450.00)
        ]
        
        for item in items:
            item_insert = """
                INSERT INTO purchase_request_items (
                    purchase_request_id, item_name, description, requested_quantity, 
                    unit, estimated_unit_price, total_estimated_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            db_manager.execute_query(item_insert, (pr_id,) + item)
        
        print(f"✓ Added {len(items)} items to purchase request")
        
        # Step 3: Approve purchase request
        print("\n3. Approving Purchase Request...")
        approve_query = """
            UPDATE purchase_requests 
            SET status = 'approved', updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        """
        db_manager.execute_query(approve_query, (pr_id,))
        print(f"✓ Purchase Request {pr_id} approved")
        
        # Step 4: Create purchase order
        print("\n4. Creating Purchase Order...")
        po_number = f"PO-TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        po_insert = """
            INSERT INTO purchase_orders (
                po_number, tenant_id, status, order_date, expected_delivery_date,
                subtotal, tax_rate, tax_amount, total_amount, purchase_request_id, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        subtotal = 575.00
        tax_rate = 18.0
        tax_amount = subtotal * (tax_rate / 100)
        total_amount = subtotal + tax_amount
        
        po_id = db_manager.execute_query(po_insert, (
            po_number, 1, 'draft', datetime.now().date().isoformat(),
            (datetime.now() + timedelta(days=7)).date().isoformat(),
            subtotal, tax_rate, tax_amount, total_amount, pr_id, 1
        ))
        
        print(f"✓ Purchase Order created: {po_number} (ID: {po_id})")
        
        # Step 5: Add items to purchase order
        print("\n5. Adding Items to Purchase Order...")
        for item in items:
            po_item_insert = """
                INSERT INTO purchase_order_items (
                    purchase_order_id, item_name, description, quantity, 
                    unit, unit_price, total_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            db_manager.execute_query(po_item_insert, (po_id,) + item)
        
        print(f"✓ Added {len(items)} items to purchase order")
        
        # Step 6: Create inventory items
        print("\n6. Creating Inventory Items...")
        for i, item in enumerate(items):
            inv_insert = """
                INSERT INTO inventory (
                    name, sku, category, quantity, unit, reorder_level,
                    cost_price, tenant_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            sku = f"TEST-{i+1:03d}-{datetime.now().strftime('%Y%m%d')}"
            
            inv_id = db_manager.execute_query(inv_insert, (
                item[0], sku, 'Test Category', 0, item[3], 10,
                item[4], 1, 1
            ))
            
            # Add inventory transaction
            trans_insert = """
                INSERT INTO inventory_transactions (
                    inventory_id, transaction_type, quantity, reference_type, 
                    reference_id, reason, unit_cost, total_cost, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            db_manager.execute_query(trans_insert, (
                inv_id, 'in', item[2], 'purchase_order', po_id,
                'Goods received from purchase order', item[4], item[5], 1
            ))
            
            # Update inventory quantity
            update_inv = "UPDATE inventory SET quantity = ? WHERE id = ?"
            db_manager.execute_query(update_inv, (item[2], inv_id))
        
        print(f"✓ Created {len(items)} inventory items with transactions")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in procurement workflow: {e}")
        return False

def verify_records():
    """Verify all records were created"""
    print("\n=== VERIFYING CREATED RECORDS ===")
    
    try:
        # Check purchase requests
        pr_count = db_manager.execute_query("SELECT COUNT(*) as count FROM purchase_requests")[0]['count']
        print(f"✓ Purchase Requests: {pr_count}")
        
        # Check purchase orders
        po_count = db_manager.execute_query("SELECT COUNT(*) as count FROM purchase_orders")[0]['count']
        print(f"✓ Purchase Orders: {po_count}")
        
        # Check inventory
        inv_count = db_manager.execute_query("SELECT COUNT(*) as count FROM inventory")[0]['count']
        print(f"✓ Inventory Items: {inv_count}")
        
        # Check inventory transactions
        trans_count = db_manager.execute_query("SELECT COUNT(*) as count FROM inventory_transactions")[0]['count']
        print(f"✓ Inventory Transactions: {trans_count}")
        
        # Show recent records
        print(f"\n📝 RECENT RECORDS:")
        
        recent_prs = db_manager.execute_query("""
            SELECT request_number, status, total_estimated_amount 
            FROM purchase_requests 
            ORDER BY created_at DESC LIMIT 3
        """)
        for pr in recent_prs:
            print(f"  PR: {pr['request_number']} - {pr['status']} (${pr['total_estimated_amount']})")
        
        recent_pos = db_manager.execute_query("""
            SELECT po_number, status, total_amount 
            FROM purchase_orders 
            ORDER BY created_at DESC LIMIT 3
        """)
        for po in recent_pos:
            print(f"  PO: {po['po_number']} - {po['status']} (${po['total_amount']})")
        
        recent_inv = db_manager.execute_query("""
            SELECT name, sku, quantity 
            FROM inventory 
            ORDER BY created_at DESC LIMIT 3
        """)
        for inv in recent_inv:
            print(f"  INV: {inv['name']} ({inv['sku']}) - Qty: {inv['quantity']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error verifying records: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 STARTING SIMPLE PROCUREMENT DATABASE TEST")
    print("="*60)
    
    # Test database connection
    if not test_database_connection():
        return
    
    # Setup tables
    if not setup_procurement_tables():
        return
    
    # Test workflow
    if not test_procurement_workflow():
        return
    
    # Verify records
    if not verify_records():
        return
    
    print("\n✅ ALL TESTS PASSED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
