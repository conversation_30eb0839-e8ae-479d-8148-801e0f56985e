const axios = require('axios');

const baseURL = 'http://localhost:5002';

// Test routes that are returning 404 in frontend
const testRoutes = [
    '/api/accounting/sales-invoices?tenant_id=1',
    '/api/accounting/customer-payments?tenant_id=1',
    '/api/accounting/aged-receivables?tenant_id=1',
    '/api/accounting/tax/gst-dashboard?tenant_id=1&start_date=2025-08-31&end_date=2025-09-16',
    '/api/accounting/inventory/valuation?tenant_id=1&costing_method=FIFO',
    '/api/accounting/reports/cash-flow?tenant_id=1&start_date=2024-12-31&end_date=2025-09-16',
    '/api/accounting/tax/gst-returns?tenant_id=1&start_date=2025-08-31&end_date=2025-09-16',
    '/api/accounting/tax/tds-data?tenant_id=1&start_date=2025-08-31&end_date=2025-09-16'
];

async function testRoute(route) {
    try {
        const response = await axios.get(baseURL + route, {
            timeout: 5000,
            validateStatus: function (status) {
                return status < 500; // Accept any status code less than 500
            }
        });
        console.log(`✅ ${route} - Status: ${response.status}`);
        return response.status;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log(`❌ ${route} - Server not running`);
            return 'ECONNREFUSED';
        } else if (error.response) {
            console.log(`⚠️  ${route} - Status: ${error.response.status}`);
            return error.response.status;
        } else {
            console.log(`❌ ${route} - Error: ${error.message}`);
            return 'ERROR';
        }
    }
}

async function testAllRoutes() {
    console.log('🧪 Testing Accounting Routes Directly');
    console.log('=' + '='.repeat(50));
    
    for (const route of testRoutes) {
        await testRoute(route);
    }
    
    console.log('\n📊 Summary:');
    console.log('- 401 = Route exists, needs authentication');
    console.log('- 404 = Route not found');
    console.log('- 500 = Route exists but has server error');
    console.log('- ECONNREFUSED = Server not running');
}

testAllRoutes();
