import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, <PERSON>, Badge, <PERSON><PERSON>, Al<PERSON>, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faClipboardList, 
  faArrowLeft, 
  faCalendar, 
  faUser, 
  faBoxes,
  faExclamationTriangle,
  faEdit,
  faCheck,
  faTimes,
  faBuilding,
  faWarehouse
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';

const PurchaseRequestDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [purchaseRequest, setPurchaseRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPurchaseRequest();
  }, [id]);

  const loadPurchaseRequest = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getPurchaseRequest(id);
      setPurchaseRequest(response.data);
    } catch (err) {
      console.error('Error loading purchase request:', err);
      setError(err.response?.data?.message || 'Failed to load purchase request');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'secondary';
      case 'submitted': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'processing': return 'info';
      case 'completed': return 'success';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'danger';
      case 'urgent': return 'danger';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading purchase request details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/requests')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Purchase Requests
        </Button>
      </Container>
    );
  }

  if (!purchaseRequest) {
    return (
      <Container fluid className="py-4">
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Purchase request not found
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/requests')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Purchase Requests
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/procurement/requests')}
            className="mb-2"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Purchase Requests
          </Button>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faClipboardList} className="me-2 text-primary" />
            Purchase Request Details
          </h2>
          <p className="text-muted mb-0">View purchase request information and items</p>
        </div>
        <div>
          <Badge bg={getStatusBadgeVariant(purchaseRequest.status)} className="fs-6 me-2">
            {purchaseRequest.status?.toUpperCase()}
          </Badge>
          <Badge bg={getPriorityBadgeVariant(purchaseRequest.priority)} className="fs-6">
            {purchaseRequest.priority?.toUpperCase()} PRIORITY
          </Badge>
        </div>
      </div>

      <Row>
        {/* Purchase Request Information */}
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faClipboardList} className="me-2" />
                Request Information
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Request Number</label>
                    <div className="fw-semibold">{purchaseRequest.request_number}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Request Date
                    </label>
                    <div>{new Date(purchaseRequest.request_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Required Date
                    </label>
                    <div>{new Date(purchaseRequest.required_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Priority</label>
                    <div>
                      <Badge bg={getPriorityBadgeVariant(purchaseRequest.priority)}>
                        {purchaseRequest.priority?.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Status</label>
                    <div>
                      <Badge bg={getStatusBadgeVariant(purchaseRequest.status)}>
                        {purchaseRequest.status?.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faBuilding} className="me-1" />
                      Requesting Tenant
                    </label>
                    <div>{purchaseRequest.requesting_tenant_name || 'N/A'}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faWarehouse} className="me-1" />
                      Storeroom
                    </label>
                    <div>{purchaseRequest.storeroom_name || 'N/A'}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Estimated Amount</label>
                    <div className="fw-semibold text-success">
                      ₹{parseFloat(purchaseRequest.total_estimated_amount || 0).toLocaleString()}
                    </div>
                  </div>
                </Col>
              </Row>
              
              {purchaseRequest.notes && (
                <div className="mt-3">
                  <label className="form-label text-muted">Notes</label>
                  <div className=" p-3 rounded">{purchaseRequest.notes}</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary Card */}
        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faBoxes} className="me-2" />
                Summary
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Items:</span>
                <span className="fw-semibold">{purchaseRequest.items?.length || 0}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Quantity:</span>
                <span className="fw-semibold">
                  {purchaseRequest.items?.reduce((sum, item) => sum + (item.requested_quantity || 0), 0) || 0}
                </span>
              </div>
              <hr />
              <div className="d-flex justify-content-between">
                <span>Estimated Amount:</span>
                <span className="fw-semibold text-success">
                  ₹{parseFloat(purchaseRequest.total_estimated_amount || 0).toLocaleString()}
                </span>
              </div>
              
              {purchaseRequest.created_at && (
                <div className="mt-3 pt-3 border-top">
                  <small className="text-muted">
                    <FontAwesomeIcon icon={faUser} className="me-1" />
                    Created: {new Date(purchaseRequest.created_at).toLocaleString()}
                  </small>
                  {purchaseRequest.created_by_username && (
                    <div className="mt-1">
                      <small className="text-muted">
                        By: {purchaseRequest.created_by_username}
                      </small>
                    </div>
                  )}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Request Items */}
      <Card>
        <Card.Header className="text-primary">
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faBoxes} className="me-2" />
            Requested Items
          </h5>
        </Card.Header>
        <Card.Body className="p-0" style={{ backgroundColor: '#f8f9fa' }}>
          {purchaseRequest.items && purchaseRequest.items.length > 0 ? (
            <div className="table-responsive">
              <Table striped hover className="mb-0">
                <thead className="table-dark">
                  <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>Requested Qty</th>
                    <th>Unit</th>
                    <th>Est. Unit Price</th>
                    <th>Est. Total</th>
                  </tr>
                </thead>
                <tbody>
                  {purchaseRequest.items.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="fw-semibold">{item.item_name}</td>
                      <td>{item.item_description || '-'}</td>
                      <td>{item.requested_quantity}</td>
                      <td>{item.unit}</td>
                      <td>₹{parseFloat(item.estimated_unit_price || 0).toFixed(2)}</td>
                      <td className="fw-semibold">₹{parseFloat(item.estimated_total_price || 0).toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faBoxes} className="text-muted fa-2x mb-3" />
              <p className="text-muted">No items found for this purchase request</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default PurchaseRequestDetail;
