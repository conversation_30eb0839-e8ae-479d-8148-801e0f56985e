"""
AVINI Labs Accounts Payable Service
Manages vendor invoices, payments, and payable transactions
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
from decimal import Decimal

from database_manager import db_manager
from services.accounting_service import AccountingService

logger = logging.getLogger(__name__)

class AccountsPayableService:
    """Service for managing accounts payable operations"""
    
    def __init__(self):
        self.db = db_manager
        self.accounting_service = AccountingService()
    
    # ============================================================================
    # VENDOR MANAGEMENT
    # ============================================================================
    
    def create_vendor(self, vendor_data: Dict) -> int:
        """Create a new vendor"""
        try:
            # Validate required fields
            required_fields = ['vendor_name', 'tenant_id']
            for field in required_fields:
                if field not in vendor_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate vendor code if not provided
            if 'vendor_code' not in vendor_data:
                vendor_data['vendor_code'] = self._generate_vendor_code(vendor_data['tenant_id'])
            
            # Set defaults
            vendor_data.setdefault('vendor_type', 'SUPPLIER')
            vendor_data.setdefault('payment_terms', 'NET_30')
            vendor_data.setdefault('credit_limit', 0)
            vendor_data.setdefault('credit_days', 30)
            vendor_data.setdefault('is_active', True)
            vendor_data.setdefault('country', 'India')
            
            vendor_id = self.db.insert_record('vendors', vendor_data)
            logger.info(f"Created vendor {vendor_data['vendor_code']} with ID {vendor_id}")
            return vendor_id
            
        except Exception as e:
            logger.error(f"Error creating vendor: {str(e)}")
            raise
    
    def get_vendor_by_code(self, vendor_code: str, tenant_id: int) -> Optional[Dict]:
        """Get vendor by vendor code"""
        try:
            vendors = self.db.execute_query(
                "SELECT * FROM vendors WHERE vendor_code = ? AND tenant_id = ?",
                (vendor_code, tenant_id)
            )
            return vendors[0] if vendors else None
        except Exception as e:
            logger.error(f"Error getting vendor by code {vendor_code}: {str(e)}")
            raise
    
    def get_active_vendors(self, tenant_id: int) -> List[Dict]:
        """Get all active vendors for a tenant"""
        try:
            return self.db.execute_query(
                "SELECT * FROM vendors WHERE tenant_id = ? AND is_active = 1 ORDER BY vendor_name",
                (tenant_id,)
            )
        except Exception as e:
            logger.error(f"Error getting active vendors: {str(e)}")
            raise
    
    # ============================================================================
    # PURCHASE INVOICE MANAGEMENT
    # ============================================================================
    
    def create_purchase_invoice(self, invoice_data: Dict, line_items: List[Dict]) -> int:
        """Create a new purchase invoice"""
        try:
            # Validate required fields
            required_fields = ['vendor_id', 'invoice_date', 'total_amount', 'tenant_id']
            for field in required_fields:
                if field not in invoice_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate invoice number if not provided
            if 'invoice_number' not in invoice_data:
                invoice_data['invoice_number'] = self._generate_invoice_number('PURCHASE', invoice_data['tenant_id'])
            
            # Calculate due date if not provided
            if 'due_date' not in invoice_data:
                vendor = self.db.execute_query("SELECT credit_days FROM vendors WHERE id = ?", (invoice_data['vendor_id'],))
                credit_days = vendor[0]['credit_days'] if vendor else 30
                due_date = datetime.strptime(invoice_data['invoice_date'], '%Y-%m-%d') + timedelta(days=credit_days)
                invoice_data['due_date'] = due_date.strftime('%Y-%m-%d')
            
            # Set defaults
            invoice_data.setdefault('status', 'DRAFT')
            invoice_data.setdefault('subtotal', 0)
            invoice_data.setdefault('tax_amount', 0)
            invoice_data.setdefault('discount_amount', 0)
            invoice_data.setdefault('paid_amount', 0)
            invoice_data.setdefault('currency', 'INR')
            invoice_data.setdefault('exchange_rate', 1.0000)
            
            # Calculate outstanding amount
            invoice_data['outstanding_amount'] = invoice_data['total_amount'] - invoice_data['paid_amount']
            
            # Insert invoice
            invoice_id = self.db.insert_record('purchase_invoices', invoice_data)
            
            # Insert line items
            for i, line_item in enumerate(line_items, 1):
                line_item.update({
                    'purchase_invoice_id': invoice_id,
                    'line_number': i
                })
                self.db.insert_record('purchase_invoice_lines', line_item)
            
            logger.info(f"Created purchase invoice {invoice_data['invoice_number']} with ID {invoice_id}")
            return invoice_id
            
        except Exception as e:
            logger.error(f"Error creating purchase invoice: {str(e)}")
            raise
    
    def approve_purchase_invoice(self, invoice_id: int, approved_by: int) -> bool:
        """Approve a purchase invoice and create journal entry"""
        try:
            # Get invoice
            invoice = self.db.execute_query("SELECT * FROM purchase_invoices WHERE id = ?", (invoice_id,))
            if not invoice:
                raise ValueError(f"Purchase invoice with ID {invoice_id} not found")
            
            invoice = invoice[0]
            if invoice['status'] != 'DRAFT':
                raise ValueError("Only draft invoices can be approved")
            
            # Get line items
            line_items = self.db.execute_query(
                "SELECT * FROM purchase_invoice_lines WHERE purchase_invoice_id = ?",
                (invoice_id,)
            )
            
            # Create journal entry
            journal_data = {
                'journal_type': 'GENERAL',
                'transaction_date': invoice['invoice_date'],
                'description': f"Purchase Invoice {invoice['invoice_number']} - {invoice.get('vendor_invoice_number', '')}",
                'reference_type': 'PROCUREMENT',
                'reference_id': invoice_id,
                'reference_number': invoice['invoice_number'],
                'tenant_id': invoice['tenant_id'],
                'created_by': approved_by,
                'is_auto_generated': True
            }
            
            # Create journal entry lines
            journal_lines = []
            
            # Debit expense/asset accounts for line items
            for line_item in line_items:
                if line_item.get('account_id'):
                    journal_lines.append({
                        'account_id': line_item['account_id'],
                        'debit_amount': line_item['line_total'],
                        'credit_amount': 0,
                        'description': line_item['item_description']
                    })
            
            # If no specific accounts, use default expense account
            if not journal_lines:
                # Get default expense account
                expense_account = self.accounting_service.get_account_by_code('5210', invoice['tenant_id'])  # Reagents and Supplies
                if expense_account:
                    journal_lines.append({
                        'account_id': expense_account['id'],
                        'debit_amount': invoice['subtotal'],
                        'credit_amount': 0,
                        'description': f"Purchase Invoice {invoice['invoice_number']}"
                    })
            
            # Add tax if applicable
            if invoice['tax_amount'] > 0:
                tax_account = self.accounting_service.get_account_by_code('1220', invoice['tenant_id'])  # Other Receivables (for input tax)
                if tax_account:
                    journal_lines.append({
                        'account_id': tax_account['id'],
                        'debit_amount': invoice['tax_amount'],
                        'credit_amount': 0,
                        'description': f"Input Tax - {invoice['invoice_number']}"
                    })
            
            # Credit accounts payable
            ap_account = self.accounting_service.get_account_by_code('2110', invoice['tenant_id'])  # Trade Payables
            if ap_account:
                journal_lines.append({
                    'account_id': ap_account['id'],
                    'debit_amount': 0,
                    'credit_amount': invoice['total_amount'],
                    'description': f"Accounts Payable - {invoice['invoice_number']}"
                })
            
            # Create and post journal entry
            journal_id = self.accounting_service.create_journal_entry(journal_data, journal_lines)
            self.accounting_service.post_journal_entry(journal_id, approved_by)
            
            # Update invoice status
            update_data = {
                'status': 'APPROVED',
                'journal_entry_id': journal_id,
                'approved_by': approved_by,
                'approved_at': datetime.now().isoformat()
            }
            self.db.update_record('purchase_invoices', invoice_id, update_data)
            
            logger.info(f"Approved purchase invoice {invoice['invoice_number']}")
            return True
            
        except Exception as e:
            logger.error(f"Error approving purchase invoice {invoice_id}: {str(e)}")
            raise
    
    # ============================================================================
    # VENDOR PAYMENT MANAGEMENT
    # ============================================================================
    
    def create_vendor_payment(self, payment_data: Dict, invoice_allocations: List[Dict]) -> int:
        """Create a vendor payment and allocate to invoices"""
        try:
            # Validate required fields
            required_fields = ['vendor_id', 'payment_date', 'payment_method', 'total_amount', 'tenant_id']
            for field in required_fields:
                if field not in payment_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate payment number if not provided
            if 'payment_number' not in payment_data:
                payment_data['payment_number'] = self._generate_payment_number(payment_data['tenant_id'])
            
            # Set defaults
            payment_data.setdefault('status', 'DRAFT')
            payment_data.setdefault('currency', 'INR')
            payment_data.setdefault('exchange_rate', 1.0000)
            
            # Insert payment
            payment_id = self.db.insert_record('vendor_payments', payment_data)
            
            # Insert allocations
            total_allocated = 0
            for allocation in invoice_allocations:
                allocation.update({
                    'payment_id': payment_id
                })
                self.db.insert_record('vendor_payment_allocations', allocation)
                total_allocated += allocation['allocated_amount']
            
            # Validate total allocation
            if abs(total_allocated - payment_data['total_amount']) > 0.01:
                raise ValueError(f"Total allocated amount {total_allocated} does not match payment amount {payment_data['total_amount']}")
            
            logger.info(f"Created vendor payment {payment_data['payment_number']} with ID {payment_id}")
            return payment_id
            
        except Exception as e:
            logger.error(f"Error creating vendor payment: {str(e)}")
            raise
    
    def process_vendor_payment(self, payment_id: int, processed_by: int) -> bool:
        """Process a vendor payment and create journal entry"""
        try:
            # Get payment
            payment = self.db.execute_query("SELECT * FROM vendor_payments WHERE id = ?", (payment_id,))
            if not payment:
                raise ValueError(f"Vendor payment with ID {payment_id} not found")
            
            payment = payment[0]
            if payment['status'] != 'DRAFT':
                raise ValueError("Only draft payments can be processed")
            
            # Get allocations
            allocations = self.db.execute_query(
                """SELECT vpa.*, pi.invoice_number 
                   FROM vendor_payment_allocations vpa
                   JOIN purchase_invoices pi ON vpa.purchase_invoice_id = pi.id
                   WHERE vpa.payment_id = ?""",
                (payment_id,)
            )
            
            # Create journal entry
            journal_data = {
                'journal_type': 'CASH_PAYMENT' if payment['payment_method'] == 'CASH' else 'BANK_PAYMENT',
                'transaction_date': payment['payment_date'],
                'description': f"Vendor Payment {payment['payment_number']}",
                'reference_type': 'PAYMENT',
                'reference_id': payment_id,
                'reference_number': payment['payment_number'],
                'tenant_id': payment['tenant_id'],
                'created_by': processed_by,
                'is_auto_generated': True
            }
            
            # Create journal entry lines
            journal_lines = []
            
            # Debit accounts payable
            ap_account = self.accounting_service.get_account_by_code('2110', payment['tenant_id'])  # Trade Payables
            if ap_account:
                journal_lines.append({
                    'account_id': ap_account['id'],
                    'debit_amount': payment['total_amount'],
                    'credit_amount': 0,
                    'description': f"Payment to vendor - {payment['payment_number']}"
                })
            
            # Credit cash/bank account
            if payment['payment_method'] == 'CASH':
                cash_account = self.accounting_service.get_account_by_code('1110', payment['tenant_id'])  # Cash in Hand
                if cash_account:
                    journal_lines.append({
                        'account_id': cash_account['id'],
                        'debit_amount': 0,
                        'credit_amount': payment['total_amount'],
                        'description': f"Cash payment - {payment['payment_number']}"
                    })
            else:
                # Use bank account if specified, otherwise default bank account
                bank_account_id = payment.get('bank_account_id')
                if not bank_account_id:
                    bank_account = self.accounting_service.get_account_by_code('1121', payment['tenant_id'])  # Default bank
                    bank_account_id = bank_account['id'] if bank_account else None
                
                if bank_account_id:
                    journal_lines.append({
                        'account_id': bank_account_id,
                        'debit_amount': 0,
                        'credit_amount': payment['total_amount'],
                        'description': f"Bank payment - {payment['payment_number']}"
                    })
            
            # Create and post journal entry
            journal_id = self.accounting_service.create_journal_entry(journal_data, journal_lines)
            self.accounting_service.post_journal_entry(journal_id, processed_by)
            
            # Update payment status
            self.db.update_record('vendor_payments', payment_id, {
                'status': 'PROCESSED',
                'journal_entry_id': journal_id
            })
            
            # Update invoice paid amounts
            for allocation in allocations:
                invoice_id = allocation['purchase_invoice_id']
                allocated_amount = allocation['allocated_amount']
                
                # Get current invoice
                invoice = self.db.execute_query("SELECT * FROM purchase_invoices WHERE id = ?", (invoice_id,))
                if invoice:
                    invoice = invoice[0]
                    new_paid_amount = invoice['paid_amount'] + allocated_amount
                    new_outstanding = invoice['total_amount'] - new_paid_amount
                    
                    # Determine new status
                    if new_outstanding <= 0.01:
                        new_status = 'PAID'
                    elif new_paid_amount > 0:
                        new_status = 'PARTIALLY_PAID'
                    else:
                        new_status = invoice['status']
                    
                    self.db.update_record('purchase_invoices', invoice_id, {
                        'paid_amount': new_paid_amount,
                        'outstanding_amount': new_outstanding,
                        'status': new_status
                    })
            
            logger.info(f"Processed vendor payment {payment['payment_number']}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing vendor payment {payment_id}: {str(e)}")
            raise
    
    # ============================================================================
    # REPORTING
    # ============================================================================
    
    def get_aged_payables_report(self, tenant_id: int, as_of_date: str = None) -> List[Dict]:
        """Generate aged payables report"""
        try:
            if not as_of_date:
                as_of_date = date.today().isoformat()
            
            query = """
                SELECT 
                    v.vendor_code,
                    v.vendor_name,
                    pi.invoice_number,
                    pi.invoice_date,
                    pi.due_date,
                    pi.total_amount,
                    pi.outstanding_amount,
                    CASE 
                        WHEN pi.due_date >= ? THEN 'Current'
                        WHEN pi.due_date >= date(?, '-30 days') THEN '1-30 Days'
                        WHEN pi.due_date >= date(?, '-60 days') THEN '31-60 Days'
                        WHEN pi.due_date >= date(?, '-90 days') THEN '61-90 Days'
                        ELSE 'Over 90 Days'
                    END as aging_bucket,
                    julianday(?) - julianday(pi.due_date) as days_overdue
                FROM purchase_invoices pi
                JOIN vendors v ON pi.vendor_id = v.id
                WHERE pi.tenant_id = ? 
                    AND pi.outstanding_amount > 0
                    AND pi.status IN ('APPROVED', 'PARTIALLY_PAID')
                ORDER BY v.vendor_name, pi.due_date
            """
            
            return self.db.execute_query(query, (as_of_date, as_of_date, as_of_date, as_of_date, as_of_date, tenant_id))
            
        except Exception as e:
            logger.error(f"Error generating aged payables report: {str(e)}")
            raise
    
    # ============================================================================
    # HELPER METHODS
    # ============================================================================
    
    def _generate_vendor_code(self, tenant_id: int) -> str:
        """Generate a unique vendor code"""
        try:
            # Get the latest vendor code for this tenant
            result = self.db.execute_query(
                "SELECT vendor_code FROM vendors WHERE tenant_id = ? ORDER BY id DESC LIMIT 1",
                (tenant_id,)
            )
            
            if result:
                last_code = result[0]['vendor_code']
                # Extract number from format like "VEN001"
                if last_code.startswith('VEN'):
                    next_num = int(last_code[3:]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            return f"VEN{next_num:03d}"
            
        except Exception as e:
            logger.error(f"Error generating vendor code: {str(e)}")
            return f"VEN{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _generate_invoice_number(self, invoice_type: str, tenant_id: int) -> str:
        """Generate a unique invoice number"""
        try:
            prefix = 'PI' if invoice_type == 'PURCHASE' else 'SI'
            year = datetime.now().year
            
            result = self.db.execute_query(
                "SELECT invoice_number FROM purchase_invoices WHERE tenant_id = ? ORDER BY id DESC LIMIT 1",
                (tenant_id,)
            )
            
            if result:
                last_number = result[0]['invoice_number']
                if f"{prefix}-{year}" in last_number:
                    parts = last_number.split('-')
                    next_num = int(parts[-1]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            return f"{prefix}-{year}-{next_num:04d}"
            
        except Exception as e:
            logger.error(f"Error generating invoice number: {str(e)}")
            return f"{prefix}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _generate_payment_number(self, tenant_id: int) -> str:
        """Generate a unique payment number"""
        try:
            year = datetime.now().year
            
            result = self.db.execute_query(
                "SELECT payment_number FROM vendor_payments WHERE tenant_id = ? ORDER BY id DESC LIMIT 1",
                (tenant_id,)
            )
            
            if result:
                last_number = result[0]['payment_number']
                if f"VP-{year}" in last_number:
                    parts = last_number.split('-')
                    next_num = int(parts[-1]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            return f"VP-{year}-{next_num:04d}"
            
        except Exception as e:
            logger.error(f"Error generating payment number: {str(e)}")
            return f"VP-{datetime.now().strftime('%Y%m%d%H%M%S')}"
