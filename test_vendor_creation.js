#!/usr/bin/env node

/**
 * Test script to verify vendor creation functionality
 * This script tests the complete vendor creation flow from frontend to backend
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5002/api';

async function testVendorCreation() {
    console.log('🧪 Testing Vendor Creation Functionality\n');

    try {
        // Step 1: Login to get authentication token
        console.log('1️⃣ Authenticating...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        if (loginResponse.status !== 200) {
            throw new Error('Authentication failed');
        }

        const token = loginResponse.data.token;
        const user = loginResponse.data.user;
        console.log(`✅ Authentication successful for user: ${user.username}`);

        // Step 2: Test vendor creation with correct field names
        console.log('\n2️⃣ Creating test vendor...');
        const vendorData = {
            vendor_name: 'Test Vendor Frontend',
            vendor_code: 'TESTFE001',
            contact_person: 'Frontend Test',
            email: '<EMAIL>',
            phone: '**********',
            address_line1: '789 Frontend St',
            payment_terms: 'NET_30',
            tax_id: 'TAXFE123',
            is_active: true,
            tenant_id: user.tenant_id,
            created_by: user.id
        };

        const createResponse = await axios.post(`${BASE_URL}/accounting/vendors`, vendorData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (createResponse.status === 201) {
            console.log(`✅ Vendor created successfully with ID: ${createResponse.data.vendor_id}`);
        } else {
            throw new Error(`Unexpected status: ${createResponse.status}`);
        }

        // Step 3: Verify vendor was created by fetching vendors list
        console.log('\n3️⃣ Verifying vendor creation...');
        const listResponse = await axios.get(`${BASE_URL}/accounting/vendors?tenant_id=${user.tenant_id}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const vendors = listResponse.data.data;
        const createdVendor = vendors.find(v => v.vendor_code === 'TESTFE001');

        if (createdVendor) {
            console.log('✅ Vendor verification successful');
            console.log(`   - Vendor Name: ${createdVendor.vendor_name}`);
            console.log(`   - Vendor Code: ${createdVendor.vendor_code}`);
            console.log(`   - Contact Person: ${createdVendor.contact_person}`);
            console.log(`   - Address: ${createdVendor.address_line1}`);
            console.log(`   - Payment Terms: ${createdVendor.payment_terms}`);
        } else {
            throw new Error('Created vendor not found in vendors list');
        }

        // Step 4: Test error handling with duplicate vendor code
        console.log('\n4️⃣ Testing duplicate vendor code handling...');
        try {
            await axios.post(`${BASE_URL}/accounting/vendors`, {
                ...vendorData,
                vendor_name: 'Duplicate Test'
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            console.log('❌ Expected error for duplicate vendor code, but request succeeded');
        } catch (error) {
            if (error.response && error.response.data.error.includes('UNIQUE constraint failed')) {
                console.log('✅ Duplicate vendor code properly rejected');
            } else {
                throw error;
            }
        }

        console.log('\n🎉 All vendor creation tests passed successfully!');
        console.log('\n📋 Summary:');
        console.log('   ✅ Authentication works');
        console.log('   ✅ Vendor creation with correct field names works');
        console.log('   ✅ Vendor data is properly stored');
        console.log('   ✅ Duplicate vendor code validation works');
        console.log('\n🔧 The 500 Internal Server Error issue has been resolved!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.response?.data || error.message);
        process.exit(1);
    }
}

// Run the test
testVendorCreation();
