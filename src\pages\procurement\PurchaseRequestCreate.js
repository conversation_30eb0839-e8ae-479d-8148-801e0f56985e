import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Modal, Table } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faClipboardList,
  faPlus,
  faTimes,
  faSave,
  faArrowLeft,
  faExclamationTriangle,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import procurementAPI from '../../services/procurementAPI';
import UnitSelector from '../../components/common/UnitSelector';
import StoreroomSelector from '../../components/common/StoreroomSelector';

const PurchaseRequestCreate = () => {
  const [formData, setFormData] = useState({
    priority: 'medium',
    required_date: '',
    storeroom_id: '',
    notes: '',
    items: []
  });
  const [newItem, setNewItem] = useState({
    item_name: '',
    item_description: '',
    requested_quantity: '',
    unit: '',
    estimated_unit_price: ''
  });
  const [inventoryItems, setInventoryItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [validated, setValidated] = useState(false);

  const { currentUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadInventoryItems();
    // Set default required date to 7 days from now
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 7);
    setFormData(prev => ({
      ...prev,
      required_date: defaultDate.toISOString().split('T')[0]
    }));
  }, []);

  const loadInventoryItems = async () => {
    try {
      const response = await procurementAPI.getInventoryItems();
      setInventoryItems(response.data.data || []);
    } catch (err) {
      console.error('Error loading inventory items:', err);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNewItemChange = (field, value) => {
    setNewItem(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInventoryItemSelect = (inventoryItem) => {
    setNewItem(prev => ({
      ...prev,
      item_name: inventoryItem.name,
      item_description: inventoryItem.description || '',
      unit: inventoryItem.unit || '',
      estimated_unit_price: inventoryItem.selling_price || ''
    }));
  };

  const addItem = () => {
    // Validate new item
    if (!newItem.item_name || !newItem.requested_quantity || !newItem.unit) {
      setError('Please fill in all required item fields');
      return;
    }

    const item = {
      ...newItem,
      id: Date.now(), // Temporary ID for frontend
      requested_quantity: parseInt(newItem.requested_quantity),
      estimated_unit_price: parseFloat(newItem.estimated_unit_price || 0),
      estimated_total_price: parseInt(newItem.requested_quantity) * parseFloat(newItem.estimated_unit_price || 0)
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, item]
    }));

    // Reset new item form
    setNewItem({
      item_name: '',
      item_description: '',
      requested_quantity: '',
      unit: '',
      estimated_unit_price: ''
    });
    setError(null);
  };

  const removeItem = (itemId) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const validateForm = () => {
    if (!formData.required_date) {
      setError('Required date is mandatory');
      return false;
    }

    if (formData.items.length === 0) {
      setError('At least one item is required');
      return false;
    }

    const today = new Date().toISOString().split('T')[0];
    if (formData.required_date <= today) {
      setError('Required date must be in the future');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setValidated(true);

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Prepare data for submission
      const submitData = {
        ...formData,
        items: formData.items.map(item => ({
          item_name: item.item_name,
          item_description: item.item_description,
          requested_quantity: item.requested_quantity,
          unit: item.unit,
          estimated_unit_price: item.estimated_unit_price,
          notes: item.notes || ''
        }))
      };

      await procurementAPI.createPurchaseRequest(submitData);
      setShowSuccessModal(true);
    } catch (err) {
      console.error('Error creating purchase request:', err);
      setError(err.response?.data?.message || 'Failed to create purchase request');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    navigate('/procurement/requests');
  };

  const handleCancel = () => {
    if (formData.items.length > 0 || formData.notes) {
      setShowCancelModal(true);
    } else {
      navigate('/procurement/requests');
    }
  };

  const confirmCancel = () => {
    setShowCancelModal(false);
    navigate('/procurement/requests');
  };

  const getTotalEstimatedAmount = () => {
    return formData.items.reduce((total, item) => total + (item.estimated_total_price || 0), 0);
  };

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faClipboardList} className="me-2 text-primary" />
            Create Purchase Request
          </h2>
          <p className="text-muted mb-0">Submit a new purchase request to the hub</p>
        </div>
        <Button variant="outline-secondary" onClick={handleCancel}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to List
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Row>
          <Col lg={8}>
            {/* Request Details */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0 text-primary">Request Details</h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <Row>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Priority <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        value={formData.priority}
                        onChange={(e) => handleInputChange('priority', e.target.value)}
                        required
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Required Date <span className="text-danger">*</span></Form.Label>
                      <Form.Control
                        type="date"
                        value={formData.required_date}
                        onChange={(e) => handleInputChange('required_date', e.target.value)}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6} className="mb-3">
                    <StoreroomSelector
                      value={formData.storeroom_id}
                      onChange={(value) => handleInputChange('storeroom_id', value)}
                      label="Destination Storeroom"
                      required={true}
                      placeholder="Select destination storeroom..."
                    />
                  </Col>
                </Row>
                <Row>
                  <Col className="mb-3">
                    <Form.Group>
                      <Form.Label>Notes</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder="Additional notes or special instructions..."
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Items */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0 text-primary">Items</h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                {/* Add New Item Form */}
                <div className="border rounded p-3 mb-3">
                  <h6 className="mb-3">Add New Item</h6>
                  <Row>
                    <Col md={6} className="mb-3">
                      <Form.Group>
                        <Form.Label>Item Name <span className="text-danger">*</span></Form.Label>
                        <Form.Control
                          type="text"
                          value={newItem.item_name}
                          onChange={(e) => handleNewItemChange('item_name', e.target.value)}
                          placeholder="Enter item name"
                          list="inventory-items"
                        />
                        <datalist id="inventory-items">
                          {inventoryItems.map(item => (
                            <option key={item.id} value={item.name} />
                          ))}
                        </datalist>
                      </Form.Group>
                    </Col>
                    <Col md={6} className="mb-3">
                      <Form.Group>
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                          type="text"
                          value={newItem.item_description}
                          onChange={(e) => handleNewItemChange('item_description', e.target.value)}
                          placeholder="Item description"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={3} className="mb-3">
                      <Form.Group>
                        <Form.Label>Quantity <span className="text-danger">*</span></Form.Label>
                        <Form.Control
                          type="number"
                          min="1"
                          value={newItem.requested_quantity}
                          onChange={(e) => handleNewItemChange('requested_quantity', e.target.value)}
                          placeholder="0"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3} className="mb-3">
                      <UnitSelector
                        value={newItem.unit}
                        onChange={(e) => handleNewItemChange('unit', e.target.value)}
                        label="Unit"
                        required={true}
                        placeholder="Select unit..."
                      />
                    </Col>
                    <Col md={3} className="mb-3">
                      <Form.Group>
                        <Form.Label>Est. Unit Price</Form.Label>
                        <Form.Control
                          type="number"
                          step="0.01"
                          min="0"
                          value={newItem.estimated_unit_price}
                          onChange={(e) => handleNewItemChange('estimated_unit_price', e.target.value)}
                          placeholder="0.00"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3} className="mb-3 d-flex align-items-end">
                      <Button variant="primary" onClick={addItem} className="w-100">
                        <FontAwesomeIcon icon={faPlus} className="me-2" />
                        Add Item
                      </Button>
                    </Col>
                  </Row>
                </div>

                {/* Items List */}
                {formData.items.length > 0 ? (
                  <div className="table-responsive">
                    <Table striped bordered hover>
                      <thead>
                        <tr>
                          <th>Item Name</th>
                          <th>Description</th>
                          <th>Quantity</th>
                          <th>Unit</th>
                          <th>Est. Unit Price</th>
                          <th>Est. Total</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.items.map((item) => (
                          <tr key={item.id}>
                            <td>{item.item_name}</td>
                            <td>{item.item_description}</td>
                            <td>{item.requested_quantity}</td>
                            <td>{item.unit}</td>
                            <td>₹{parseFloat(item.estimated_unit_price || 0).toFixed(2)}</td>
                            <td>₹{parseFloat(item.estimated_total_price || 0).toFixed(2)}</td>
                            <td>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => removeItem(item.id)}
                              >
                                <FontAwesomeIcon icon={faTimes} />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center text-muted py-4">
                    <FontAwesomeIcon icon={faClipboardList} className="fa-2x mb-2 opacity-50" />
                    <div>No items added yet</div>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4}>
            {/* Summary */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0 text-primary">Summary</h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <div className="d-flex justify-content-between mb-2">
                  <span>Total Items:</span>
                  <span className="fw-semibold">{formData.items.length}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Priority:</span>
                  <span className="fw-semibold text-capitalize">{formData.priority}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Required Date:</span>
                  <span className="fw-semibold">
                    {formData.required_date ? new Date(formData.required_date).toLocaleDateString() : '-'}
                  </span>
                </div>
                <hr />
                <div className="d-flex justify-content-between">
                  <span className="fw-semibold">Est. Total Amount:</span>
                  <span className="fw-semibold text-primary">
                    ₹{getTotalEstimatedAmount().toLocaleString()}
                  </span>
                </div>
              </Card.Body>
            </Card>

            {/* Actions */}
            <Card>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <div className="d-grid gap-2">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={loading || formData.items.length === 0}
                  >
                    <FontAwesomeIcon icon={faSave} className="me-2" />
                    {loading ? 'Creating...' : 'Create Request'}
                  </Button>
                  <Button variant="outline-secondary" onClick={handleCancel}>
                    Cancel
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* Success Modal */}
      <Modal show={showSuccessModal} onHide={handleSuccessModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success me-2" />
            Success
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Purchase request has been created successfully!</p>
          <p className="text-muted mb-0">You can now submit it for approval or continue editing.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleSuccessModalClose}>
            Go to Requests List
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Cancel Confirmation Modal */}
      <Modal show={showCancelModal} onHide={() => setShowCancelModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Cancel</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to cancel? All unsaved changes will be lost.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCancelModal(false)}>
            Continue Editing
          </Button>
          <Button variant="danger" onClick={confirmCancel}>
            Yes, Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PurchaseRequestCreate;
