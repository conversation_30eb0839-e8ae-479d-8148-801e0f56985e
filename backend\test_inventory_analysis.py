#!/usr/bin/env python3
"""
Inventory and Low Stock Analysis Script
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import db_manager

def analyze_inventory():
    print('=== INVENTORY AND LOW STOCK ANALYSIS ===')
    
    # Check current inventory status
    print('\n1. Current Inventory Status:')
    inventory_items = db_manager.execute_query('''
        SELECT name, sku, quantity, reorder_level, location, tenant_id,
               CASE WHEN quantity <= reorder_level THEN "LOW STOCK" ELSE "OK" END as status
        FROM inventory 
        ORDER BY quantity ASC
    ''')
    
    for item in inventory_items:
        print(f'  - {item["name"]} ({item["sku"]})')
        print(f'    Location: {item["location"]}')
        print(f'    Current: {item["quantity"]} | Reorder at: {item["reorder_level"]} | Status: {item["status"]}')
        print()
    
    # Check storerooms with auto-reorder enabled
    print('2. Storerooms with Auto-Reorder Enabled:')
    auto_storerooms = db_manager.execute_query('''
        SELECT name, tenant_id, min_quantity_threshold, auto_reorder_enabled, reorder_quantity
        FROM storerooms 
        WHERE auto_reorder_enabled = 1 AND status = "active"
    ''')
    
    for sr in auto_storerooms:
        print(f'  - {sr["name"]} (Tenant: {sr["tenant_id"]})')
        print(f'    Min threshold: {sr["min_quantity_threshold"]} | Reorder qty: {sr["reorder_quantity"]}')
    
    # Check existing purchase requests
    print('\n3. Existing Purchase Requests:')
    purchase_requests = db_manager.execute_query('''
        SELECT request_number, status, notes, created_at, total_estimated_amount
        FROM purchase_requests 
        ORDER BY created_at DESC
        LIMIT 10
    ''')
    
    if purchase_requests:
        for pr in purchase_requests:
            amount = pr["total_estimated_amount"] or 0
            print(f'  - {pr["request_number"]}: {pr["status"]} (${amount})')
            print(f'    Notes: {pr["notes"]}')
            print(f'    Created: {pr["created_at"]}')
            print()
    else:
        print('  No purchase requests found')
    
    # Check purchase request items
    print('4. Purchase Request Items:')
    pr_items = db_manager.execute_query('''
        SELECT pri.item_name, pri.requested_quantity, pri.unit, 
               pri.estimated_unit_price, pri.total_estimated_amount,
               pr.request_number
        FROM purchase_request_items pri
        JOIN purchase_requests pr ON pri.purchase_request_id = pr.id
        ORDER BY pr.created_at DESC
        LIMIT 10
    ''')
    
    if pr_items:
        for item in pr_items:
            unit_price = item["estimated_unit_price"] or 0
            total_amount = item["total_estimated_amount"] or 0
            print(f'  - {item["item_name"]} ({item["request_number"]})')
            print(f'    Quantity: {item["requested_quantity"]} {item["unit"]} @ ${unit_price:.2f} = ${total_amount:.2f}')
    else:
        print('  No purchase request items found')
    
    print('\n=== ANALYSIS COMPLETE ===')

if __name__ == "__main__":
    analyze_inventory()
