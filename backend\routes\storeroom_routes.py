from flask import Blueprint, jsonify, request
from datetime import datetime
from utils import token_required, require_role, filter_data_by_tenant
from database_manager import db_manager

storeroom_bp = Blueprint('storeroom', __name__)

@storeroom_bp.route('/api/storerooms', methods=['GET'])
@token_required
def get_storerooms():
    """Get all storerooms with tenant-based filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Get query parameters
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        tenant_id = request.args.get('tenant_id', type=int)
        
        # Base query
        query = """
            SELECT s.*, t.name as tenant_name, t.site_code, t.is_hub
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE 1=1
        """
        params = []
        
        # Filter by active status
        if active_only:
            query += " AND s.status = 'active'"
        
        # Apply tenant-based filtering
        if user_role == 'admin':
            # Admin can see all storerooms
            if tenant_id:
                query += " AND s.tenant_id = ?"
                params.append(tenant_id)
        elif user_role == 'hub_admin':
            # Hub admin can see all storerooms (hub and franchises)
            if tenant_id:
                query += " AND s.tenant_id = ?"
                params.append(tenant_id)
        else:
            # Franchise users can only see their own storerooms
            query += " AND s.tenant_id = ?"
            params.append(user_tenant_id)
        
        query += " ORDER BY t.name, s.name"
        
        storerooms = db_manager.execute_query(query, tuple(params))
        
        return jsonify({
            'success': True,
            'data': storerooms
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@storeroom_bp.route('/api/storerooms/by-tenant', methods=['GET'])
@token_required
def get_storerooms_by_tenant():
    """Get storerooms grouped by tenant for dropdown selection"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Base query
        query = """
            SELECT s.*, t.name as tenant_name, t.site_code, t.is_hub
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.status = 'active'
        """
        params = []
        
        # Apply tenant-based filtering for dropdown access
        if user_role in ['admin', 'hub_admin']:
            # Mayiladuthurai Hub users can see ALL storerooms
            pass
        else:
            # Franchise users can only see their own storerooms (readonly)
            query += " AND s.tenant_id = ?"
            params.append(user_tenant_id)
        
        query += " ORDER BY t.name, s.name"
        
        storerooms = db_manager.execute_query(query, tuple(params))
        
        # Group by tenant
        grouped_storerooms = {}
        for storeroom in storerooms:
            tenant_key = f"{storeroom['tenant_name']} ({storeroom['site_code']})"
            if tenant_key not in grouped_storerooms:
                grouped_storerooms[tenant_key] = {
                    'tenant_id': storeroom['tenant_id'],
                    'tenant_name': storeroom['tenant_name'],
                    'site_code': storeroom['site_code'],
                    'is_hub': storeroom['is_hub'],
                    'storerooms': []
                }
            
            grouped_storerooms[tenant_key]['storerooms'].append({
                'id': storeroom['id'],
                'storeroom_id': storeroom['storeroom_id'],
                'name': storeroom['name'],
                'description': storeroom['description'],
                'location_details': storeroom['location_details'],
                'capacity': storeroom['capacity'],
                'manager_name': storeroom['manager_name']
            })
        
        return jsonify({
            'success': True,
            'data': grouped_storerooms
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@storeroom_bp.route('/api/storerooms/<int:storeroom_id>', methods=['GET'])
@token_required
def get_storeroom(storeroom_id):
    """Get a specific storeroom"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        query = """
            SELECT s.*, t.name as tenant_name, t.site_code, t.is_hub,
                   u.username as created_by_username
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            LEFT JOIN users u ON s.created_by = u.id
            WHERE s.id = ?
        """
        
        result = db_manager.execute_query(query, (storeroom_id,))
        if not result:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404
        
        storeroom = result[0]
        
        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if storeroom['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        return jsonify({
            'success': True,
            'data': storeroom
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@storeroom_bp.route('/api/storerooms', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def create_storeroom():
    """Create a new storeroom"""
    try:
        data = request.get_json()
        user = request.current_user
        
        # Validate required fields
        required_fields = ['storeroom_id', 'name', 'tenant_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        # Check if storeroom_id already exists
        existing = db_manager.execute_query(
            "SELECT id FROM storerooms WHERE storeroom_id = ?", 
            (data['storeroom_id'],)
        )
        if existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom ID already exists'
            }), 400
        
        # Validate tenant exists
        tenant_check = db_manager.execute_query(
            "SELECT id FROM tenants WHERE id = ? AND is_active = 1",
            (data['tenant_id'],)
        )
        if not tenant_check:
            return jsonify({
                'success': False,
                'error': 'Invalid tenant ID'
            }), 400
        
        # Create new storeroom
        storeroom_data = {
            'storeroom_id': data['storeroom_id'],
            'name': data['name'],
            'description': data.get('description', ''),
            'tenant_id': data['tenant_id'],
            'location_details': data.get('location_details', ''),
            'capacity': data.get('capacity'),
            'status': data.get('status', 'active'),
            'manager_name': data.get('manager_name', ''),
            'manager_contact': data.get('manager_contact', ''),
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        storeroom_id = db_manager.insert_record('storerooms', storeroom_data)
        
        # Get the created storeroom with tenant info
        created_storeroom = db_manager.execute_query("""
            SELECT s.*, t.name as tenant_name, t.site_code, t.is_hub
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.id = ?
        """, (storeroom_id,))[0]
        
        return jsonify({
            'success': True,
            'data': created_storeroom,
            'message': 'Storeroom created successfully'
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@storeroom_bp.route('/api/storerooms/<int:storeroom_id>', methods=['PUT'])
@token_required
@require_role(['admin', 'hub_admin'])
def update_storeroom(storeroom_id):
    """Update a storeroom"""
    try:
        data = request.get_json()
        user = request.current_user
        
        # Check if storeroom exists
        existing = db_manager.execute_query(
            "SELECT * FROM storerooms WHERE id = ?", (storeroom_id,)
        )
        if not existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404
        
        # Check if storeroom_id already exists (excluding current storeroom)
        if 'storeroom_id' in data:
            existing_id = db_manager.execute_query(
                "SELECT id FROM storerooms WHERE storeroom_id = ? AND id != ?", 
                (data['storeroom_id'], storeroom_id)
            )
            if existing_id:
                return jsonify({
                    'success': False,
                    'error': 'Storeroom ID already exists'
                }), 400
        
        # Validate tenant if being updated
        if 'tenant_id' in data:
            tenant_check = db_manager.execute_query(
                "SELECT id FROM tenants WHERE id = ? AND is_active = 1",
                (data['tenant_id'],)
            )
            if not tenant_check:
                return jsonify({
                    'success': False,
                    'error': 'Invalid tenant ID'
                }), 400
        
        # Update fields
        update_data = {
            'updated_at': datetime.now().isoformat()
        }
        
        allowed_fields = [
            'storeroom_id', 'name', 'description', 'tenant_id', 
            'location_details', 'capacity', 'status', 'manager_name', 'manager_contact'
        ]
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # Update the storeroom
        set_clause = ', '.join([f"{key} = ?" for key in update_data.keys()])
        values = list(update_data.values()) + [storeroom_id]
        
        db_manager.execute_update(
            f"UPDATE storerooms SET {set_clause} WHERE id = ?",
            tuple(values)
        )
        
        # Get updated storeroom with tenant info
        updated_storeroom = db_manager.execute_query("""
            SELECT s.*, t.name as tenant_name, t.site_code, t.is_hub
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.id = ?
        """, (storeroom_id,))[0]
        
        return jsonify({
            'success': True,
            'data': updated_storeroom,
            'message': 'Storeroom updated successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@storeroom_bp.route('/api/storerooms/<int:storeroom_id>', methods=['DELETE'])
@token_required
@require_role(['admin', 'hub_admin'])
def delete_storeroom(storeroom_id):
    """Delete a storeroom (soft delete by setting status to inactive)"""
    try:
        # Check if storeroom exists
        existing = db_manager.execute_query(
            "SELECT * FROM storerooms WHERE id = ?", (storeroom_id,)
        )
        if not existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404
        
        # Check if storeroom is being used in any active records
        usage_checks = [
            ("purchase_requests", "storeroom_id", "active purchase requests"),
            ("inventory_transfers", "from_storeroom_id", "inventory transfers (from)"),
            ("inventory_transfers", "to_storeroom_id", "inventory transfers (to)")
        ]
        
        for table, column, description in usage_checks:
            usage = db_manager.execute_query(
                f"SELECT COUNT(*) as count FROM {table} WHERE {column} = ?",
                (storeroom_id,)
            )
            if usage and usage[0]['count'] > 0:
                return jsonify({
                    'success': False,
                    'error': f'Cannot delete storeroom. It is being used in {description}.'
                }), 400
        
        # Soft delete by setting status to inactive
        db_manager.execute_update(
            "UPDATE storerooms SET status = ?, updated_at = ? WHERE id = ?",
            ('inactive', datetime.now().isoformat(), storeroom_id)
        )
        
        return jsonify({
            'success': True,
            'message': 'Storeroom deleted successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
