import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Tabs, Tab, Table, Button, Form,
  Alert, Spinner, Badge, InputGroup
} from 'react-bootstrap';
import {
  FaChartLine, FaFileAlt, FaBalanceScale, FaBook,
  FaWater, FaDownload, FaCalendarAlt, FaFilter
} from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const FinancialReports = () => {
  const [activeTab, setActiveTab] = useState('trial-balance');
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadReport();
  }, [activeTab, dateRange]);

  const loadReport = async () => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = 1; // Get from context

      switch (activeTab) {
        case 'trial-balance':
          const trialBalance = await accountingService.getTrialBalance(tenantId, dateRange.endDate);
          setReportData(trialBalance);
          break;
        case 'profit-loss':
          const profitLoss = await accountingService.getProfitLoss(tenantId, dateRange.startDate, dateRange.endDate);
          setReportData(profitLoss);
          break;
        case 'balance-sheet':
          const balanceSheet = await accountingService.getBalanceSheet(tenantId, dateRange.endDate);
          setReportData(balanceSheet);
          break;
        case 'general-ledger':
          const generalLedger = await accountingService.getGeneralLedger(tenantId, dateRange.startDate, dateRange.endDate);
          setReportData(generalLedger);
          break;
        case 'cash-flow':
          const cashFlow = await accountingService.getCashFlowStatement(tenantId, dateRange.startDate, dateRange.endDate);
          setReportData(cashFlow);
          break;
        default:
          break;
      }
    } catch (err) {
      setError(`Failed to load ${activeTab} report: ${err.message}`);
      console.error('Financial report error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format = 'pdf') => {
    try {
      const tenantId = 1;
      const response = await accountingService.exportReport(activeTab, tenantId, dateRange, format);

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${activeTab}-${dateRange.endDate}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError(`Failed to export report: ${err.message}`);
    }
  };

  const ReportHeader = ({ title, icon }) => (
    <Row className="mb-3">
      <Col md={6}>
        <h5>
          {icon}
          {title}
        </h5>
      </Col>
      <Col md={6}>
        <Row>
          <Col md={4}>
            <Form.Control
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
            />
          </Col>
          <Col md={4}>
            <Form.Control
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
            />
          </Col>
          <Col md={4}>
            <Button
              variant="outline-primary"
              onClick={() => handleExport('pdf')}
              className="me-2"
            >
              <FaDownload className="me-1" />
              PDF
            </Button>
            <Button
              variant="outline-success"
              onClick={() => handleExport('excel')}
            >
              Excel
            </Button>
          </Col>
        </Row>
      </Col>
    </Row>
  );

  const TrialBalanceTab = () => (
    <div>
      <ReportHeader
        title="Trial Balance"
        icon={<FaBalanceScale className="me-2" />}
      />

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : reportData ? (
        <div>
          <Table responsive striped>
            <thead>
              <tr>
                <th>Account Code</th>
                <th>Account Name</th>
                <th>Account Type</th>
                <th className="text-end">Debit Balance</th>
                <th className="text-end">Credit Balance</th>
              </tr>
            </thead>
            <tbody>
              {reportData.accounts?.map((account) => (
                <tr key={account.account_code}>
                  <td>{account.account_code}</td>
                  <td>{account.account_name}</td>
                  <td>
                    <Badge bg="secondary">{account.account_type}</Badge>
                  </td>
                  <td className="text-end">
                    {account.debit_balance > 0 ? `₹${account.debit_balance.toLocaleString()}` : '-'}
                  </td>
                  <td className="text-end">
                    {account.credit_balance > 0 ? `₹${account.credit_balance.toLocaleString()}` : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="table-dark">
                <th colSpan={3}>Total</th>
                <th className="text-end">₹{reportData.total_debits?.toLocaleString()}</th>
                <th className="text-end">₹{reportData.total_credits?.toLocaleString()}</th>
              </tr>
            </tfoot>
          </Table>

          {reportData.total_debits !== reportData.total_credits && (
            <Alert variant="warning">
              <strong>Warning:</strong> Trial Balance is not balanced!
              Difference: ₹{Math.abs(reportData.total_debits - reportData.total_credits).toLocaleString()}
            </Alert>
          )}
        </div>
      ) : null}
    </div>
  );

  const ProfitLossTab = () => (
    <div>
      <ReportHeader
        title="Profit & Loss Statement"
        icon={<FaChartLine className="me-2" />}
      />

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : reportData ? (
        <Table responsive>
          <tbody>
            <tr className="table-primary">
              <td><strong>REVENUE</strong></td>
              <td className="text-end"><strong>₹{reportData.total_revenue?.toLocaleString()}</strong></td>
            </tr>
            {reportData.revenue_accounts?.map((account) => (
              <tr key={account.account_code}>
                <td className="ps-4">{account.account_name}</td>
                <td className="text-end">₹{account.amount?.toLocaleString()}</td>
              </tr>
            ))}

            <tr className="table-warning">
              <td><strong>COST OF GOODS SOLD</strong></td>
              <td className="text-end"><strong>₹{reportData.total_cogs?.toLocaleString()}</strong></td>
            </tr>
            {reportData.cogs_accounts?.map((account) => (
              <tr key={account.account_code}>
                <td className="ps-4">{account.account_name}</td>
                <td className="text-end">₹{account.amount?.toLocaleString()}</td>
              </tr>
            ))}

            <tr className="table-success">
              <td><strong>GROSS PROFIT</strong></td>
              <td className="text-end"><strong>₹{reportData.gross_profit?.toLocaleString()}</strong></td>
            </tr>

            <tr className="table-danger">
              <td><strong>OPERATING EXPENSES</strong></td>
              <td className="text-end"><strong>₹{reportData.total_expenses?.toLocaleString()}</strong></td>
            </tr>
            {reportData.expense_accounts?.map((account) => (
              <tr key={account.account_code}>
                <td className="ps-4">{account.account_name}</td>
                <td className="text-end">₹{account.amount?.toLocaleString()}</td>
              </tr>
            ))}

            <tr className="table-dark">
              <td><strong>NET PROFIT</strong></td>
              <td className="text-end"><strong>₹{reportData.net_profit?.toLocaleString()}</strong></td>
            </tr>
          </tbody>
        </Table>
      ) : null}
    </div>
  );

  const BalanceSheetTab = () => (
    <div>
      <ReportHeader
        title="Balance Sheet"
        icon={<FaFileAlt className="me-2" />}
      />

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : reportData ? (
        <Row>
          <Col md={6}>
            <Card>
              <Card.Header>
                <h6>ASSETS</h6>
              </Card.Header>
              <Card.Body>
                <Table responsive>
                  <tbody>
                    <tr className="table-primary">
                      <td><strong>Current Assets</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_current_assets?.toLocaleString()}</strong></td>
                    </tr>
                    {reportData.current_assets?.map((asset) => (
                      <tr key={asset.account_code}>
                        <td className="ps-3">{asset.account_name}</td>
                        <td className="text-end">₹{asset.amount?.toLocaleString()}</td>
                      </tr>
                    ))}

                    <tr className="table-secondary">
                      <td><strong>Fixed Assets</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_fixed_assets?.toLocaleString()}</strong></td>
                    </tr>
                    {reportData.fixed_assets?.map((asset) => (
                      <tr key={asset.account_code}>
                        <td className="ps-3">{asset.account_name}</td>
                        <td className="text-end">₹{asset.amount?.toLocaleString()}</td>
                      </tr>
                    ))}

                    <tr className="table-dark">
                      <td><strong>TOTAL ASSETS</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_assets?.toLocaleString()}</strong></td>
                    </tr>
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>

          <Col md={6}>
            <Card>
              <Card.Header>
                <h6>LIABILITIES & EQUITY</h6>
              </Card.Header>
              <Card.Body>
                <Table responsive>
                  <tbody>
                    <tr className="table-warning">
                      <td><strong>Current Liabilities</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_current_liabilities?.toLocaleString()}</strong></td>
                    </tr>
                    {reportData.current_liabilities?.map((liability) => (
                      <tr key={liability.account_code}>
                        <td className="ps-3">{liability.account_name}</td>
                        <td className="text-end">₹{liability.amount?.toLocaleString()}</td>
                      </tr>
                    ))}

                    <tr className="table-danger">
                      <td><strong>Long-term Liabilities</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_long_term_liabilities?.toLocaleString()}</strong></td>
                    </tr>
                    {reportData.long_term_liabilities?.map((liability) => (
                      <tr key={liability.account_code}>
                        <td className="ps-3">{liability.account_name}</td>
                        <td className="text-end">₹{liability.amount?.toLocaleString()}</td>
                      </tr>
                    ))}

                    <tr className="table-success">
                      <td><strong>Equity</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_equity?.toLocaleString()}</strong></td>
                    </tr>
                    {reportData.equity_accounts?.map((equity) => (
                      <tr key={equity.account_code}>
                        <td className="ps-3">{equity.account_name}</td>
                        <td className="text-end">₹{equity.amount?.toLocaleString()}</td>
                      </tr>
                    ))}

                    <tr className="table-dark">
                      <td><strong>TOTAL LIABILITIES & EQUITY</strong></td>
                      <td className="text-end"><strong>₹{reportData.total_liabilities_equity?.toLocaleString()}</strong></td>
                    </tr>
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      ) : null}
    </div>
  );

  const CashFlowTab = () => (
    <div>
      <ReportHeader
        title="Cash Flow Statement"
        icon={<FaWater className="me-2" />}
      />

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : reportData ? (
        <Table responsive>
          <tbody>
            <tr className="table-primary">
              <td><strong>OPERATING ACTIVITIES</strong></td>
              <td className="text-end"><strong>₹{reportData.operating_cash_flow?.toLocaleString()}</strong></td>
            </tr>
            <tr>
              <td className="ps-4">Net Income</td>
              <td className="text-end">₹{reportData.net_income?.toLocaleString()}</td>
            </tr>
            <tr>
              <td className="ps-4">Depreciation</td>
              <td className="text-end">₹{reportData.depreciation?.toLocaleString()}</td>
            </tr>
            <tr>
              <td className="ps-4">Changes in Working Capital</td>
              <td className="text-end">₹{reportData.working_capital_changes?.toLocaleString()}</td>
            </tr>

            <tr className="table-warning">
              <td><strong>INVESTING ACTIVITIES</strong></td>
              <td className="text-end"><strong>₹{reportData.investing_cash_flow?.toLocaleString()}</strong></td>
            </tr>
            <tr>
              <td className="ps-4">Capital Expenditures</td>
              <td className="text-end">₹{reportData.capital_expenditures?.toLocaleString()}</td>
            </tr>

            <tr className="table-danger">
              <td><strong>FINANCING ACTIVITIES</strong></td>
              <td className="text-end"><strong>₹{reportData.financing_cash_flow?.toLocaleString()}</strong></td>
            </tr>
            <tr>
              <td className="ps-4">Loan Proceeds</td>
              <td className="text-end">₹{reportData.loan_proceeds?.toLocaleString()}</td>
            </tr>
            <tr>
              <td className="ps-4">Loan Repayments</td>
              <td className="text-end">₹{reportData.loan_repayments?.toLocaleString()}</td>
            </tr>

            <tr className="table-dark">
              <td><strong>NET CHANGE IN CASH</strong></td>
              <td className="text-end"><strong>₹{reportData.net_cash_change?.toLocaleString()}</strong></td>
            </tr>
            <tr className="table-dark">
              <td><strong>ENDING CASH BALANCE</strong></td>
              <td className="text-end"><strong>₹{reportData.ending_cash_balance?.toLocaleString()}</strong></td>
            </tr>
          </tbody>
        </Table>
      ) : null}
    </div>
  );

  return (
    <div className="financial-reports">
      <Card>
        <Card.Header>
          <Row>
            <Col>
              <h5 className="mb-0 text-primary">
                <FaChartLine className="me-2" />
                Financial Reports
              </h5>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body style={{background:"#f8f9fa"}}>
          {error && <Alert variant="danger">{error}</Alert>}

          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-3"
          >
            <Tab eventKey="trial-balance" title="Trial Balance">
              <TrialBalanceTab />
            </Tab>
            <Tab eventKey="profit-loss" title="Profit & Loss">
              <ProfitLossTab />
            </Tab>
            <Tab eventKey="balance-sheet" title="Balance Sheet">
              <BalanceSheetTab />
            </Tab>
            <Tab eventKey="cash-flow" title="Cash Flow">
              <CashFlowTab />
            </Tab>
          </Tabs>
        </Card.Body>
      </Card>
    </div>
  );
};

export default FinancialReports;
