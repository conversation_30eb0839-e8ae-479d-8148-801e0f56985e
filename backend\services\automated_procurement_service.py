"""
Automated Procurement Service

This service monitors inventory levels and automatically creates purchase requests
when stock levels fall below minimum thresholds.
"""

import logging
from datetime import datetime, timedelta
from database_manager import db_manager

logger = logging.getLogger(__name__)

class AutomatedProcurementService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def check_inventory_levels(self):
        """
        Check all inventory items against storeroom minimum thresholds
        and create purchase requests for items below threshold
        """
        try:
            # Get all storerooms with auto reorder enabled
            storerooms_query = """
                SELECT s.*, t.name as tenant_name, t.site_code
                FROM storerooms s
                LEFT JOIN tenants t ON s.tenant_id = t.id
                WHERE s.auto_reorder_enabled = 1 
                AND s.status = 'active'
                AND s.deleted_at IS NULL
            """
            storerooms = db_manager.execute_query(storerooms_query)
            
            if not storerooms:
                self.logger.info("No storerooms with auto reorder enabled found")
                return
            
            self.logger.info(f"Checking inventory levels for {len(storerooms)} storerooms")
            
            for storeroom in storerooms:
                self._check_storeroom_inventory(storeroom)
                
        except Exception as e:
            self.logger.error(f"Error checking inventory levels: {e}")
    
    def _check_storeroom_inventory(self, storeroom):
        """Check inventory levels for a specific storeroom"""
        try:
            # Get inventory items for this storeroom that are below minimum threshold
            # Using existing inventory table schema
            inventory_query = """
                SELECT i.*, i.name as item_name, i.description as item_description,
                       i.unit as unit_of_measure, i.category
                FROM inventory i
                WHERE i.location = ?
                AND i.quantity <= i.reorder_level
                AND i.tenant_id = ?
            """

            low_stock_items = db_manager.execute_query(
                inventory_query,
                [storeroom['name'], storeroom['tenant_id']]
            )
            
            if not low_stock_items:
                self.logger.debug(f"No low stock items found for storeroom {storeroom['name']}")
                return
            
            self.logger.info(f"Found {len(low_stock_items)} low stock items in {storeroom['name']}")
            
            # Check if there's already a pending purchase request for these items
            for item in low_stock_items:
                if not self._has_pending_purchase_request(storeroom['id'], item['id']):
                    self._create_purchase_request(storeroom, item)
                    
        except Exception as e:
            self.logger.error(f"Error checking storeroom {storeroom['name']} inventory: {e}")
    
    def _has_pending_purchase_request(self, storeroom_id, item_id):
        """Check if there's already a pending purchase request for this item"""
        try:
            # Check for existing purchase requests in the last 30 days
            cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()

            # Simplified check - just look for recent purchase requests from this storeroom
            query = """
                SELECT COUNT(*) as count
                FROM purchase_requests pr
                WHERE pr.storeroom_id = ?
                AND pr.status IN ('draft', 'submitted', 'approved', 'processing')
                AND pr.created_at >= ?
            """

            result = db_manager.execute_query(query, [storeroom_id, cutoff_date])
            return result[0]['count'] > 0 if result else False

        except Exception as e:
            self.logger.error(f"Error checking pending purchase requests: {e}")
            return False  # Allow creation if check fails
    
    def _create_purchase_request(self, storeroom, item):
        """Create an automated purchase request for low stock item"""
        try:
            # Generate purchase request number
            pr_number = self._generate_pr_number(storeroom['tenant_id'])
            
            # Calculate quantity to order (reorder quantity or minimum threshold)
            quantity_to_order = max(
                storeroom['reorder_quantity'] or item['reorder_level'],
                item['reorder_level'] - item['quantity']
            )
            
            # Create purchase request
            pr_data = {
                'request_number': pr_number,
                'requesting_tenant_id': storeroom['tenant_id'],
                'hub_tenant_id': 1,  # Mayiladuthurai Hub
                'storeroom_id': storeroom['id'],
                'priority': 'medium',
                'status': 'draft',
                'request_date': datetime.now().date().isoformat(),
                'required_date': (datetime.now() + timedelta(days=storeroom.get('reorder_point_days', 7))).date().isoformat(),
                'notes': f"Automated reorder: Stock level ({item['quantity']}) below reorder level ({item['reorder_level']})",
                'total_estimated_amount': 0,  # Will be updated when items are added
                'tenant_id': storeroom['tenant_id'],
                'created_by': 1,  # System user
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Insert purchase request
            pr_id = db_manager.insert_record('purchase_requests', pr_data)
            
            if pr_id:
                # Create purchase request item
                item_data = {
                    'purchase_request_id': pr_id,
                    'item_name': item['name'],
                    'description': item.get('description', ''),
                    'requested_quantity': int(quantity_to_order),
                    'unit': item['unit'],
                    'estimated_unit_price': item.get('cost_price', 0),
                    'total_estimated_amount': quantity_to_order * (item.get('cost_price', 0) or 0),
                    'notes': f"Auto-generated for storeroom: {storeroom['name']}",
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
                
                item_id = db_manager.insert_record('purchase_request_items', item_data)

                if item_id:
                    # Update total estimated cost
                    db_manager.update_record('purchase_requests', pr_id, {
                        'total_estimated_amount': item_data['total_estimated_amount'],
                        'updated_at': datetime.now().isoformat()
                    })
                    
                    self.logger.info(
                        f"Created automated purchase request {pr_number} for {item['item_name']} "
                        f"in {storeroom['name']} (Quantity: {quantity_to_order})"
                    )
                    
                    return pr_id
                else:
                    self.logger.error(f"Failed to create purchase request item for {item['item_name']}")
            else:
                self.logger.error(f"Failed to create purchase request for {item['item_name']}")
                
        except Exception as e:
            self.logger.error(f"Error creating purchase request for {item['item_name']}: {e}")
            return None
    
    def _generate_pr_number(self, tenant_id):
        """Generate a unique purchase request number"""
        try:
            # Get tenant site code
            tenant_query = "SELECT site_code FROM tenants WHERE id = ?"
            tenant_result = db_manager.execute_query(tenant_query, [tenant_id])
            site_code = tenant_result[0]['site_code'] if tenant_result else 'UNK'
            
            # Get next sequence number for this tenant
            count_query = """
                SELECT COUNT(*) as count 
                FROM purchase_requests 
                WHERE requesting_tenant_id = ? 
                AND DATE(created_at) = DATE('now')
            """
            count_result = db_manager.execute_query(count_query, [tenant_id])
            sequence = (count_result[0]['count'] if count_result else 0) + 1
            
            # Format: AUTO-{SITE_CODE}-{YYYYMMDD}-{SEQ}
            date_str = datetime.now().strftime('%Y%m%d')
            return f"AUTO-{site_code}-{date_str}-{sequence:03d}"
            
        except Exception as e:
            self.logger.error(f"Error generating PR number: {e}")
            return f"AUTO-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def run_automated_check(self):
        """Main method to run the automated procurement check"""
        self.logger.info("Starting automated procurement check...")
        try:
            self.check_inventory_levels()
            self.logger.info("Automated procurement check completed successfully")
        except Exception as e:
            self.logger.error(f"Automated procurement check failed: {e}")

# Singleton instance
automated_procurement_service = AutomatedProcurementService()
