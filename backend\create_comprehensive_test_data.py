"""
Comprehensive Test Data Creation Script

This script creates complete test data for the procurement lifecycle including:
- Enhanced storerooms with inventory management settings
- Inventory items with various stock levels
- Complete procurement workflow data
- Automated purchase requests
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import db_manager
from datetime import datetime, timedelta
import random

def create_enhanced_storerooms():
    """Create storerooms with enhanced inventory management settings"""
    print("Creating enhanced storerooms...")
    
    storerooms_data = [
        {
            'storeroom_id': 'SR-MLT-001',
            'name': 'Main Laboratory Storage',
            'description': 'Primary storage for laboratory supplies and reagents',
            'tenant_id': 1,  # Mayiladuthurai Hub
            'location_details': 'Ground Floor, Building A, Room 101',
            'capacity': 500.00,
            'status': 'active',
            'manager_name': 'Dr. <PERSON><PERSON>',
            'manager_contact': '+91-9876543210',
            'min_quantity_threshold': 10.00,
            'max_quantity_limit': 1000.00,
            'safety_stock_level': 20.00,
            'reorder_quantity': 100.00,
            'auto_reorder_enabled': True,
            'reorder_point_days': 7,
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'storeroom_id': 'SR-MLT-002',
            'name': 'Chemical Storage',
            'description': 'Specialized storage for chemicals and hazardous materials',
            'tenant_id': 1,  # Mayiladuthurai Hub
            'location_details': 'Ground Floor, Building B, Room 205',
            'capacity': 300.00,
            'status': 'active',
            'manager_name': 'Ms. Priya Sharma',
            'manager_contact': '+91-9876543211',
            'min_quantity_threshold': 5.00,
            'max_quantity_limit': 500.00,
            'safety_stock_level': 15.00,
            'reorder_quantity': 50.00,
            'auto_reorder_enabled': True,
            'reorder_point_days': 5,
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'storeroom_id': 'SR-SKZ-001',
            'name': 'Sirkazhi Branch Storage',
            'description': 'Main storage for Sirkazhi franchise',
            'tenant_id': 2,  # Sirkazhi
            'location_details': 'First Floor, Main Building',
            'capacity': 200.00,
            'status': 'active',
            'manager_name': 'Mr. Arun Krishnan',
            'manager_contact': '+91-9876543212',
            'min_quantity_threshold': 8.00,
            'max_quantity_limit': 300.00,
            'safety_stock_level': 12.00,
            'reorder_quantity': 75.00,
            'auto_reorder_enabled': True,
            'reorder_point_days': 10,
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
    ]
    
    for storeroom_data in storerooms_data:
        try:
            # Check if storeroom already exists
            existing = db_manager.execute_query(
                "SELECT id FROM storerooms WHERE storeroom_id = ?", 
                [storeroom_data['storeroom_id']]
            )
            
            if existing:
                # Update existing storeroom
                db_manager.update_record('storerooms', existing[0]['id'], storeroom_data)
                print(f"✓ Updated storeroom: {storeroom_data['name']}")
            else:
                # Create new storeroom
                storeroom_id = db_manager.insert_record('storerooms', storeroom_data)
                print(f"✓ Created storeroom: {storeroom_data['name']} (ID: {storeroom_id})")
                
        except Exception as e:
            print(f"✗ Error creating storeroom {storeroom_data['name']}: {e}")

def create_inventory_master_data():
    """Create inventory master data for testing"""
    print("Creating inventory master data...")
    
    inventory_items = [
        {
            'item_code': 'LAB-001',
            'name': 'Blood Collection Tubes (EDTA)',
            'description': 'Purple top tubes for blood collection with EDTA anticoagulant',
            'category': 'Laboratory Supplies',
            'unit_of_measure': 'pieces',
            'status': 'active',
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'item_code': 'LAB-002',
            'name': 'Glucose Reagent Kit',
            'description': 'Reagent kit for glucose testing',
            'category': 'Reagents',
            'unit_of_measure': 'kit',
            'status': 'active',
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'item_code': 'LAB-003',
            'name': 'Disposable Syringes (5ml)',
            'description': '5ml disposable syringes for sample collection',
            'category': 'Medical Supplies',
            'unit_of_measure': 'pieces',
            'status': 'active',
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'item_code': 'CHEM-001',
            'name': 'Sodium Chloride Solution',
            'description': '0.9% Sodium Chloride solution for laboratory use',
            'category': 'Chemicals',
            'unit_of_measure': 'liter',
            'status': 'active',
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        },
        {
            'item_code': 'CHEM-002',
            'name': 'Distilled Water',
            'description': 'Laboratory grade distilled water',
            'category': 'Chemicals',
            'unit_of_measure': 'liter',
            'status': 'active',
            'created_by': 1,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
    ]
    
    # First, ensure inventory_master table exists
    try:
        # Use a direct database connection for table creation
        import sqlite3
        conn = sqlite3.connect('data/avini_labs.db')
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory_master (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                unit_of_measure TEXT,
                status TEXT DEFAULT 'active',
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP
            )
        """)
        conn.commit()
        conn.close()
        print("✓ Inventory master table ready")
    except Exception as e:
        print(f"✗ Error creating inventory_master table: {e}")
    
    for item_data in inventory_items:
        try:
            # Check if item already exists
            existing = db_manager.execute_query(
                "SELECT id FROM inventory_master WHERE item_code = ?", 
                [item_data['item_code']]
            )
            
            if existing:
                # Update existing item
                db_manager.update_record('inventory_master', existing[0]['id'], item_data)
                print(f"✓ Updated inventory item: {item_data['name']}")
            else:
                # Create new item
                item_id = db_manager.insert_record('inventory_master', item_data)
                print(f"✓ Created inventory item: {item_data['name']} (ID: {item_id})")
                
        except Exception as e:
            print(f"✗ Error creating inventory item {item_data['name']}: {e}")

def create_inventory_stock_data():
    """Create inventory stock data with various levels"""
    print("Creating inventory stock data...")
    
    # Get storerooms and inventory items
    storerooms = db_manager.execute_query("SELECT * FROM storerooms WHERE status = 'active'")
    items = db_manager.execute_query("SELECT * FROM inventory_master WHERE status = 'active'")
    
    if not storerooms or not items:
        print("✗ No storerooms or inventory items found")
        return
    
    # Ensure inventory table exists
    try:
        # Use a direct database connection for table creation
        import sqlite3
        conn = sqlite3.connect('data/avini_labs.db')
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                storeroom_id INTEGER NOT NULL,
                item_id INTEGER NOT NULL,
                current_quantity DECIMAL(10,2) DEFAULT 0,
                reserved_quantity DECIMAL(10,2) DEFAULT 0,
                available_quantity DECIMAL(10,2) DEFAULT 0,
                last_purchase_cost DECIMAL(10,2) DEFAULT 0,
                average_cost DECIMAL(10,2) DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (storeroom_id) REFERENCES storerooms(id),
                FOREIGN KEY (item_id) REFERENCES inventory_master(id)
            )
        """)
        conn.commit()
        conn.close()
        print("✓ Inventory table ready")
    except Exception as e:
        print(f"✗ Error creating inventory table: {e}")
    
    # Create inventory records with varying stock levels
    for storeroom in storerooms:
        for item in items:
            # Create different stock scenarios
            min_threshold = storeroom.get('min_quantity_threshold', 10)
            
            # 30% chance of low stock (below threshold)
            # 50% chance of normal stock (above threshold)
            # 20% chance of high stock (near max)
            
            rand = random.random()
            if rand < 0.3:
                # Low stock - below threshold
                current_qty = random.uniform(0, min_threshold * 0.8)
            elif rand < 0.8:
                # Normal stock - above threshold
                current_qty = random.uniform(min_threshold * 1.2, min_threshold * 5)
            else:
                # High stock
                current_qty = random.uniform(min_threshold * 5, min_threshold * 10)
            
            inventory_data = {
                'storeroom_id': storeroom['id'],
                'item_id': item['id'],
                'current_quantity': round(current_qty, 2),
                'reserved_quantity': 0,
                'available_quantity': round(current_qty, 2),
                'last_purchase_cost': round(random.uniform(10, 500), 2),
                'average_cost': round(random.uniform(10, 500), 2),
                'last_updated': datetime.now().isoformat(),
                'status': 'active',
                'created_by': 1,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            try:
                # Check if inventory record already exists
                existing = db_manager.execute_query(
                    "SELECT id FROM inventory WHERE storeroom_id = ? AND item_id = ?",
                    [storeroom['id'], item['id']]
                )
                
                if existing:
                    # Update existing record
                    db_manager.update_record('inventory', existing[0]['id'], inventory_data)
                    print(f"✓ Updated inventory: {item['name']} in {storeroom['name']} (Qty: {current_qty})")
                else:
                    # Create new record
                    inv_id = db_manager.insert_record('inventory', inventory_data)
                    print(f"✓ Created inventory: {item['name']} in {storeroom['name']} (Qty: {current_qty})")
                    
            except Exception as e:
                print(f"✗ Error creating inventory record: {e}")

def main():
    """Main function to create all test data"""
    print("=== Creating Comprehensive Procurement Test Data ===")
    
    try:
        create_enhanced_storerooms()
        print()
        
        create_inventory_master_data()
        print()
        
        create_inventory_stock_data()
        print()
        
        print("=== Test Data Creation Complete ===")
        print("You can now:")
        print("1. View enhanced storerooms in Settings > Storerooms")
        print("2. Check inventory levels in Inventory Management")
        print("3. Trigger automated procurement checks")
        print("4. View generated purchase requests")
        
    except Exception as e:
        print(f"✗ Error creating test data: {e}")

if __name__ == "__main__":
    main()
