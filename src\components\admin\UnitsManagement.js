import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Button, 
  Row, 
  Col, 
  Alert, 
  Table, 
  Modal, 
  Badge,
  Spinner,
  ButtonGroup
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faRuler,
  faPlus,
  faEdit,
  faTrash,
  faSave,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import unitsAPI from '../../services/unitsAPI';
import {
  TextInput,
  SelectInput,
  SuccessModal,
  ErrorModal
} from '../common';

const UnitsManagement = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [units, setUnits] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  
  // Form states
  const [editingUnit, setEditingUnit] = useState(null);
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    category: '',
    is_active: true
  });

  // Load initial data
  useEffect(() => {
    loadUnits();
    loadCategories();
  }, []);

  const loadUnits = async () => {
    setLoading(true);
    try {
      const response = await unitsAPI.getUnits({ active_only: false });
      setUnits(response.data.data || []);
    } catch (error) {
      setErrorMessage('Failed to load units: ' + error.message);
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await unitsAPI.getUnitCategories();
      setCategories(response.data.data || []);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const handleAddUnit = async () => {
    if (!formData.code || !formData.name || !formData.category) {
      setErrorMessage('Please fill in all required fields');
      setShowErrorModal(true);
      return;
    }

    setLoading(true);
    try {
      await unitsAPI.createUnit(formData);
      setSuccessMessage(`Unit "${formData.name}" created successfully`);
      setShowSuccessModal(true);
      setShowAddModal(false);
      resetForm();
      await loadUnits();
      await loadCategories();
    } catch (error) {
      setErrorMessage('Failed to create unit: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleEditUnit = async () => {
    if (!formData.code || !formData.name || !formData.category) {
      setErrorMessage('Please fill in all required fields');
      setShowErrorModal(true);
      return;
    }

    setLoading(true);
    try {
      await unitsAPI.updateUnit(editingUnit.id, formData);
      setSuccessMessage(`Unit "${formData.name}" updated successfully`);
      setShowSuccessModal(true);
      setShowEditModal(false);
      setEditingUnit(null);
      resetForm();
      await loadUnits();
      await loadCategories();
    } catch (error) {
      setErrorMessage('Failed to update unit: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUnit = async (unit) => {
    if (window.confirm(`Are you sure you want to delete the unit "${unit.name}"?`)) {
      setLoading(true);
      try {
        await unitsAPI.deleteUnit(unit.id);
        setSuccessMessage(`Unit "${unit.name}" deleted successfully`);
        setShowSuccessModal(true);
        await loadUnits();
      } catch (error) {
        setErrorMessage('Failed to delete unit: ' + (error.response?.data?.error || error.message));
        setShowErrorModal(true);
      } finally {
        setLoading(false);
      }
    }
  };

  const openAddModal = () => {
    resetForm();
    setShowAddModal(true);
  };

  const openEditModal = (unit) => {
    setEditingUnit(unit);
    setFormData({
      code: unit.code,
      name: unit.name,
      description: unit.description || '',
      category: unit.category,
      is_active: unit.is_active
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      category: '',
      is_active: true
    });
  };

  const getCategoryBadgeVariant = (category) => {
    const variants = {
      'count': 'primary',
      'weight': 'success',
      'volume': 'info',
      'length': 'warning',
      'packaging': 'secondary'
    };
    return variants[category] || 'secondary';
  };

  const filteredUnits = selectedCategory 
    ? units.filter(unit => unit.category === selectedCategory)
    : units;

  if (loading && units.length === 0) {
    return (
      <div className="text-center my-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-2">Loading units...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="mb-1">
            <FontAwesomeIcon icon={faRuler} className="me-2" />
            Units of Measure Management
          </h5>
          <p className="text-muted mb-0">Manage global units of measure for the system</p>
        </div>
        <Button 
          variant="primary" 
          size="sm"
          onClick={openAddModal}
          disabled={loading}
        >
          <FontAwesomeIcon icon={faPlus} className="me-2" />
          Add Unit
        </Button>
      </div>

      {/* Filters */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Filter by Category</Form.Label>
                <Form.Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={8} className="d-flex align-items-end">
              <div className="text-muted">
                Showing {filteredUnits.length} of {units.length} units
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Units Table */}
      <Card>
        <Card.Body className="p-0">
          {filteredUnits.length === 0 ? (
            <div className="text-center p-4 text-muted">
              <FontAwesomeIcon icon={faInfoCircle} size="2x" className="mb-2" />
              <p>No units found</p>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead className="table-light">
                <tr>
                  <th>Code</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Description</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUnits.map((unit) => (
                  <tr key={unit.id}>
                    <td>
                      <code className="text-primary">{unit.code}</code>
                    </td>
                    <td className="fw-semibold">{unit.name}</td>
                    <td>
                      <Badge bg={getCategoryBadgeVariant(unit.category)}>
                        {unit.category}
                      </Badge>
                    </td>
                    <td className="text-muted">{unit.description || '-'}</td>
                    <td>
                      <Badge bg={unit.is_active ? 'success' : 'secondary'}>
                        {unit.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>
                      <ButtonGroup size="sm">
                        <Button
                          variant="outline-primary"
                          onClick={() => openEditModal(unit)}
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          onClick={() => handleDeleteUnit(unit)}
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </ButtonGroup>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Add Unit Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add New Unit</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <TextInput
                  name="code"
                  label="Unit Code"
                  value={formData.code}
                  onChange={(e) => setFormData(prev => ({...prev, code: e.target.value}))}
                  placeholder="e.g., kg, pcs, liter"
                  required
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="name"
                  label="Unit Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                  placeholder="e.g., Kilogram, Pieces, Liter"
                  required
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <SelectInput
                  name="category"
                  label="Category"
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({...prev, category: e.target.value}))}
                  options={[
                    { value: 'count', label: 'Count' },
                    { value: 'weight', label: 'Weight' },
                    { value: 'volume', label: 'Volume' },
                    { value: 'length', label: 'Length' },
                    { value: 'packaging', label: 'Packaging' }
                  ]}
                  required
                />
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Status</Form.Label>
                  <Form.Check
                    type="switch"
                    id="is-active-add"
                    label="Active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({...prev, is_active: e.target.checked}))}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
                    placeholder="Optional description..."
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleAddUnit} disabled={loading}>
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Creating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Create Unit
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Unit Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Unit</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <TextInput
                  name="code"
                  label="Unit Code"
                  value={formData.code}
                  onChange={(e) => setFormData(prev => ({...prev, code: e.target.value}))}
                  placeholder="e.g., kg, pcs, liter"
                  required
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="name"
                  label="Unit Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                  placeholder="e.g., Kilogram, Pieces, Liter"
                  required
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <SelectInput
                  name="category"
                  label="Category"
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({...prev, category: e.target.value}))}
                  options={[
                    { value: 'count', label: 'Count' },
                    { value: 'weight', label: 'Weight' },
                    { value: 'volume', label: 'Volume' },
                    { value: 'length', label: 'Length' },
                    { value: 'packaging', label: 'Packaging' }
                  ]}
                  required
                />
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Status</Form.Label>
                  <Form.Check
                    type="switch"
                    id="is-active-edit"
                    label="Active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({...prev, is_active: e.target.checked}))}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
                    placeholder="Optional description..."
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleEditUnit} disabled={loading}>
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Updating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Update Unit
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        message={successMessage}
      />

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        message={errorMessage}
      />
    </div>
  );
};

export default UnitsManagement;
