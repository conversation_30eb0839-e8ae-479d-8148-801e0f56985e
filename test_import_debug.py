#!/usr/bin/env python3
"""
Test script to check for import issues in procurement routes
"""
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_procurement_routes_import():
    """Test if procurement routes can be imported without errors"""
    try:
        print("🔍 Testing procurement routes import...")
        
        # Try to import the procurement routes
        from routes.procurement_routes_db import procurement_bp
        print("✅ procurement_routes_db imported successfully")
        
        # Check if the confirm function exists
        if hasattr(procurement_bp, 'deferred_functions'):
            print(f"📄 Blueprint has {len(procurement_bp.deferred_functions)} deferred functions")
        
        # Try to import the specific function
        from routes.procurement_routes_db import confirm_delivery_note
        print("✅ confirm_delivery_note function imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def test_dependencies():
    """Test if all dependencies are available"""
    try:
        print("\n🔍 Testing dependencies...")
        
        # Test database manager
        from database_manager import db_manager
        print("✅ database_manager imported")
        
        # Test utils
        from utils import token_required, require_module_access
        print("✅ utils imported")
        
        # Test delivery service
        from services.delivery_service import DeliveryService
        print("✅ delivery_service imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Dependency error: {e}")
        return False

def test_syntax_check():
    """Test if the procurement routes file has syntax errors"""
    try:
        print("\n🔍 Testing syntax...")
        
        # Read the file and try to compile it
        with open('backend/routes/procurement_routes_db.py', 'r') as f:
            content = f.read()
        
        # Try to compile the code
        compile(content, 'backend/routes/procurement_routes_db.py', 'exec')
        print("✅ No syntax errors found")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def check_confirm_function():
    """Check if the confirm function is properly defined"""
    try:
        print("\n🔍 Checking confirm function definition...")
        
        # Read the file and look for the confirm function
        with open('backend/routes/procurement_routes_db.py', 'r') as f:
            content = f.read()
        
        # Check if the function is defined
        if 'def confirm_delivery_note(' in content:
            print("✅ confirm_delivery_note function found in file")
            
            # Check if the route decorator is present
            if '@procurement_bp.route(\'/api/procurement/delivery-notes/<int:dn_id>/confirm\', methods=[\'POST\'])' in content:
                print("✅ Route decorator found")
            else:
                print("❌ Route decorator not found")
                return False
                
            # Check if decorators are present
            if '@token_required' in content and '@require_module_access' in content:
                print("✅ Required decorators found")
            else:
                print("❌ Required decorators missing")
                return False
                
            return True
        else:
            print("❌ confirm_delivery_note function not found in file")
            return False
            
    except Exception as e:
        print(f"❌ Error checking function: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Import Debug Test")
    print("=" * 50)
    
    tests = [
        ("Syntax Check", test_syntax_check),
        ("Dependencies", test_dependencies),
        ("Procurement Routes Import", test_procurement_routes_import),
        ("Confirm Function Check", check_confirm_function),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n🎉 All imports are working correctly!")
        print("💡 The issue might be:")
        print("  1. Server needs to be restarted")
        print("  2. Server is running an older version of the code")
        print("  3. There's a caching issue")
    else:
        print("\n⚠️  Some imports failed. Fix these issues first.")
    
    return all_passed

if __name__ == "__main__":
    main()
