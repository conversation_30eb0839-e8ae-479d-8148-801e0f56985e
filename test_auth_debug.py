#!/usr/bin/env python3
"""
Test script to debug authentication issues
"""
import requests
import json
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_login_and_get_token():
    """Test login and get a valid token"""
    try:
        print("🔍 Testing login to get authentication token...")
        
        # Try to login with default admin credentials
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            'http://localhost:5002/api/auth/login',
            json=login_data,
            timeout=10
        )
        
        print(f"📊 Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            user = data.get('user')
            
            print("✅ Login successful!")
            print(f"📄 User: {user.get('username')} (Role: {user.get('role')})")
            print(f"📄 Token: {token[:20]}..." if token else "No token received")
            
            return token, user
        else:
            print(f"❌ Login failed: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None, None

def test_delivery_notes_with_auth(token):
    """Test delivery notes endpoint with authentication"""
    try:
        print("\n🔍 Testing delivery notes with authentication...")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            'http://localhost:5002/api/procurement/delivery-notes',
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Delivery notes retrieved successfully!")
            print(f"📄 Success: {data.get('success')}")
            print(f"📄 Data count: {len(data.get('data', []))}")
            print(f"📄 Pagination: {data.get('pagination')}")
            
            # Show sample delivery notes
            delivery_notes = data.get('data', [])
            if delivery_notes:
                print("📄 Sample delivery notes:")
                for i, dn in enumerate(delivery_notes[:3], 1):
                    print(f"  {i}. ID: {dn.get('id')}, Number: {dn.get('delivery_number')}, Status: {dn.get('status')}")
            
            return True
        else:
            print(f"❌ Request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_delivery_note_detail(token, note_id=5):
    """Test getting a specific delivery note"""
    try:
        print(f"\n🔍 Testing delivery note detail (ID: {note_id})...")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f'http://localhost:5002/api/procurement/delivery-notes/{note_id}',
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Delivery note detail retrieved successfully!")
            print(f"📄 ID: {data.get('id')}, Number: {data.get('delivery_number')}")
            print(f"📄 Status: {data.get('status')}, Items: {len(data.get('items', []))}")
            return True
        else:
            print(f"❌ Request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_confirm_delivery(token, note_id=5):
    """Test confirming a delivery"""
    try:
        print(f"\n🔍 Testing delivery confirmation (ID: {note_id})...")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        confirm_data = {
            'signature': 'Test confirmation',
            'delivery_notes': 'Confirmed via API test'
        }
        
        url = f'http://localhost:5002/api/procurement/delivery-notes/{note_id}/confirm'
        print(f"📄 Testing URL: {url}")

        response = requests.post(
            url,
            headers=headers,
            json=confirm_data,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Delivery confirmation successful!")
            print(f"📄 Success: {data.get('success')}")
            print(f"📄 Message: {data.get('message')}")
            return True
        else:
            print(f"❌ Confirmation failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Confirmation error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Authentication Debug Test")
    print("=" * 50)
    
    # Step 1: Login and get token
    token, user = test_login_and_get_token()
    
    if not token:
        print("\n❌ Cannot proceed without authentication token")
        return False
    
    # Step 2: Test delivery notes endpoint
    success_list = test_delivery_notes_with_auth(token)
    
    # Step 3: Test delivery note detail
    success_detail = test_delivery_note_detail(token)
    
    # Step 4: Test delivery confirmation (only if note is in dispatched status)
    # Note: This will only work if there's a delivery note in 'dispatched' status
    success_confirm = test_confirm_delivery(token)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"  Authentication: {'✅ PASS' if token else '❌ FAIL'}")
    print(f"  Delivery Notes List: {'✅ PASS' if success_list else '❌ FAIL'}")
    print(f"  Delivery Note Detail: {'✅ PASS' if success_detail else '❌ FAIL'}")
    print(f"  Delivery Confirmation: {'✅ PASS' if success_confirm else '❌ FAIL'}")
    
    if token and success_list and success_detail:
        print("\n🎉 Backend API is working correctly!")
        print("💡 If frontend is still showing errors, check:")
        print("  1. Browser console for JavaScript errors")
        print("  2. Network tab for failed requests")
        print("  3. Authentication token in localStorage")
        print("  4. User permissions and role")
    
    return token and success_list and success_detail

if __name__ == "__main__":
    main()
