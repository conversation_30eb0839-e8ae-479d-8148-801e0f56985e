import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Tabs, Tab, Table, Button, Form, Modal,
  <PERSON><PERSON>, Spinner, Badge, InputGroup
} from 'react-bootstrap';
import {
  FaWarehouse, FaChartLine, FaCogs, FaExchangeAlt,
  FaDownload, FaEye, FaCog, FaCalculator
} from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const InventoryAccounting = () => {
  const [activeTab, setActiveTab] = useState('valuation');
  const [inventoryData, setInventoryData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [costingMethod, setCostingMethod] = useState('FIFO');

  useEffect(() => {
    loadInventoryData();
  }, [activeTab, costingMethod]);

  const loadInventoryData = async () => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = 1; // Get from context

      switch (activeTab) {
        case 'valuation':
          const valuationData = await accountingService.getInventoryValuation(tenantId, costingMethod);
          setInventoryData(valuationData);
          break;
        case 'movements':
          const movementsData = await accountingService.getInventoryMovements(tenantId);
          setInventoryData(movementsData);
          break;
        case 'cogs':
          const cogsData = await accountingService.getCOGSAnalysis(tenantId);
          setInventoryData(cogsData);
          break;
        case 'adjustments':
          const adjustmentsData = await accountingService.getInventoryAdjustments(tenantId);
          setInventoryData(adjustmentsData);
          break;
        default:
          break;
      }
    } catch (err) {
      setError(`Failed to load inventory data: ${err.message}`);
      console.error('Inventory data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (reportType) => {
    try {
      const tenantId = 1;
      const response = await accountingService.exportInventoryReport(reportType, tenantId, costingMethod);

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${reportType}-${new Date().toISOString().split('T')[0]}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError(`Failed to export report: ${err.message}`);
    }
  };

  const ValuationTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <h5>Inventory Valuation Report</h5>
        </Col>
        <Col md={6}>
          <Row>
            <Col md={6}>
              <Form.Select
                value={costingMethod}
                onChange={(e) => setCostingMethod(e.target.value)}
              >
                <option value="FIFO">FIFO Method</option>
                <option value="LIFO">LIFO Method</option>
                <option value="AVERAGE">Weighted Average</option>
              </Form.Select>
            </Col>
            <Col md={6} className="text-end">
              <Button
                variant="outline-primary"
                onClick={() => handleExport('inventory-valuation')}
              >
                <FaDownload className="me-1" />
                Export
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : inventoryData ? (
        <div>
          <Row className="mb-4">
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-primary">{inventoryData.total_items}</h4>
                  <p className="mb-0">Total Items</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-success">₹{inventoryData.total_value?.toLocaleString()}</h4>
                  <p className="mb-0">Total Value</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-warning">{inventoryData.low_stock_items}</h4>
                  <p className="mb-0">Low Stock Items</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-info">₹{inventoryData.average_cost?.toLocaleString()}</h4>
                  <p className="mb-0">Average Cost</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Table responsive striped hover>
            <thead>
              <tr>
                <th>Item Code</th>
                <th>Item Name</th>
                <th>Category</th>
                <th>Quantity</th>
                <th>Unit Cost</th>
                <th>Total Value</th>
                <th>Costing Method</th>
                <th>Last Updated</th>
              </tr>
            </thead>
            <tbody>
              {inventoryData.items?.map((item) => (
                <tr key={item.id}>
                  <td>{item.item_code}</td>
                  <td>{item.item_name}</td>
                  <td>{item.category}</td>
                  <td>
                    {item.quantity}
                    {item.quantity <= item.reorder_level && (
                      <Badge bg="warning" className="ms-2">Low Stock</Badge>
                    )}
                  </td>
                  <td>₹{item.unit_cost?.toLocaleString()}</td>
                  <td>₹{item.total_value?.toLocaleString()}</td>
                  <td>
                    <Badge bg="info">{item.costing_method}</Badge>
                  </td>
                  <td>{new Date(item.last_updated).toLocaleDateString()}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
      ) : null}
    </div>
  );

  const COGSTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <h5>Cost of Goods Sold Analysis</h5>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="outline-primary"
            onClick={() => handleExport('cogs-analysis')}
          >
            <FaDownload className="me-1" />
            Export COGS Report
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : inventoryData ? (
        <div>
          <Row className="mb-4">
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-primary">₹{inventoryData.total_cogs?.toLocaleString()}</h4>
                  <p className="mb-0">Total COGS</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-success">₹{inventoryData.average_cogs?.toLocaleString()}</h4>
                  <p className="mb-0">Average COGS</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-info">{inventoryData.cogs_transactions}</h4>
                  <p className="mb-0">COGS Transactions</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Table responsive striped hover>
            <thead>
              <tr>
                <th>Date</th>
                <th>Item</th>
                <th>Quantity Sold</th>
                <th>Unit Cost</th>
                <th>COGS Amount</th>
                <th>Sale Price</th>
                <th>Gross Margin</th>
                <th>Method</th>
              </tr>
            </thead>
            <tbody>
              {inventoryData.cogs_details?.map((transaction, index) => (
                <tr key={index}>
                  <td>{new Date(transaction.transaction_date).toLocaleDateString()}</td>
                  <td>{transaction.item_name}</td>
                  <td>{transaction.quantity_sold}</td>
                  <td>₹{transaction.unit_cost?.toLocaleString()}</td>
                  <td>₹{transaction.cogs_amount?.toLocaleString()}</td>
                  <td>₹{transaction.sale_price?.toLocaleString()}</td>
                  <td>
                    <Badge bg={transaction.gross_margin > 0 ? 'success' : 'danger'}>
                      ₹{transaction.gross_margin?.toLocaleString()}
                    </Badge>
                  </td>
                  <td>
                    <Badge bg="secondary">{transaction.costing_method}</Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
      ) : null}
    </div>
  );

  return (
    <div className="inventory-accounting">
      <Card>
        <Card.Header>
          <Row>
            <Col>
              <h5 className="mb-0 text-primary">
                <FaWarehouse className="me-2" />
                Inventory Accounting
              </h5>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body style={{background:"#f8f9fa"}}>
          {error && <Alert variant="danger">{error}</Alert>}

          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-3"
          >
            <Tab eventKey="valuation" title="Inventory Valuation">
              <ValuationTab />
            </Tab>
            <Tab eventKey="cogs" title="COGS Analysis">
              <COGSTab />
            </Tab>
          </Tabs>
        </Card.Body>
      </Card>
    </div>
  );
};

export default InventoryAccounting;
