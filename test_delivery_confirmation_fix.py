#!/usr/bin/env python3
"""
Test script to verify the delivery confirmation fix
"""
import requests
import json
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

BASE_URL = 'http://localhost:5002'

def get_auth_token():
    """Get authentication token"""
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = requests.post(f'{BASE_URL}/api/auth/login', json=login_data)
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ Authentication successful")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_delivery_confirmation(token, delivery_id):
    """Test delivery confirmation endpoint"""
    try:
        print(f"\n🔍 Testing delivery confirmation for ID: {delivery_id}")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        confirm_data = {
            'signature': 'Test confirmation signature',
            'delivery_notes': 'Confirmed via test script - testing fix'
        }
        
        url = f'{BASE_URL}/api/procurement/delivery-notes/{delivery_id}/confirm'
        print(f"📄 Testing URL: {url}")
        print(f"📄 Request data: {confirm_data}")

        response = requests.post(
            url,
            headers=headers,
            json=confirm_data,
            timeout=30
        )
        
        print(f"📄 Response status: {response.status_code}")
        print(f"📄 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Delivery confirmation successful!")
            print(f"📄 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Delivery confirmation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📄 Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📄 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def check_delivery_status_after_confirmation(token, delivery_id):
    """Check delivery status after confirmation"""
    try:
        print(f"\n🔍 Checking delivery status after confirmation for ID: {delivery_id}")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        url = f'{BASE_URL}/api/procurement/delivery-notes/{delivery_id}'
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status')
            print(f"✅ Current status: {status}")
            if status == 'delivered':
                print("✅ Status correctly updated to 'delivered'")
                return True
            else:
                print(f"⚠️  Expected status 'delivered', got '{status}'")
                return False
        else:
            print(f"❌ Failed to get delivery status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Status check error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Delivery Confirmation Fix")
    print("=" * 50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return False
    
    # Test delivery confirmation with ID 5 (should be dispatched)
    delivery_id = 5
    
    print(f"\n📋 Testing delivery confirmation for delivery note {delivery_id}")
    success = test_delivery_confirmation(token, delivery_id)
    
    if success:
        # Check status after confirmation
        status_check = check_delivery_status_after_confirmation(token, delivery_id)
        if status_check:
            print("\n✅ All tests passed! Delivery confirmation is working correctly.")
            return True
        else:
            print("\n⚠️  Confirmation succeeded but status check failed.")
            return False
    else:
        print(f"\n❌ Delivery confirmation test failed for delivery {delivery_id}")
        
        # Try with another delivery note
        print(f"\n📋 Trying with delivery note 6...")
        success2 = test_delivery_confirmation(token, 6)
        if success2:
            print("\n✅ Delivery confirmation working with delivery note 6!")
            return True
        else:
            print("\n❌ Delivery confirmation still failing")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
