"""
AVINI Labs Accounting Service
Core accounting functionality including general ledger, journal entries, and account management
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
import json

from database_manager import db_manager

logger = logging.getLogger(__name__)

class AccountingService:
    """Core accounting service for managing chart of accounts, journal entries, and general ledger"""
    
    def __init__(self):
        self.db = db_manager
    
    # ============================================================================
    # CHART OF ACCOUNTS MANAGEMENT
    # ============================================================================
    
    def create_account(self, account_data: Dict) -> int:
        """Create a new account in the chart of accounts"""
        try:
            # Validate required fields
            required_fields = ['account_code', 'account_name', 'account_type', 'account_subtype', 'tenant_id']
            for field in required_fields:
                if field not in account_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Check if account code already exists
            existing = self.db.execute_query(
                "SELECT id FROM chart_of_accounts WHERE account_code = ? AND tenant_id = ?",
                (account_data['account_code'], account_data['tenant_id'])
            )
            if existing:
                raise ValueError(f"Account code {account_data['account_code']} already exists")
            
            # Set defaults
            account_data.setdefault('account_level', 1)
            account_data.setdefault('is_control_account', False)
            account_data.setdefault('is_active', True)
            account_data.setdefault('opening_balance', 0)
            account_data.setdefault('current_balance', 0)
            account_data.setdefault('debit_balance', 0)
            account_data.setdefault('credit_balance', 0)
            
            account_id = self.db.insert_record('chart_of_accounts', account_data)
            logger.info(f"Created account {account_data['account_code']} with ID {account_id}")
            return account_id
            
        except Exception as e:
            logger.error(f"Error creating account: {str(e)}")
            raise

    def update_account(self, account_id: int, account_data: Dict) -> bool:
        """Update an existing account"""
        try:
            # Check if account exists
            existing_account = self.db.execute_query(
                "SELECT * FROM chart_of_accounts WHERE id = ?",
                (account_id,)
            )
            if not existing_account:
                raise ValueError(f"Account with ID {account_id} not found")

            existing_account = existing_account[0]

            # Check if account has transactions (business rule: can't edit accounts with transactions)
            transactions = self.db.execute_query(
                "SELECT COUNT(*) as count FROM journal_entry_line_items WHERE account_id = ?",
                (account_id,)
            )
            if transactions and transactions[0]['count'] > 0:
                raise ValueError("Cannot edit account that has existing transactions")

            # Validate required fields
            required_fields = ['account_name', 'account_type', 'tenant_id']
            for field in required_fields:
                if field not in account_data:
                    raise ValueError(f"Missing required field: {field}")

            # Validate account type
            valid_types = ['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE', 'COST_OF_GOODS_SOLD']
            if account_data['account_type'] not in valid_types:
                raise ValueError(f"Invalid account type: {account_data['account_type']}")

            # Set defaults for optional fields
            account_data.setdefault('account_subtype', existing_account['account_subtype'])
            account_data.setdefault('description', existing_account['description'])
            account_data.setdefault('is_active', existing_account['is_active'])
            account_data.setdefault('parent_account_id', existing_account['parent_account_id'])

            # Update the account
            self.db.execute_query("""
                UPDATE chart_of_accounts
                SET account_name = ?, account_type = ?, account_subtype = ?,
                    parent_account_id = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND tenant_id = ?
            """, (
                account_data['account_name'],
                account_data['account_type'],
                account_data['account_subtype'],
                account_data.get('parent_account_id'),
                account_data.get('description'),
                account_data.get('is_active', True),
                account_id,
                account_data['tenant_id']
            ))

            logger.info(f"Account {account_id} updated successfully")
            return True

        except Exception as e:
            logger.error(f"Error updating account {account_id}: {str(e)}")
            raise

    def get_account_by_code(self, account_code: str, tenant_id: int) -> Optional[Dict]:
        """Get account by account code"""
        try:
            accounts = self.db.execute_query(
                "SELECT * FROM chart_of_accounts WHERE account_code = ? AND tenant_id = ?",
                (account_code, tenant_id)
            )
            return accounts[0] if accounts else None
        except Exception as e:
            logger.error(f"Error getting account by code {account_code}: {str(e)}")
            raise
    
    def get_chart_of_accounts(self, tenant_id: int) -> List[Dict]:
        """Get all accounts in the chart of accounts"""
        try:
            return self.db.execute_query(
                "SELECT * FROM chart_of_accounts WHERE tenant_id = ? AND is_active = 1 ORDER BY account_code",
                (tenant_id,)
            )
        except Exception as e:
            logger.error(f"Error getting chart of accounts: {str(e)}")
            raise

    def get_accounts_by_type(self, account_type: str, tenant_id: int) -> List[Dict]:
        """Get all accounts of a specific type"""
        try:
            return self.db.execute_query(
                "SELECT * FROM chart_of_accounts WHERE account_type = ? AND tenant_id = ? AND is_active = 1 ORDER BY account_code",
                (account_type, tenant_id)
            )
        except Exception as e:
            logger.error(f"Error getting accounts by type {account_type}: {str(e)}")
            raise
    
    def update_account_balance(self, account_id: int, debit_amount: Decimal = 0, credit_amount: Decimal = 0) -> bool:
        """Update account balance based on debit/credit amounts"""
        try:
            # Get current account
            account = self.db.execute_query("SELECT * FROM chart_of_accounts WHERE id = ?", (account_id,))
            if not account:
                raise ValueError(f"Account with ID {account_id} not found")
            
            account = account[0]
            current_debit = Decimal(str(account.get('debit_balance', 0)))
            current_credit = Decimal(str(account.get('credit_balance', 0)))
            
            # Update balances
            new_debit = current_debit + Decimal(str(debit_amount))
            new_credit = current_credit + Decimal(str(credit_amount))
            
            # Calculate net balance based on account type
            account_type = account['account_type']
            if account_type in ['ASSET', 'EXPENSE', 'COST_OF_GOODS_SOLD']:
                # Normal debit balance accounts
                current_balance = new_debit - new_credit
            else:
                # Normal credit balance accounts (LIABILITY, EQUITY, REVENUE)
                current_balance = new_credit - new_debit
            
            # Update the account
            update_data = {
                'debit_balance': float(new_debit),
                'credit_balance': float(new_credit),
                'current_balance': float(current_balance)
            }
            
            self.db.update_record('chart_of_accounts', account_id, update_data)
            return True
            
        except Exception as e:
            logger.error(f"Error updating account balance for account {account_id}: {str(e)}")
            raise
    
    # ============================================================================
    # JOURNAL ENTRY MANAGEMENT
    # ============================================================================
    
    def create_journal_entry(self, journal_data: Dict, line_items: List[Dict]) -> int:
        """Create a new journal entry with line items"""
        try:
            # Validate journal entry data
            required_fields = ['journal_type', 'transaction_date', 'description', 'tenant_id']
            for field in required_fields:
                if field not in journal_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Validate line items
            if not line_items or len(line_items) < 2:
                raise ValueError("Journal entry must have at least 2 line items")
            
            # Calculate totals and validate balance
            total_debit = sum(Decimal(str(item.get('debit_amount', 0))) for item in line_items)
            total_credit = sum(Decimal(str(item.get('credit_amount', 0))) for item in line_items)
            
            if total_debit != total_credit:
                raise ValueError(f"Journal entry is not balanced: Debit {total_debit} != Credit {total_credit}")
            
            # Generate journal number
            journal_number = self._generate_journal_number(journal_data['journal_type'], journal_data['tenant_id'])
            
            # Set journal entry defaults
            journal_data.update({
                'journal_number': journal_number,
                'posting_date': journal_data.get('posting_date', journal_data['transaction_date']),
                'total_debit': float(total_debit),
                'total_credit': float(total_credit),
                'status': 'DRAFT',
                'is_auto_generated': journal_data.get('is_auto_generated', False)
            })
            
            # Insert journal entry
            journal_id = self.db.insert_record('journal_entries', journal_data)
            
            # Insert line items
            for i, line_item in enumerate(line_items, 1):
                line_item.update({
                    'journal_entry_id': journal_id,
                    'line_number': i,
                    'debit_amount': float(line_item.get('debit_amount', 0)),
                    'credit_amount': float(line_item.get('credit_amount', 0))
                })
                self.db.insert_record('journal_entry_lines', line_item)
            
            logger.info(f"Created journal entry {journal_number} with ID {journal_id}")
            return journal_id
            
        except Exception as e:
            logger.error(f"Error creating journal entry: {str(e)}")
            raise
    
    def post_journal_entry(self, journal_id: int, posted_by: int) -> bool:
        """Post a journal entry to update account balances"""
        try:
            # Get journal entry
            journal = self.db.execute_query("SELECT * FROM journal_entries WHERE id = ?", (journal_id,))
            if not journal:
                raise ValueError(f"Journal entry with ID {journal_id} not found")
            
            journal = journal[0]
            if journal['status'] != 'DRAFT':
                raise ValueError(f"Journal entry {journal['journal_number']} is not in draft status")
            
            # Get line items
            line_items = self.db.execute_query(
                "SELECT * FROM journal_entry_lines WHERE journal_entry_id = ? ORDER BY line_number",
                (journal_id,)
            )
            
            # Update account balances
            for line_item in line_items:
                self.update_account_balance(
                    line_item['account_id'],
                    Decimal(str(line_item['debit_amount'])),
                    Decimal(str(line_item['credit_amount']))
                )
            
            # Update journal entry status
            update_data = {
                'status': 'POSTED',
                'posted_by': posted_by,
                'posted_at': datetime.now().isoformat()
            }
            self.db.update_record('journal_entries', journal_id, update_data)
            
            logger.info(f"Posted journal entry {journal['journal_number']}")
            return True
            
        except Exception as e:
            logger.error(f"Error posting journal entry {journal_id}: {str(e)}")
            raise
    
    def reverse_journal_entry(self, journal_id: int, reversal_reason: str, created_by: int) -> int:
        """Create a reversal journal entry"""
        try:
            # Get original journal entry
            original = self.db.execute_query("SELECT * FROM journal_entries WHERE id = ?", (journal_id,))
            if not original:
                raise ValueError(f"Journal entry with ID {journal_id} not found")
            
            original = original[0]
            if original['status'] != 'POSTED':
                raise ValueError("Can only reverse posted journal entries")
            
            # Get original line items
            original_lines = self.db.execute_query(
                "SELECT * FROM journal_entry_lines WHERE journal_entry_id = ? ORDER BY line_number",
                (journal_id,)
            )
            
            # Create reversal entry data
            reversal_data = {
                'journal_type': 'REVERSAL',
                'transaction_date': date.today().isoformat(),
                'posting_date': date.today().isoformat(),
                'description': f"Reversal of {original['journal_number']}: {reversal_reason}",
                'reference_type': 'REVERSAL',
                'reference_id': journal_id,
                'reference_number': original['journal_number'],
                'tenant_id': original['tenant_id'],
                'created_by': created_by,
                'is_auto_generated': True
            }
            
            # Create reversal line items (swap debit/credit)
            reversal_lines = []
            for line in original_lines:
                reversal_lines.append({
                    'account_id': line['account_id'],
                    'debit_amount': line['credit_amount'],  # Swap
                    'credit_amount': line['debit_amount'],  # Swap
                    'description': f"Reversal: {line.get('description', '')}",
                    'cost_center_id': line.get('cost_center_id'),
                    'project_id': line.get('project_id'),
                    'department_id': line.get('department_id')
                })
            
            # Create reversal journal entry
            reversal_id = self.create_journal_entry(reversal_data, reversal_lines)
            
            # Auto-post the reversal
            self.post_journal_entry(reversal_id, created_by)
            
            # Update original entry to mark as reversed
            self.db.update_record('journal_entries', journal_id, {
                'status': 'REVERSED',
                'reversal_entry_id': reversal_id
            })
            
            logger.info(f"Created reversal entry for journal {original['journal_number']}")
            return reversal_id
            
        except Exception as e:
            logger.error(f"Error reversing journal entry {journal_id}: {str(e)}")
            raise
    
    # ============================================================================
    # HELPER METHODS
    # ============================================================================
    
    def _generate_journal_number(self, journal_type: str, tenant_id: int) -> str:
        """Generate a unique journal number"""
        try:
            # Get the latest journal number for this type and tenant
            result = self.db.execute_query(
                """SELECT journal_number FROM journal_entries 
                   WHERE journal_type = ? AND tenant_id = ? 
                   ORDER BY id DESC LIMIT 1""",
                (journal_type, tenant_id)
            )
            
            if result:
                last_number = result[0]['journal_number']
                # Extract number from format like "GJ-2024-001"
                parts = last_number.split('-')
                if len(parts) >= 3:
                    next_num = int(parts[-1]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            # Generate new number
            year = datetime.now().year
            type_prefix = {
                'GENERAL': 'GJ',
                'SALES': 'SJ',
                'PURCHASE': 'PJ',
                'CASH_RECEIPT': 'CR',
                'CASH_PAYMENT': 'CP',
                'BANK_RECEIPT': 'BR',
                'BANK_PAYMENT': 'BP',
                'ADJUSTMENT': 'AJ',
                'REVERSAL': 'RJ'
            }.get(journal_type, 'GJ')
            
            return f"{type_prefix}-{year}-{next_num:03d}"
            
        except Exception as e:
            logger.error(f"Error generating journal number: {str(e)}")
            # Fallback to timestamp-based number
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            return f"JE-{timestamp}"
