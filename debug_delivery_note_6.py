#!/usr/bin/env python3
"""
Debug script to check delivery note 6 data and identify 500 error cause
"""
import sys
import os
import sqlite3

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def check_delivery_note_6_data():
    """Check the specific data for delivery note 6"""
    try:
        print("🔍 Checking Delivery Note 6 Data")
        print("-" * 50)
        
        from database_manager import db_manager
        
        # Get delivery note 6 with all related data
        dn_query = """
            SELECT dn.*, 
                   pr.storeroom_id,
                   s.name as storeroom_name,
                   t.name as to_tenant_name,
                   ft.name as from_tenant_name
            FROM delivery_notes dn
            LEFT JOIN purchase_requests pr ON dn.purchase_request_id = pr.id
            LEFT JOIN storerooms s ON pr.storeroom_id = s.id
            LEFT JOIN tenants t ON dn.to_tenant_id = t.id
            LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
            WHERE dn.id = 6
        """
        
        dn_results = db_manager.execute_query(dn_query)
        
        if not dn_results:
            print("❌ Delivery note 6 not found")
            return False
        
        dn = dn_results[0]
        print("✅ Delivery Note 6 found:")
        print(f"  ID: {dn['id']}")
        print(f"  Number: {dn['delivery_number']}")
        print(f"  Status: {dn['status']}")
        print(f"  From Tenant: {dn['from_tenant_id']} ({dn['from_tenant_name']})")
        print(f"  To Tenant: {dn['to_tenant_id']} ({dn['to_tenant_name']})")
        print(f"  Purchase Request ID: {dn['purchase_request_id']}")
        print(f"  Purchase Order ID: {dn['purchase_order_id']}")
        print(f"  Storeroom ID: {dn['storeroom_id']}")
        print(f"  Storeroom Name: {dn['storeroom_name']}")
        
        # Check delivery note items
        items_query = """
            SELECT * FROM delivery_note_items 
            WHERE delivery_note_id = 6
            ORDER BY id
        """
        items = db_manager.execute_query(items_query)
        
        print(f"\n📦 Delivery Note Items ({len(items)}):")
        for i, item in enumerate(items, 1):
            print(f"  {i}. {item['item_name']} - Qty: {item['quantity']} - Price: {item['unit_price']}")
        
        # Check if storeroom_id is missing (this could cause the 500 error)
        if not dn['storeroom_id']:
            print("\n⚠️  WARNING: storeroom_id is NULL - This will cause inventory update to fail!")
            
            # Check if we can find a default storeroom for the tenant
            storeroom_query = """
                SELECT * FROM storerooms 
                WHERE tenant_id = ? AND is_active = 1
                LIMIT 1
            """
            storerooms = db_manager.execute_query(storeroom_query, (dn['to_tenant_id'],))
            
            if storerooms:
                print(f"  💡 Available storeroom for tenant {dn['to_tenant_id']}: {storerooms[0]['name']} (ID: {storerooms[0]['id']})")
            else:
                print(f"  ❌ No storerooms found for tenant {dn['to_tenant_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking delivery note data: {e}")
        return False

def check_inventory_table_structure():
    """Check if inventory table has all required fields"""
    try:
        print("\n🔍 Checking Inventory Table Structure")
        print("-" * 50)
        
        db_path = os.path.join('backend', 'data', 'avini_labs.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check inventory table structure
        cursor.execute("PRAGMA table_info(inventory)")
        columns = cursor.fetchall()
        
        print("📋 Inventory table columns:")
        required_fields = ['name', 'sku', 'quantity', 'storeroom_id', 'tenant_id', 'created_by']
        
        existing_fields = [col[1] for col in columns]
        for field in required_fields:
            status = "✅" if field in existing_fields else "❌"
            print(f"  {status} {field}")
        
        # Check if storerooms table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='storerooms'")
        storerooms_exists = cursor.fetchone()
        
        if storerooms_exists:
            print("\n✅ Storerooms table exists")
            
            # Check storerooms for tenant 3 (delivery note 6's destination)
            cursor.execute("SELECT * FROM storerooms WHERE tenant_id = 3 AND is_active = 1")
            storerooms = cursor.fetchall()
            print(f"📦 Storerooms for tenant 3: {len(storerooms)}")
            for sr in storerooms:
                print(f"  - ID: {sr[0]}, Name: {sr[1]}")
        else:
            print("\n❌ Storerooms table does not exist")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking inventory structure: {e}")
        return False

def simulate_inventory_update():
    """Simulate the inventory update to see where it fails"""
    try:
        print("\n🔍 Simulating Inventory Update")
        print("-" * 50)
        
        from database_manager import db_manager
        
        # Get delivery note 6 items
        items_query = """
            SELECT * FROM delivery_note_items 
            WHERE delivery_note_id = 6
            ORDER BY id
        """
        items = db_manager.execute_query(items_query)
        
        if not items:
            print("❌ No items found for delivery note 6")
            return False
        
        # Try to find inventory for each item
        for item in items:
            print(f"\n📦 Processing item: {item['item_name']}")
            
            # This is the query that might be failing
            inventory_query = """
                SELECT * FROM inventory 
                WHERE (name = ? OR sku = ?) 
                AND storeroom_id = ? 
                AND tenant_id = ?
                AND is_active = 1
                LIMIT 1
            """
            
            # We need storeroom_id and tenant_id - let's use dummy values to test
            test_storeroom_id = 1  # This might be NULL causing the issue
            test_tenant_id = 3
            
            try:
                inventory_results = db_manager.execute_query(
                    inventory_query, 
                    (item['item_name'], item['item_name'], test_storeroom_id, test_tenant_id)
                )
                
                if inventory_results:
                    print(f"  ✅ Found existing inventory item: {inventory_results[0]['name']}")
                else:
                    print(f"  ⚠️  No existing inventory found - would create new item")
                    
            except Exception as e:
                print(f"  ❌ Inventory query failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error simulating inventory update: {e}")
        return False

def main():
    """Main debug function"""
    print("🚀 Debugging Delivery Note 6 - 500 Error Investigation")
    print("=" * 60)
    
    tests = [
        ("Delivery Note 6 Data", check_delivery_note_6_data),
        ("Inventory Table Structure", check_inventory_table_structure),
        ("Inventory Update Simulation", simulate_inventory_update),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 60)
    print("📊 Debug Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print("\n💡 Likely Causes of 500 Error:")
    print("  1. Missing storeroom_id in delivery note (NULL value)")
    print("  2. Database constraint violations in inventory update")
    print("  3. Missing required fields in inventory table")
    print("  4. Transaction timeout due to long-running operations")

if __name__ == "__main__":
    main()
