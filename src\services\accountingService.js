import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5002';

class AccountingService {
  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/accounting`,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Handle auth errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async healthCheck() {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Chart of Accounts
  async getChartOfAccounts(tenantId, accountType = null) {
    const params = { tenant_id: tenantId };
    if (accountType) params.account_type = accountType;
    
    const response = await this.api.get('/chart-of-accounts', { params });
    return response.data;
  }

  async createAccount(accountData) {
    const response = await this.api.post('/chart-of-accounts', accountData);
    return response.data;
  }

  async updateAccount(accountId, accountData) {
    const response = await this.api.put(`/chart-of-accounts/${accountId}`, accountData);
    return response.data;
  }

  async getAccountByCode(accountCode, tenantId) {
    const response = await this.api.get(`/chart-of-accounts/${accountCode}`, {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  // Journal Entries
  async getJournalEntries(tenantId, options = {}) {
    const params = { tenant_id: tenantId, ...options };
    const response = await this.api.get('/journal-entries', { params });
    return response.data;
  }

  async createJournalEntry(journalData) {
    const response = await this.api.post('/journal-entries', journalData);
    return response.data;
  }

  async postJournalEntry(journalId, postedBy) {
    const response = await this.api.post(`/journal-entries/${journalId}/post`, {
      posted_by: postedBy
    });
    return response.data;
  }

  async reverseJournalEntry(journalId, reversalReason, createdBy) {
    const response = await this.api.post(`/journal-entries/${journalId}/reverse`, {
      reversal_reason: reversalReason,
      created_by: createdBy
    });
    return response.data;
  }

  // Vendors
  async getVendors(tenantId) {
    const response = await this.api.get('/vendors', {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  async createVendor(vendorData) {
    const response = await this.api.post('/vendors', vendorData);
    return response.data;
  }

  // Customers
  async getCustomers(tenantId) {
    const response = await this.api.get('/customers', {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  async createCustomer(customerData) {
    const response = await this.api.post('/customers', customerData);
    return response.data;
  }

  // Purchase Invoices (Accounts Payable)
  async getPurchaseInvoices(tenantId, options = {}) {
    const params = { tenant_id: tenantId, ...options };
    const response = await this.api.get('/purchase-invoices', { params });
    return response.data;
  }

  async createPurchaseInvoice(invoiceData) {
    const response = await this.api.post('/purchase-invoices', invoiceData);
    return response.data;
  }

  async approvePurchaseInvoice(invoiceId, approvedBy) {
    const response = await this.api.post(`/purchase-invoices/${invoiceId}/approve`, {
      approved_by: approvedBy
    });
    return response.data;
  }

  // Reports
  async getAgedPayablesReport(tenantId, asOfDate = null) {
    const params = { tenant_id: tenantId };
    if (asOfDate) params.as_of_date = asOfDate;
    
    const response = await this.api.get('/reports/aged-payables', { params });
    return response.data;
  }

  async getAgedReceivablesReport(tenantId, asOfDate = null) {
    const params = { tenant_id: tenantId };
    if (asOfDate) params.as_of_date = asOfDate;
    
    const response = await this.api.get('/reports/aged-receivables', { params });
    return response.data;
  }

  async getTrialBalance(tenantId, asOfDate = null) {
    const params = { tenant_id: tenantId };
    if (asOfDate) params.as_of_date = asOfDate;
    
    const response = await this.api.get('/reports/trial-balance', { params });
    return response.data;
  }

  async getProfitLoss(tenantId, startDate, endDate) {
    const params = {
      tenant_id: tenantId,
      start_date: startDate,
      end_date: endDate
    };
    
    const response = await this.api.get('/reports/profit-loss', { params });
    return response.data;
  }

  async getBalanceSheet(tenantId, asOfDate = null) {
    const params = { tenant_id: tenantId };
    if (asOfDate) params.as_of_date = asOfDate;
    
    const response = await this.api.get('/reports/balance-sheet', { params });
    return response.data;
  }

  async getGeneralLedger(tenantId, accountId = null, startDate = null, endDate = null) {
    const params = { tenant_id: tenantId };
    if (accountId) params.account_id = accountId;
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    
    const response = await this.api.get('/reports/general-ledger', { params });
    return response.data;
  }

  // Tax Management
  async getTaxTypes() {
    const response = await this.api.get('/tax-types');
    return response.data;
  }

  async calculateGST(amount, gstRate = 18, includeTax = false) {
    const response = await this.api.post('/tax/calculate-gst', {
      amount,
      gst_rate: gstRate,
      include_tax: includeTax
    });
    return response.data;
  }

  async calculateTDS(amount, tdsRate = 10, threshold = null) {
    const response = await this.api.post('/tax/calculate-tds', {
      amount,
      tds_rate: tdsRate,
      threshold
    });
    return response.data;
  }

  async calculateCompositeTax(amount, taxConfig) {
    const response = await this.api.post('/tax/calculate-composite', {
      amount,
      tax_config: taxConfig
    });
    return response.data;
  }

  async getTaxSummaryReport(startDate, endDate) {
    const response = await this.api.get('/tax/summary-report', {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  }

  // Inventory Accounting
  async getInventoryValuationMethods() {
    const response = await this.api.get('/inventory/valuation-methods');
    return response.data;
  }

  async calculateInventoryCost(itemId, quantity, method = 'FIFO') {
    const response = await this.api.post('/inventory/calculate-cost', {
      item_id: itemId,
      quantity,
      method
    });
    return response.data;
  }

  async getInventoryValuationReport(asOfDate = null, method = 'FIFO') {
    const params = { method };
    if (asOfDate) params.as_of_date = asOfDate;
    
    const response = await this.api.get('/inventory/valuation-report', { params });
    return response.data;
  }

  async createInventoryTransaction(transactionData) {
    const response = await this.api.post('/inventory/transactions', transactionData);
    return response.data;
  }

  // Integration
  async syncBillingToAccounting(tenantId, createdBy) {
    const response = await this.api.post('/integration/sync-billing', {
      tenant_id: tenantId,
      created_by: createdBy
    });
    return response.data;
  }

  async syncProcurementToAccounting(tenantId, createdBy) {
    const response = await this.api.post('/integration/sync-procurement', {
      tenant_id: tenantId,
      created_by: createdBy
    });
    return response.data;
  }

  async completeAccountingSetup(tenantId, createdBy) {
    const response = await this.api.post('/integration/complete-setup', {
      tenant_id: tenantId,
      created_by: createdBy
    });
    return response.data;
  }

  // Accounts Receivable Methods

  async updateCustomer(customerId, customerData) {
    const response = await this.api.put(`/customers/${customerId}`, customerData);
    return response.data;
  }

  async getSalesInvoices(tenantId) {
    const response = await this.api.get('/sales-invoices', { params: { tenant_id: tenantId } });
    return response.data;
  }

  async createSalesInvoice(invoiceData) {
    const response = await this.api.post('/sales-invoices', invoiceData);
    return response.data;
  }

  async updateSalesInvoice(invoiceId, invoiceData) {
    const response = await this.api.put(`/sales-invoices/${invoiceId}`, invoiceData);
    return response.data;
  }

  async getCustomerPayments(tenantId) {
    const response = await this.api.get('/customer-payments', { params: { tenant_id: tenantId } });
    return response.data;
  }

  async createCustomerPayment(paymentData) {
    const response = await this.api.post('/customer-payments', paymentData);
    return response.data;
  }

  async getAgedReceivables(tenantId) {
    const response = await this.api.get('/aged-receivables', { params: { tenant_id: tenantId } });
    return response.data;
  }

  // Financial Reports Methods

  async getCashFlowStatement(tenantId, startDate, endDate) {
    const response = await this.api.get('/reports/cash-flow', {
      params: { tenant_id: tenantId, start_date: startDate, end_date: endDate }
    });
    return response.data;
  }

  async exportReport(reportType, tenantId, dateRange, format = 'pdf') {
    const response = await this.api.get(`/reports/export/${reportType}`, {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate,
        format: format
      },
      responseType: 'blob'
    });
    return response;
  }

  // Tax Management Methods
  async getGSTDashboard(tenantId, dateRange) {
    const response = await this.api.get('/tax/gst-dashboard', {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      }
    });
    return response.data;
  }

  async getGSTReturns(tenantId, dateRange) {
    const response = await this.api.get('/tax/gst-returns', {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      }
    });
    return response.data;
  }

  async getTDSData(tenantId, dateRange) {
    const response = await this.api.get('/tax/tds-data', {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      }
    });
    return response.data;
  }

  async getTCSData(tenantId, dateRange) {
    const response = await this.api.get('/tax/tcs-data', {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      }
    });
    return response.data;
  }

  async generateTaxReport(reportType, tenantId, dateRange) {
    const response = await this.api.get(`/tax/reports/${reportType}`, {
      params: {
        tenant_id: tenantId,
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      },
      responseType: 'blob'
    });
    return response;
  }

  // Inventory Accounting Methods
  async getInventoryValuation(tenantId, costingMethod = 'FIFO') {
    const response = await this.api.get('/inventory/valuation', {
      params: { tenant_id: tenantId, costing_method: costingMethod }
    });
    return response.data;
  }

  async getInventoryMovements(tenantId) {
    const response = await this.api.get('/inventory/movements', {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  async getCOGSAnalysis(tenantId) {
    const response = await this.api.get('/inventory/cogs-analysis', {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  async getInventoryAdjustments(tenantId) {
    const response = await this.api.get('/inventory/adjustments', {
      params: { tenant_id: tenantId }
    });
    return response.data;
  }

  async exportInventoryReport(reportType, tenantId, costingMethod = 'FIFO') {
    const response = await this.api.get(`/inventory/export/${reportType}`, {
      params: {
        tenant_id: tenantId,
        costing_method: costingMethod
      },
      responseType: 'blob'
    });
    return response;
  }
}

export const accountingService = new AccountingService();
export default AccountingService;
