import React, { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import storeroomAPI from '../../services/storeroomAPI';
import { useAuth } from '../../context/AuthContext';

const StoreroomSelector = ({ 
  value, 
  onChange, 
  name = 'storeroom_id',
  label = 'Storeroom',
  required = false,
  disabled = false,
  size = 'md',
  className = '',
  placeholder = 'Select storeroom...',
  showAllOption = false,
  allOptionText = 'All Storerooms',
  tenantId = null // Filter by specific tenant if provided
}) => {
  const [storerooms, setStorerooms] = useState([]);
  const [groupedStorerooms, setGroupedStorerooms] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();

  useEffect(() => {
    loadStorerooms();
  }, [tenantId]);

  const loadStorerooms = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get storerooms grouped by tenant for better organization
      const response = await storeroomAPI.getStoreroomsByTenant();
      
      if (response.data.success) {
        const grouped = response.data.data;
        setGroupedStorerooms(grouped);
        
        // Flatten for simple array if needed
        const flatStorerooms = [];
        Object.values(grouped).forEach(tenant => {
          tenant.storerooms.forEach(storeroom => {
            flatStorerooms.push({
              ...storeroom,
              tenant_name: tenant.tenant_name,
              site_code: tenant.site_code,
              is_hub: tenant.is_hub,
              display_name: `${storeroom.name} (${tenant.site_code})`
            });
          });
        });
        
        // Filter by tenant if specified
        const filteredStorerooms = tenantId 
          ? flatStorerooms.filter(s => s.tenant_id === tenantId)
          : flatStorerooms;
        
        setStorerooms(filteredStorerooms);
      } else {
        setError(response.data.error || 'Failed to load storerooms');
      }
    } catch (err) {
      console.error('Error loading storerooms:', err);
      setError('Failed to load storerooms');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const selectedValue = e.target.value;
    if (onChange) {
      onChange(selectedValue);
    }
  };

  const renderGroupedOptions = () => {
    return Object.entries(groupedStorerooms).map(([tenantKey, tenant]) => {
      // Filter by tenant if specified
      if (tenantId && tenant.tenant_id !== tenantId) {
        return null;
      }
      
      return (
        <optgroup key={tenant.tenant_id} label={tenantKey}>
          {tenant.storerooms.map(storeroom => (
            <option key={storeroom.id} value={storeroom.id}>
              {storeroom.name}
              {storeroom.location_details && ` - ${storeroom.location_details}`}
            </option>
          ))}
        </optgroup>
      );
    });
  };

  const renderFlatOptions = () => {
    return storerooms.map(storeroom => (
      <option key={storeroom.id} value={storeroom.id}>
        {storeroom.display_name}
        {storeroom.location_details && ` - ${storeroom.location_details}`}
      </option>
    ));
  };

  if (error) {
    return (
      <Form.Group className={className}>
        {label && (
          <Form.Label>
            {label}
            {required && <span className="text-danger ms-1">*</span>}
          </Form.Label>
        )}
        <Form.Select disabled>
          <option>Error loading storerooms</option>
        </Form.Select>
        <Form.Text className="text-danger">{error}</Form.Text>
      </Form.Group>
    );
  }

  return (
    <Form.Group className={className}>
      {label && (
        <Form.Label>
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </Form.Label>
      )}
      <Form.Select
        name={name}
        value={value || ''}
        onChange={handleChange}
        required={required}
        disabled={disabled || loading}
        size={size}
      >
        <option value="">
          {loading ? 'Loading storerooms...' : placeholder}
        </option>
        
        {showAllOption && (
          <option value="all">{allOptionText}</option>
        )}
        
        {/* Show grouped options if we have multiple tenants, flat options otherwise */}
        {Object.keys(groupedStorerooms).length > 1 && !tenantId
          ? renderGroupedOptions()
          : renderFlatOptions()
        }
      </Form.Select>
      
      {/* Show access info for franchise users */}
      {currentUser?.role === 'franchise_admin' && (
        <Form.Text className="text-muted">
          You can only see storerooms from your franchise
        </Form.Text>
      )}
    </Form.Group>
  );
};

export default StoreroomSelector;
