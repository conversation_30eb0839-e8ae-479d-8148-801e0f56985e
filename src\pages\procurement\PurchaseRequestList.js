import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Button, Form, Badge, Alert, Modal } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faClipboardList,
  faPlus,
  faSearch,
  faFilter,
  faEye,
  faEdit,
  faPaperPlane,
  faCheck,
  faTimes,
  faExclamationTriangle,
  faFileInvoice
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import ResponsiveDataTable from '../../components/common/ResponsiveDataTable';
import procurementAPI from '../../services/procurementAPI';

const PurchaseRequestList = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    start_date: '',
    end_date: ''
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [approvalAction, setApprovalAction] = useState('approve');
  const [approvalNotes, setApprovalNotes] = useState('');

  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  const navigate = useNavigate();

  useEffect(() => {
    loadRequests();
  }, [filters, currentPage]);

  const loadRequests = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        page: currentPage,
        limit: 20
      };
      
      if (searchQuery) {
        params.search = searchQuery;
      }

      const response = await procurementAPI.getPurchaseRequests(params);
      setRequests(response.data.data || []);
      setTotalPages(response.data.total_pages || 1);
    } catch (err) {
      console.error('Error loading purchase requests:', err);
      setError('Failed to load purchase requests');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadRequests();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'submitted': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'fulfilled': return 'primary';
      default: return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority) => {
    switch (priority) {
      case 'urgent': return 'danger';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const handleSubmitRequest = async (requestId) => {
    try {
      await procurementAPI.submitPurchaseRequest(requestId);
      loadRequests();
    } catch (err) {
      console.error('Error submitting request:', err);
      setError('Failed to submit request');
    }
  };

  const handleApprovalAction = (request, action) => {
    setSelectedRequest(request);
    setApprovalAction(action);
    setApprovalNotes('');
    setShowApprovalModal(true);
  };

  const submitApprovalAction = async () => {
    try {
      if (approvalAction === 'approve') {
        await procurementAPI.approvePurchaseRequest(selectedRequest.id, {
          approval_notes: approvalNotes
        });
      } else {
        await procurementAPI.rejectPurchaseRequest(selectedRequest.id, {
          rejection_reason: approvalNotes
        });
      }
      
      setShowApprovalModal(false);
      loadRequests();
    } catch (err) {
      console.error('Error processing approval:', err);
      setError(`Failed to ${approvalAction} request`);
    }
  };

  const handleGeneratePO = async (requestId) => {
    try {
      setLoading(true);
      const response = await procurementAPI.generatePOFromRequest(requestId, {
        supplier_id: null, // Will be selected in PO management
        notes: 'Generated from approved purchase request'
      });

      if (response.success) {
        setError(null);
        // Show success message and redirect to PO management
        alert(`Purchase Order ${response.po_number} generated successfully!`);
        navigate('/procurement/purchase-orders');
      } else {
        setError(response.error || 'Failed to generate purchase order');
      }
    } catch (err) {
      console.error('Error generating PO:', err);
      setError('Failed to generate purchase order');
    } finally {
      setLoading(false);
    }
  };

  const canEdit = (request) => {
    return request.status === 'draft' && 
           (currentUser?.role === 'admin' || 
            currentUser?.role === 'hub_admin' || 
            request.requesting_tenant_id === currentUser?.tenant_id);
  };

  const canSubmit = (request) => {
    return request.status === 'draft' && 
           (currentUser?.role === 'admin' || 
            currentUser?.role === 'hub_admin' || 
            request.requesting_tenant_id === currentUser?.tenant_id);
  };

  const canApprove = (request) => {
    return request.status === 'submitted' &&
           (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin');
  };

  const canGeneratePO = (request) => {
    return request.status === 'approved' &&
           (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin');
  };

  // Table columns
  const columns = [
    {
      key: 'request_number',
      label: 'Request #',
      render: (value, row) => (
        <Link to={`/procurement/requests/${row.id}`} className="text-decoration-none fw-semibold">
          {value}
        </Link>
      )
    },
    {
      key: 'request_date',
      label: 'Date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'priority',
      label: 'Priority',
      render: (value) => (
        <Badge bg={getPriorityBadgeVariant(value)} className="text-capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge bg={getStatusBadgeVariant(value)} className="text-capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'items',
      label: 'Items',
      render: (value) => `${value?.length || 0} items`
    },
    {
      key: 'total_estimated_amount',
      label: 'Est. Amount',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'required_date',
      label: 'Required By',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  // Mobile card configuration
  const mobileCardConfig = {
    title: (request) => request.request_number,
    subtitle: (request) => `${request.items?.length || 0} items • ${request.priority} priority`,
    primaryField: 'request_date',
    secondaryField: 'status',
    statusField: 'status'
  };

  const getRowActions = (request) => {
    const actions = [];

    actions.push({
      label: 'View',
      icon: faEye,
      variant: 'outline-primary',
      onClick: () => navigate(`/procurement/requests/${request.id}`)
    });

    if (canEdit(request)) {
      actions.push({
        label: 'Edit',
        icon: faEdit,
        variant: 'outline-secondary',
        onClick: () => navigate(`/procurement/requests/${request.id}/edit`)
      });
    }

    if (canSubmit(request)) {
      actions.push({
        label: 'Submit',
        icon: faPaperPlane,
        variant: 'outline-success',
        onClick: () => handleSubmitRequest(request.id)
      });
    }

    if (canApprove(request)) {
      actions.push({
        label: 'Approve',
        icon: faCheck,
        variant: 'outline-success',
        onClick: () => handleApprovalAction(request, 'approve')
      });
      actions.push({
        label: 'Reject',
        icon: faTimes,
        variant: 'outline-danger',
        onClick: () => handleApprovalAction(request, 'reject')
      });
    }

    if (canGeneratePO(request)) {
      actions.push({
        label: 'Generate PO',
        icon: faFileInvoice,
        variant: 'outline-primary',
        onClick: () => handleGeneratePO(request.id)
      });
    }

    return actions;
  };

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faClipboardList} className="me-2 text-primary" />
            Purchase Requests
          </h2>
          <p className="text-muted mb-0">Manage purchase requests and approvals</p>
        </div>
        {hasModuleAccess('PURCHASE_REQUESTS') && (
          <Link to="/procurement/requests/create" className="btn btn-primary">
            <FontAwesomeIcon icon={faPlus} className="me-2" />
            New Request
          </Link>
        )}
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header className='text-primary'>
          <FontAwesomeIcon icon={faFilter} className="me-2" />
          Filters
        </Card.Header>
        <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
          <Form onSubmit={handleSearch}>
            <Row>
              <Col md={3} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="submitted">Submitted</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="fulfilled">Fulfilled</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Priority</Form.Label>
                <Form.Select
                  value={filters.priority}
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                >
                  <option value="">All Priorities</option>
                  <option value="urgent">Urgent</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => handleFilterChange('start_date', e.target.value)}
                />
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => handleFilterChange('end_date', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6} className="mb-3">
                <Form.Label>Search</Form.Label>
                <div className="input-group">
                  <Form.Control
                    type="text"
                    placeholder="Search by request number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" variant="outline-secondary">
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Data Table */}
      <Card>
        <Card.Body className="p-0">
          <ResponsiveDataTable
            data={requests}
            columns={columns}
            loading={loading}
            emptyMessage="No purchase requests found."
            mobileCardConfig={mobileCardConfig}
            getRowActions={getRowActions}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </Card.Body>
      </Card>

      {/* Approval Modal */}
      <Modal show={showApprovalModal} onHide={() => setShowApprovalModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {approvalAction === 'approve' ? 'Approve' : 'Reject'} Purchase Request
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Are you sure you want to {approvalAction} purchase request{' '}
            <strong>{selectedRequest?.request_number}</strong>?
          </p>
          <Form.Group>
            <Form.Label>
              {approvalAction === 'approve' ? 'Approval Notes' : 'Rejection Reason'}
            </Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={approvalNotes}
              onChange={(e) => setApprovalNotes(e.target.value)}
              placeholder={`Enter ${approvalAction === 'approve' ? 'approval notes' : 'rejection reason'}...`}
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowApprovalModal(false)}>
            Cancel
          </Button>
          <Button
            variant={approvalAction === 'approve' ? 'success' : 'danger'}
            onClick={submitApprovalAction}
          >
            {approvalAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PurchaseRequestList;
