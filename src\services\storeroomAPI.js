import api from './api';

const storeroomAPI = {
  // Get all storerooms
  getStorerooms: (params = {}) => api.get('/storerooms', { params }),

  // Get storerooms grouped by tenant for dropdown selection
  getStoreroomsByTenant: () => api.get('/storerooms/by-tenant'),

  // Get specific storeroom
  getStoreroom: (id) => api.get(`/storerooms/${id}`),

  // Create new storeroom
  createStoreroom: (data) => api.post('/storerooms', data),

  // Update storeroom
  updateStoreroom: (id, data) => api.put(`/storerooms/${id}`, data),

  // Delete storeroom
  deleteStoreroom: (id) => api.delete(`/storerooms/${id}`)
};

export default storeroomAPI;
