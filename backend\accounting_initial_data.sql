-- AVINI Labs Accounting Module Initial Data
-- Standard Chart of Accounts and Configuration Data

-- ============================================================================
-- CORE DATA SETUP
-- Note: Users, departments, and modules are stored in JSON files
-- This section only sets up accounting-specific data
-- ============================================================================

-- ============================================================================
-- STANDARD CHART OF ACCOUNTS
-- ============================================================================

-- Assets
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
-- Current Assets
('1000', 'CURRENT ASSETS', 'ASSET', 'CURRENT_ASSET', 1, TRUE, 1, 1),
('1100', 'Cash and Cash Equivalents', 'ASSET', 'CURRENT_ASSET', 2, TRUE, 1, 1),
('1110', 'Cash in Hand', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1120', 'Bank Accounts', 'ASSET', 'CURRENT_ASSET', 3, TRUE, 1, 1),
('1121', 'SBI Current Account', 'ASSET', 'CURRENT_ASSET', 4, FALSE, 1, 1),
('1122', 'HDFC Savings Account', 'ASSET', 'CURRENT_ASSET', 4, FALSE, 1, 1),
('1200', 'Accounts Receivable', 'ASSET', 'CURRENT_ASSET', 2, TRUE, 1, 1),
('1210', 'Trade Receivables', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1220', 'Other Receivables', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1300', 'Inventory', 'ASSET', 'CURRENT_ASSET', 2, TRUE, 1, 1),
('1310', 'Laboratory Supplies', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1320', 'Reagents and Chemicals', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1330', 'Medical Equipment Supplies', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1400', 'Prepaid Expenses', 'ASSET', 'CURRENT_ASSET', 2, TRUE, 1, 1),
('1410', 'Prepaid Insurance', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),
('1420', 'Prepaid Rent', 'ASSET', 'CURRENT_ASSET', 3, FALSE, 1, 1),

-- Fixed Assets
('1500', 'FIXED ASSETS', 'ASSET', 'FIXED_ASSET', 1, TRUE, 1, 1),
('1510', 'Laboratory Equipment', 'ASSET', 'FIXED_ASSET', 2, FALSE, 1, 1),
('1520', 'Computer Equipment', 'ASSET', 'FIXED_ASSET', 2, FALSE, 1, 1),
('1530', 'Furniture and Fixtures', 'ASSET', 'FIXED_ASSET', 2, FALSE, 1, 1),
('1540', 'Vehicles', 'ASSET', 'FIXED_ASSET', 2, FALSE, 1, 1),
('1550', 'Building and Improvements', 'ASSET', 'FIXED_ASSET', 2, FALSE, 1, 1),
('1560', 'Accumulated Depreciation', 'ASSET', 'FIXED_ASSET', 2, TRUE, 1, 1),
('1561', 'Accumulated Depreciation - Equipment', 'ASSET', 'FIXED_ASSET', 3, FALSE, 1, 1),
('1562', 'Accumulated Depreciation - Computers', 'ASSET', 'FIXED_ASSET', 3, FALSE, 1, 1);

-- Liabilities
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
-- Current Liabilities
('2000', 'CURRENT LIABILITIES', 'LIABILITY', 'CURRENT_LIABILITY', 1, TRUE, 1, 1),
('2100', 'Accounts Payable', 'LIABILITY', 'CURRENT_LIABILITY', 2, TRUE, 1, 1),
('2110', 'Trade Payables', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2120', 'Other Payables', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2200', 'Accrued Expenses', 'LIABILITY', 'CURRENT_LIABILITY', 2, TRUE, 1, 1),
('2210', 'Accrued Salaries', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2220', 'Accrued Utilities', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2300', 'Tax Liabilities', 'LIABILITY', 'CURRENT_LIABILITY', 2, TRUE, 1, 1),
('2310', 'GST Payable', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2320', 'TDS Payable', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),
('2330', 'Professional Tax Payable', 'LIABILITY', 'CURRENT_LIABILITY', 3, FALSE, 1, 1),

-- Long Term Liabilities
('2400', 'LONG TERM LIABILITIES', 'LIABILITY', 'LONG_TERM_LIABILITY', 1, TRUE, 1, 1),
('2410', 'Bank Loans', 'LIABILITY', 'LONG_TERM_LIABILITY', 2, FALSE, 1, 1),
('2420', 'Equipment Loans', 'LIABILITY', 'LONG_TERM_LIABILITY', 2, FALSE, 1, 1);

-- Equity
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
('3000', 'EQUITY', 'EQUITY', 'OWNER_EQUITY', 1, TRUE, 1, 1),
('3100', 'Owner''s Capital', 'EQUITY', 'OWNER_EQUITY', 2, FALSE, 1, 1),
('3200', 'Retained Earnings', 'EQUITY', 'RETAINED_EARNINGS', 2, FALSE, 1, 1),
('3300', 'Current Year Earnings', 'EQUITY', 'RETAINED_EARNINGS', 2, FALSE, 1, 1);

-- Revenue
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
('4000', 'REVENUE', 'REVENUE', 'OPERATING_REVENUE', 1, TRUE, 1, 1),
('4100', 'Laboratory Services Revenue', 'REVENUE', 'OPERATING_REVENUE', 2, TRUE, 1, 1),
('4110', 'Pathology Services', 'REVENUE', 'OPERATING_REVENUE', 3, FALSE, 1, 1),
('4120', 'Radiology Services', 'REVENUE', 'OPERATING_REVENUE', 3, FALSE, 1, 1),
('4130', 'Consultation Services', 'REVENUE', 'OPERATING_REVENUE', 3, FALSE, 1, 1),
('4200', 'Franchise Revenue', 'REVENUE', 'OPERATING_REVENUE', 2, TRUE, 1, 1),
('4210', 'Franchise Fees', 'REVENUE', 'OPERATING_REVENUE', 3, FALSE, 1, 1),
('4220', 'Commission Income', 'REVENUE', 'OPERATING_REVENUE', 3, FALSE, 1, 1),
('4300', 'Other Revenue', 'REVENUE', 'OTHER_REVENUE', 2, TRUE, 1, 1),
('4310', 'Interest Income', 'REVENUE', 'OTHER_REVENUE', 3, FALSE, 1, 1),
('4320', 'Miscellaneous Income', 'REVENUE', 'OTHER_REVENUE', 3, FALSE, 1, 1);

-- Expenses
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
('5000', 'OPERATING EXPENSES', 'EXPENSE', 'OPERATING_EXPENSE', 1, TRUE, 1, 1),
('5100', 'Personnel Expenses', 'EXPENSE', 'OPERATING_EXPENSE', 2, TRUE, 1, 1),
('5110', 'Salaries and Wages', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5120', 'Employee Benefits', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5130', 'Professional Development', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5200', 'Laboratory Expenses', 'EXPENSE', 'OPERATING_EXPENSE', 2, TRUE, 1, 1),
('5210', 'Reagents and Supplies', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5220', 'Equipment Maintenance', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5230', 'Quality Control', 'EXPENSE', 'OPERATING_EXPENSE', 3, FALSE, 1, 1),
('5300', 'Administrative Expenses', 'EXPENSE', 'ADMINISTRATIVE_EXPENSE', 2, TRUE, 1, 1),
('5310', 'Rent and Utilities', 'EXPENSE', 'ADMINISTRATIVE_EXPENSE', 3, FALSE, 1, 1),
('5320', 'Insurance', 'EXPENSE', 'ADMINISTRATIVE_EXPENSE', 3, FALSE, 1, 1),
('5330', 'Professional Services', 'EXPENSE', 'ADMINISTRATIVE_EXPENSE', 3, FALSE, 1, 1),
('5340', 'Marketing and Advertising', 'EXPENSE', 'ADMINISTRATIVE_EXPENSE', 3, FALSE, 1, 1),
('5400', 'Financial Expenses', 'EXPENSE', 'FINANCIAL_EXPENSE', 2, TRUE, 1, 1),
('5410', 'Interest Expense', 'EXPENSE', 'FINANCIAL_EXPENSE', 3, FALSE, 1, 1),
('5420', 'Bank Charges', 'EXPENSE', 'FINANCIAL_EXPENSE', 3, FALSE, 1, 1);

-- Cost of Goods Sold
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_subtype, account_level, is_control_account, tenant_id, created_by) VALUES
('6000', 'COST OF GOODS SOLD', 'COST_OF_GOODS_SOLD', 'DIRECT_COST', 1, TRUE, 1, 1),
('6100', 'Direct Laboratory Costs', 'COST_OF_GOODS_SOLD', 'DIRECT_COST', 2, TRUE, 1, 1),
('6110', 'Test Kit Costs', 'COST_OF_GOODS_SOLD', 'DIRECT_COST', 3, FALSE, 1, 1),
('6120', 'Sample Collection Costs', 'COST_OF_GOODS_SOLD', 'DIRECT_COST', 3, FALSE, 1, 1),
('6200', 'Indirect Laboratory Costs', 'COST_OF_GOODS_SOLD', 'INDIRECT_COST', 2, TRUE, 1, 1),
('6210', 'Equipment Depreciation', 'COST_OF_GOODS_SOLD', 'INDIRECT_COST', 3, FALSE, 1, 1),
('6220', 'Laboratory Overhead', 'COST_OF_GOODS_SOLD', 'INDIRECT_COST', 3, FALSE, 1, 1);

-- ============================================================================
-- ACCOUNT GROUPS
-- ============================================================================

INSERT INTO account_groups (group_code, group_name, group_type, tenant_id) VALUES
('BS_ASSETS', 'Balance Sheet - Assets', 'BALANCE_SHEET', 1),
('BS_LIABILITIES', 'Balance Sheet - Liabilities', 'BALANCE_SHEET', 1),
('BS_EQUITY', 'Balance Sheet - Equity', 'BALANCE_SHEET', 1),
('PL_REVENUE', 'Profit & Loss - Revenue', 'PROFIT_LOSS', 1),
('PL_EXPENSES', 'Profit & Loss - Expenses', 'PROFIT_LOSS', 1),
('PL_COGS', 'Profit & Loss - Cost of Goods Sold', 'PROFIT_LOSS', 1),
('TRADING', 'Trading Account', 'TRADING', 1);

-- ============================================================================
-- TAX TYPES
-- ============================================================================

INSERT INTO tax_types (tax_code, tax_name, tax_type, tax_rate, effective_from, tenant_id, created_by) VALUES
('GST_5', 'GST 5%', 'GST', 5.00, '2024-01-01', 1, 1),
('GST_12', 'GST 12%', 'GST', 12.00, '2024-01-01', 1, 1),
('GST_18', 'GST 18%', 'GST', 18.00, '2024-01-01', 1, 1),
('GST_28', 'GST 28%', 'GST', 28.00, '2024-01-01', 1, 1),
('TDS_10', 'TDS 10%', 'TDS', 10.00, '2024-01-01', 1, 1),
('TCS_1', 'TCS 1%', 'TCS', 1.00, '2024-01-01', 1, 1);

-- ============================================================================
-- INVENTORY VALUATION METHODS
-- ============================================================================

INSERT INTO inventory_valuation_methods (method_code, method_name, method_type, description, tenant_id) VALUES
('FIFO', 'First In First Out', 'FIFO', 'First items purchased are first items used', 1),
('LIFO', 'Last In First Out', 'LIFO', 'Last items purchased are first items used', 1),
('AVG', 'Weighted Average', 'AVERAGE', 'Average cost of all items in inventory', 1),
('STD', 'Standard Cost', 'STANDARD', 'Predetermined standard cost', 1);

-- ============================================================================
-- COST CENTERS
-- ============================================================================

INSERT INTO cost_centers (cost_center_code, cost_center_name, description, tenant_id, created_by) VALUES
('LAB001', 'Pathology Department', 'Main pathology laboratory', 1, 1),
('LAB002', 'Radiology Department', 'Radiology and imaging services', 1, 1),
('ADM001', 'Administration', 'Administrative functions', 1, 1),
('MKT001', 'Marketing', 'Marketing and business development', 1, 1),
('FIN001', 'Finance', 'Finance and accounting', 1, 1);

-- ============================================================================
-- FINANCIAL PERIODS
-- ============================================================================

INSERT INTO financial_periods (period_code, period_name, period_type, start_date, end_date, fiscal_year, is_current, tenant_id, created_by) VALUES
('FY2024-25', 'Financial Year 2024-25', 'YEARLY', '2024-04-01', '2025-03-31', 2024, TRUE, 1, 1),
('Q1-2024-25', 'Q1 2024-25', 'QUARTERLY', '2024-04-01', '2024-06-30', 2024, FALSE, 1, 1),
('Q2-2024-25', 'Q2 2024-25', 'QUARTERLY', '2024-07-01', '2024-09-30', 2024, FALSE, 1, 1),
('Q3-2024-25', 'Q3 2024-25', 'QUARTERLY', '2024-10-01', '2024-12-31', 2024, FALSE, 1, 1),
('Q4-2024-25', 'Q4 2024-25', 'QUARTERLY', '2025-01-01', '2025-03-31', 2024, FALSE, 1, 1);
