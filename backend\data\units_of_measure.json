[{"id": 1, "code": "each", "name": "Each", "description": "Individual units", "category": "count", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 2, "code": "pcs", "name": "Pieces", "description": "Individual pieces", "category": "count", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 3, "code": "kg", "name": "Kilogram", "description": "Weight in kilograms", "category": "weight", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 4, "code": "g", "name": "Gram", "description": "Weight in grams", "category": "weight", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 5, "code": "liter", "name": "Liter", "description": "Volume in liters", "category": "volume", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 6, "code": "ml", "name": "Milliliter", "description": "Volume in milliliters", "category": "volume", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 7, "code": "meter", "name": "<PERSON>er", "description": "Length in meters", "category": "length", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 8, "code": "cm", "name": "Centimeter", "description": "Length in centimeters", "category": "length", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 9, "code": "box", "name": "Box", "description": "Packaged in boxes", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 10, "code": "carton", "name": "<PERSON><PERSON>", "description": "Packaged in cartons", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 11, "code": "roll", "name": "Roll", "description": "Rolled items", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 12, "code": "pack", "name": "Pack", "description": "Packaged items", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 13, "code": "bottle", "name": "<PERSON><PERSON>", "description": "Bottled items", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 14, "code": "tube", "name": "<PERSON><PERSON>", "description": "Tubed items", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 15, "code": "vial", "name": "Vial", "description": "Small containers/vials", "category": "packaging", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 16, "code": "set", "name": "Set", "description": "Set of items", "category": "count", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 17, "code": "pair", "name": "Pair", "description": "Pair of items", "category": "count", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}, {"id": 18, "code": "dozen", "name": "<PERSON><PERSON>", "description": "12 items", "category": "count", "is_active": true, "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}]