-- AVINI Labs Accounting Module Database Schema
-- Comprehensive accounting system with SAP and Tally-style functionality
-- Integrates with existing procurement and invoice management systems

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- NOTE: Core tables (users, tenants, departments, modules) are stored in JSON files
-- Foreign key references to these tables will be handled at application level
-- ============================================================================

-- ============================================================================
-- CHART OF ACCOUNTS AND ACCOUNT MANAGEMENT
-- ============================================================================

-- 1. Chart of Accounts
CREATE TABLE IF NOT EXISTS chart_of_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_code TEXT UNIQUE NOT NULL,
    account_name TEXT NOT NULL,
    account_type TEXT NOT NULL CHECK (account_type IN (
        'ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE', 'COST_OF_GOODS_SOLD'
    )),
    account_subtype TEXT NOT NULL CHECK (account_subtype IN (
        -- Assets
        'CURRENT_ASSET', 'FIXED_ASSET', 'INTANGIBLE_ASSET', 'INVESTMENT',
        -- Liabilities  
        'CURRENT_LIABILITY', 'LONG_TERM_LIABILITY',
        -- Equity
        'OWNER_EQUITY', 'RETAINED_EARNINGS',
        -- Revenue
        'OPERATING_REVENUE', 'OTHER_REVENUE',
        -- Expenses
        'OPERATING_EXPENSE', 'ADMINISTRATIVE_EXPENSE', 'FINANCIAL_EXPENSE',
        -- Cost of Goods Sold
        'DIRECT_COST', 'INDIRECT_COST'
    )),
    parent_account_id INTEGER,
    account_level INTEGER DEFAULT 1,
    is_control_account BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    opening_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    debit_balance DECIMAL(15,2) DEFAULT 0,
    credit_balance DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
    -- Note: tenant_id and created_by reference JSON-stored data
);

-- 2. Account Groups (for organizing accounts)
CREATE TABLE IF NOT EXISTS account_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_code TEXT UNIQUE NOT NULL,
    group_name TEXT NOT NULL,
    group_type TEXT NOT NULL CHECK (group_type IN (
        'BALANCE_SHEET', 'PROFIT_LOSS', 'TRADING'
    )),
    parent_group_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_group_id) REFERENCES account_groups(id)
    -- Note: tenant_id references JSON-stored data
);

-- 3. Account Group Mappings
CREATE TABLE IF NOT EXISTS account_group_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    group_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES account_groups(id) ON DELETE CASCADE,
    UNIQUE(account_id, group_id)
);

-- ============================================================================
-- GENERAL LEDGER AND JOURNAL ENTRIES
-- ============================================================================

-- 4. Journal Entry Headers
CREATE TABLE IF NOT EXISTS journal_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    journal_number TEXT UNIQUE NOT NULL,
    journal_type TEXT NOT NULL CHECK (journal_type IN (
        'GENERAL', 'SALES', 'PURCHASE', 'CASH_RECEIPT', 'CASH_PAYMENT', 
        'BANK_RECEIPT', 'BANK_PAYMENT', 'ADJUSTMENT', 'OPENING_BALANCE',
        'CLOSING_BALANCE', 'DEPRECIATION', 'ACCRUAL', 'REVERSAL'
    )),
    transaction_date DATE NOT NULL,
    posting_date DATE NOT NULL,
    reference_type TEXT CHECK (reference_type IN (
        'INVOICE', 'PURCHASE_ORDER', 'PAYMENT', 'RECEIPT', 'ADJUSTMENT', 
        'MANUAL', 'SYSTEM_GENERATED', 'PROCUREMENT', 'BILLING'
    )),
    reference_id INTEGER,
    reference_number TEXT,
    description TEXT NOT NULL,
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
    status TEXT NOT NULL CHECK (status IN ('DRAFT', 'POSTED', 'REVERSED')) DEFAULT 'DRAFT',
    is_auto_generated BOOLEAN DEFAULT FALSE,
    reversal_entry_id INTEGER,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    posted_by INTEGER,
    posted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reversal_entry_id) REFERENCES journal_entries(id)
    -- Note: tenant_id, created_by, posted_by reference JSON-stored data
);

-- 5. Journal Entry Line Items
CREATE TABLE IF NOT EXISTS journal_entry_lines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    journal_entry_id INTEGER NOT NULL,
    line_number INTEGER NOT NULL,
    account_id INTEGER NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    cost_center_id INTEGER,
    project_id INTEGER,
    department_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (cost_center_id) REFERENCES cost_centers(id),
    FOREIGN KEY (project_id) REFERENCES projects(id)
    -- Note: department_id references JSON-stored data
);

-- ============================================================================
-- ACCOUNTS PAYABLE
-- ============================================================================

-- 6. Vendors (Enhanced from existing suppliers)
CREATE TABLE IF NOT EXISTS vendors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vendor_code TEXT UNIQUE NOT NULL,
    vendor_name TEXT NOT NULL,
    vendor_type TEXT CHECK (vendor_type IN ('SUPPLIER', 'SERVICE_PROVIDER', 'CONTRACTOR', 'OTHER')) DEFAULT 'SUPPLIER',
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    mobile TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city TEXT,
    state TEXT,
    country TEXT DEFAULT 'India',
    pincode TEXT,
    pan_number TEXT,
    gst_number TEXT,
    tax_id TEXT,
    payment_terms TEXT CHECK (payment_terms IN ('IMMEDIATE', 'NET_15', 'NET_30', 'NET_45', 'NET_60', 'CUSTOM')) DEFAULT 'NET_30',
    credit_limit DECIMAL(15,2) DEFAULT 0,
    credit_days INTEGER DEFAULT 30,
    bank_name TEXT,
    bank_account_number TEXT,
    bank_ifsc_code TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id and created_by reference JSON-stored data
);

-- 7. Purchase Invoices (AP)
CREATE TABLE IF NOT EXISTS purchase_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT NOT NULL,
    vendor_invoice_number TEXT,
    vendor_id INTEGER NOT NULL,
    purchase_order_id INTEGER,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    payment_terms TEXT,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    outstanding_amount DECIMAL(15,2) NOT NULL,
    status TEXT CHECK (status IN ('DRAFT', 'PENDING', 'APPROVED', 'PAID', 'PARTIALLY_PAID', 'OVERDUE', 'CANCELLED')) DEFAULT 'DRAFT',
    currency TEXT DEFAULT 'INR',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    notes TEXT,
    journal_entry_id INTEGER,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    approved_by INTEGER,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
    -- Note: tenant_id, created_by, approved_by reference JSON-stored data
);

-- 8. Purchase Invoice Line Items
CREATE TABLE IF NOT EXISTS purchase_invoice_lines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_invoice_id INTEGER NOT NULL,
    line_number INTEGER NOT NULL,
    item_description TEXT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    line_total DECIMAL(15,2) NOT NULL,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    inventory_item_id INTEGER,
    account_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_invoice_id) REFERENCES purchase_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id),
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- 9. Vendor Payments
CREATE TABLE IF NOT EXISTS vendor_payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_number TEXT UNIQUE NOT NULL,
    vendor_id INTEGER NOT NULL,
    payment_date DATE NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('CASH', 'CHEQUE', 'BANK_TRANSFER', 'UPI', 'CARD', 'OTHER')) NOT NULL,
    reference_number TEXT,
    total_amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'INR',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    bank_account_id INTEGER,
    notes TEXT,
    status TEXT CHECK (status IN ('DRAFT', 'PROCESSED', 'CLEARED', 'CANCELLED')) DEFAULT 'DRAFT',
    journal_entry_id INTEGER,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    approved_by INTEGER,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
    -- Note: tenant_id, created_by, approved_by reference JSON-stored data
);

-- 10. Vendor Payment Allocations
CREATE TABLE IF NOT EXISTS vendor_payment_allocations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_id INTEGER NOT NULL,
    purchase_invoice_id INTEGER NOT NULL,
    allocated_amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES vendor_payments(id) ON DELETE CASCADE,
    FOREIGN KEY (purchase_invoice_id) REFERENCES purchase_invoices(id)
);

-- ============================================================================
-- ACCOUNTS RECEIVABLE
-- ============================================================================

-- 11. Customers (Enhanced from existing patients)
CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_code TEXT UNIQUE NOT NULL,
    customer_name TEXT NOT NULL,
    customer_type TEXT CHECK (customer_type IN ('INDIVIDUAL', 'CORPORATE', 'GOVERNMENT', 'FRANCHISE', 'OTHER')) DEFAULT 'INDIVIDUAL',
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    mobile TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city TEXT,
    state TEXT,
    country TEXT DEFAULT 'India',
    pincode TEXT,
    pan_number TEXT,
    gst_number TEXT,
    payment_terms TEXT CHECK (payment_terms IN ('IMMEDIATE', 'NET_15', 'NET_30', 'NET_45', 'NET_60', 'CUSTOM')) DEFAULT 'IMMEDIATE',
    credit_limit DECIMAL(15,2) DEFAULT 0,
    credit_days INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    patient_id INTEGER, -- Link to existing patient records in JSON
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id, created_by, patient_id reference JSON-stored data
);

-- 12. Sales Invoices (AR) - Links to existing billing system
CREATE TABLE IF NOT EXISTS sales_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT UNIQUE NOT NULL,
    customer_id INTEGER NOT NULL,
    billing_id INTEGER, -- Link to existing billing records
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    payment_terms TEXT,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    outstanding_amount DECIMAL(15,2) NOT NULL,
    status TEXT CHECK (status IN ('DRAFT', 'SENT', 'PAID', 'PARTIALLY_PAID', 'OVERDUE', 'CANCELLED')) DEFAULT 'DRAFT',
    currency TEXT DEFAULT 'INR',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    notes TEXT,
    journal_entry_id INTEGER,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
    -- Note: tenant_id, created_by, billing_id reference JSON-stored data
);

-- 13. Sales Invoice Line Items
CREATE TABLE IF NOT EXISTS sales_invoice_lines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sales_invoice_id INTEGER NOT NULL,
    line_number INTEGER NOT NULL,
    item_description TEXT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    line_total DECIMAL(15,2) NOT NULL,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    test_id INTEGER, -- Link to test master
    account_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sales_invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- 14. Customer Payments
CREATE TABLE IF NOT EXISTS customer_payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_number TEXT UNIQUE NOT NULL,
    customer_id INTEGER NOT NULL,
    payment_date DATE NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('CASH', 'CHEQUE', 'BANK_TRANSFER', 'UPI', 'CARD', 'OTHER')) NOT NULL,
    reference_number TEXT,
    total_amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'INR',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    bank_account_id INTEGER,
    notes TEXT,
    status TEXT CHECK (status IN ('DRAFT', 'PROCESSED', 'CLEARED', 'CANCELLED')) DEFAULT 'DRAFT',
    journal_entry_id INTEGER,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
    -- Note: tenant_id, created_by reference JSON-stored data
);

-- 15. Customer Payment Allocations
CREATE TABLE IF NOT EXISTS customer_payment_allocations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_id INTEGER NOT NULL,
    sales_invoice_id INTEGER NOT NULL,
    allocated_amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES customer_payments(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_invoice_id) REFERENCES sales_invoices(id)
);

-- ============================================================================
-- FINANCIAL MANAGEMENT
-- ============================================================================

-- 16. Bank Accounts
CREATE TABLE IF NOT EXISTS bank_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_code TEXT UNIQUE NOT NULL,
    account_name TEXT NOT NULL,
    bank_name TEXT NOT NULL,
    account_number TEXT NOT NULL,
    account_type TEXT CHECK (account_type IN ('SAVINGS', 'CURRENT', 'FIXED_DEPOSIT', 'OVERDRAFT', 'LOAN')) DEFAULT 'CURRENT',
    ifsc_code TEXT,
    branch_name TEXT,
    opening_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    currency TEXT DEFAULT 'INR',
    is_active BOOLEAN DEFAULT TRUE,
    chart_account_id INTEGER, -- Link to chart of accounts
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chart_account_id) REFERENCES chart_of_accounts(id)
    -- Note: tenant_id, created_by reference JSON-stored data
);

-- 17. Cost Centers
CREATE TABLE IF NOT EXISTS cost_centers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cost_center_code TEXT UNIQUE NOT NULL,
    cost_center_name TEXT NOT NULL,
    description TEXT,
    parent_cost_center_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_cost_center_id) REFERENCES cost_centers(id)
    -- Note: tenant_id, created_by reference JSON-stored data
);

-- 18. Projects
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_code TEXT UNIQUE NOT NULL,
    project_name TEXT NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    status TEXT CHECK (status IN ('ACTIVE', 'COMPLETED', 'ON_HOLD', 'CANCELLED')) DEFAULT 'ACTIVE',
    budget DECIMAL(15,2) DEFAULT 0,
    actual_cost DECIMAL(15,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id, created_by reference JSON-stored data
);

-- ============================================================================
-- TAX MANAGEMENT
-- ============================================================================

-- 19. Tax Types
CREATE TABLE IF NOT EXISTS tax_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tax_code TEXT UNIQUE NOT NULL,
    tax_name TEXT NOT NULL,
    tax_type TEXT CHECK (tax_type IN ('GST', 'VAT', 'SERVICE_TAX', 'EXCISE', 'CUSTOMS', 'TDS', 'TCS', 'OTHER')) NOT NULL,
    tax_rate DECIMAL(5,2) NOT NULL,
    is_compound BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    description TEXT,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id, created_by reference JSON-stored data
);

-- 20. Tax Accounts Mapping
CREATE TABLE IF NOT EXISTS tax_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tax_type_id INTEGER NOT NULL,
    tax_payable_account_id INTEGER NOT NULL,
    tax_receivable_account_id INTEGER,
    tax_expense_account_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tax_type_id) REFERENCES tax_types(id) ON DELETE CASCADE,
    FOREIGN KEY (tax_payable_account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (tax_receivable_account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (tax_expense_account_id) REFERENCES chart_of_accounts(id)
);

-- ============================================================================
-- INVENTORY ACCOUNTING
-- ============================================================================

-- 21. Inventory Valuation Methods
CREATE TABLE IF NOT EXISTS inventory_valuation_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    method_code TEXT UNIQUE NOT NULL,
    method_name TEXT NOT NULL,
    method_type TEXT CHECK (method_type IN ('FIFO', 'LIFO', 'AVERAGE', 'STANDARD', 'SPECIFIC_IDENTIFICATION')) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id references JSON-stored data
);

-- 22. Inventory Accounting Entries
CREATE TABLE IF NOT EXISTS inventory_accounting_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_transaction_id INTEGER NOT NULL,
    transaction_type TEXT CHECK (transaction_type IN ('PURCHASE', 'SALE', 'ADJUSTMENT', 'TRANSFER', 'WRITE_OFF')) NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(12,2) NOT NULL,
    total_cost DECIMAL(15,2) NOT NULL,
    valuation_method_id INTEGER NOT NULL,
    journal_entry_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_transaction_id) REFERENCES inventory_transactions(id),
    FOREIGN KEY (valuation_method_id) REFERENCES inventory_valuation_methods(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
);

-- ============================================================================
-- FINANCIAL REPORTING AND PERIODS
-- ============================================================================

-- 23. Financial Periods
CREATE TABLE IF NOT EXISTS financial_periods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period_code TEXT UNIQUE NOT NULL,
    period_name TEXT NOT NULL,
    period_type TEXT CHECK (period_type IN ('MONTHLY', 'QUARTERLY', 'YEARLY')) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    fiscal_year INTEGER NOT NULL,
    status TEXT CHECK (status IN ('OPEN', 'CLOSED', 'LOCKED')) DEFAULT 'OPEN',
    is_current BOOLEAN DEFAULT FALSE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    closed_by INTEGER,
    closed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: tenant_id, created_by, closed_by reference JSON-stored data
);

-- 24. Financial Reports
CREATE TABLE IF NOT EXISTS financial_reports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    report_code TEXT UNIQUE NOT NULL,
    report_name TEXT NOT NULL,
    report_type TEXT CHECK (report_type IN (
        'TRIAL_BALANCE', 'PROFIT_LOSS', 'BALANCE_SHEET', 'CASH_FLOW',
        'AGED_RECEIVABLES', 'AGED_PAYABLES', 'GENERAL_LEDGER', 'CUSTOM'
    )) NOT NULL,
    period_id INTEGER NOT NULL,
    generated_date DATE NOT NULL,
    report_data TEXT, -- JSON data for the report
    file_path TEXT,
    status TEXT CHECK (status IN ('GENERATING', 'COMPLETED', 'FAILED')) DEFAULT 'GENERATING',
    tenant_id INTEGER NOT NULL,
    generated_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (period_id) REFERENCES financial_periods(id)
    -- Note: tenant_id, generated_by reference JSON-stored data
);

-- ============================================================================
-- AUDIT TRAIL AND COMPLIANCE
-- ============================================================================

-- 25. Accounting Audit Trail
CREATE TABLE IF NOT EXISTS accounting_audit_trail (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    action_type TEXT CHECK (action_type IN ('CREATE', 'UPDATE', 'DELETE', 'POST', 'REVERSE')) NOT NULL,
    old_values TEXT, -- JSON of old values
    new_values TEXT, -- JSON of new values
    changed_fields TEXT, -- JSON array of changed field names
    user_id INTEGER NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    session_id TEXT,
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Note: user_id, tenant_id reference JSON-stored data
);

-- ============================================================================
-- INTEGRATION TABLES
-- ============================================================================

-- 26. Procurement Integration Mapping
CREATE TABLE IF NOT EXISTS procurement_accounting_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    procurement_type TEXT CHECK (procurement_type IN ('PURCHASE_REQUEST', 'PURCHASE_ORDER', 'DELIVERY_NOTE', 'PAYMENT')) NOT NULL,
    procurement_id INTEGER NOT NULL,
    journal_entry_id INTEGER NOT NULL,
    mapping_type TEXT CHECK (mapping_type IN ('ACCRUAL', 'RECEIPT', 'PAYMENT', 'ADJUSTMENT')) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
);

-- 27. Billing Integration Mapping
CREATE TABLE IF NOT EXISTS billing_accounting_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    billing_type TEXT CHECK (billing_type IN ('INVOICE', 'PAYMENT', 'CREDIT_NOTE', 'DEBIT_NOTE')) NOT NULL,
    billing_id INTEGER NOT NULL,
    journal_entry_id INTEGER NOT NULL,
    mapping_type TEXT CHECK (mapping_type IN ('REVENUE', 'PAYMENT', 'ADJUSTMENT', 'WRITE_OFF')) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Chart of Accounts indexes
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_code ON chart_of_accounts(account_code);
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_type ON chart_of_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_tenant ON chart_of_accounts(tenant_id);

-- Journal Entries indexes
CREATE INDEX IF NOT EXISTS idx_journal_entries_number ON journal_entries(journal_number);
CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(transaction_date);
CREATE INDEX IF NOT EXISTS idx_journal_entries_type ON journal_entries(journal_type);
CREATE INDEX IF NOT EXISTS idx_journal_entries_tenant ON journal_entries(tenant_id);
CREATE INDEX IF NOT EXISTS idx_journal_entries_reference ON journal_entries(reference_type, reference_id);

-- Journal Entry Lines indexes
CREATE INDEX IF NOT EXISTS idx_journal_lines_entry ON journal_entry_lines(journal_entry_id);
CREATE INDEX IF NOT EXISTS idx_journal_lines_account ON journal_entry_lines(account_id);

-- Vendor indexes
CREATE INDEX IF NOT EXISTS idx_vendors_code ON vendors(vendor_code);
CREATE INDEX IF NOT EXISTS idx_vendors_tenant ON vendors(tenant_id);

-- Customer indexes
CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(customer_code);
CREATE INDEX IF NOT EXISTS idx_customers_tenant ON customers(tenant_id);

-- Invoice indexes
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_number ON purchase_invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_vendor ON purchase_invoices(vendor_id);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON purchase_invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_tenant ON purchase_invoices(tenant_id);

CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_tenant ON sales_invoices(tenant_id);

-- Payment indexes
CREATE INDEX IF NOT EXISTS idx_vendor_payments_number ON vendor_payments(payment_number);
CREATE INDEX IF NOT EXISTS idx_vendor_payments_vendor ON vendor_payments(vendor_id);
CREATE INDEX IF NOT EXISTS idx_vendor_payments_date ON vendor_payments(payment_date);

CREATE INDEX IF NOT EXISTS idx_customer_payments_number ON customer_payments(payment_number);
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);

-- Audit trail indexes
CREATE INDEX IF NOT EXISTS idx_audit_trail_table_record ON accounting_audit_trail(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_user ON accounting_audit_trail(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_date ON accounting_audit_trail(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_trail_tenant ON accounting_audit_trail(tenant_id);
