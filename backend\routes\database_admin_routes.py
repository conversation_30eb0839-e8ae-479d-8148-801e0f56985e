#!/usr/bin/env python3
"""
Database Administration Routes for AVINI Labs
Provides API endpoints for database management and administration
"""

from flask import Blueprint, jsonify, request
import os
import sqlite3
import json
from datetime import datetime
from database_manager import db_manager
from utils import token_required, read_data, write_data

database_admin_bp = Blueprint('database_admin', __name__)

@database_admin_bp.route('/admin/database/tables', methods=['GET'])
@token_required
def get_database_tables():
    """Get list of all database tables"""
    try:
        tables = db_manager.get_all_tables()
        
        # Get record counts for each table
        table_info = []
        for table in tables:
            try:
                count_result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                count = count_result[0]['count'] if count_result else 0
                table_info.append({
                    'name': table,
                    'record_count': count
                })
            except Exception as e:
                table_info.append({
                    'name': table,
                    'record_count': 0,
                    'error': str(e)
                })
        
        return jsonify({
            'success': True,
            'tables': [t['name'] for t in table_info],
            'table_info': table_info
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/stats', methods=['GET'])
@token_required
def get_database_stats():
    """Get database statistics"""
    try:
        tables = db_manager.get_all_tables()
        total_records = 0
        
        # Calculate total records across all tables
        for table in tables:
            try:
                count_result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                total_records += count_result[0]['count'] if count_result else 0
            except:
                pass
        
        # Get database file size
        db_size = 0
        if os.path.exists(db_manager.db_path):
            db_size = os.path.getsize(db_manager.db_path)
        
        # Check for backup files to get last backup date
        last_backup = None
        backup_dir = os.path.dirname(db_manager.db_path)
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.backup')]
            if backup_files:
                backup_files.sort(reverse=True)
                backup_path = os.path.join(backup_dir, backup_files[0])
                last_backup = datetime.fromtimestamp(os.path.getmtime(backup_path)).isoformat()
        
        return jsonify({
            'success': True,
            'totalTables': len(tables),
            'totalRecords': total_records,
            'dbSize': db_size,
            'lastBackup': last_backup
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/schema', methods=['GET'])
@token_required
def get_table_schema(table_name):
    """Get schema information for a specific table"""
    try:
        schema = db_manager.get_table_info(table_name)
        return jsonify({
            'success': True,
            'schema': schema
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/data', methods=['GET'])
@token_required
def get_table_data(table_name):
    """Get data from a specific table with pagination"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))
        search = request.args.get('search', '')
        
        if search:
            # Get table schema to determine searchable fields
            schema = db_manager.get_table_info(table_name)
            text_fields = [col['name'] for col in schema if col['type'] in ['TEXT', 'VARCHAR']]
            
            if text_fields:
                where_conditions = []
                params = []
                for field in text_fields:
                    where_conditions.append(f"{field} LIKE ?")
                    params.append(f"%{search}%")
                
                where_clause = " OR ".join(where_conditions)
                result = db_manager.get_paginated(
                    table_name, 
                    page=page, 
                    per_page=per_page,
                    where_clause=where_clause,
                    params=tuple(params)
                )
            else:
                result = db_manager.get_paginated(table_name, page=page, per_page=per_page)
        else:
            result = db_manager.get_paginated(table_name, page=page, per_page=per_page)
        
        return jsonify({
            'success': True,
            'data': result['data'],
            'pagination': result['pagination']
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/records', methods=['POST'])
@token_required
def create_table_record(table_name):
    """Create a new record in a specific table"""
    try:
        data = request.get_json()
        
        # Add timestamp fields if they exist in the table
        schema = db_manager.get_table_info(table_name)
        schema_fields = [col['name'] for col in schema]
        
        if 'created_at' in schema_fields and 'created_at' not in data:
            data['created_at'] = datetime.now().isoformat()
        
        if 'updated_at' in schema_fields and 'updated_at' not in data:
            data['updated_at'] = datetime.now().isoformat()
        
        record_id = db_manager.insert_record(table_name, data)
        
        return jsonify({
            'success': True,
            'id': record_id,
            'message': 'Record created successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/records/<int:record_id>', methods=['PUT'])
@token_required
def update_table_record(table_name, record_id):
    """Update a record in a specific table"""
    try:
        data = request.get_json()
        
        # Remove id from data if present
        data.pop('id', None)
        
        rows_affected = db_manager.update_record(table_name, record_id, data)
        
        if rows_affected == 0:
            return jsonify({
                'success': False,
                'error': 'Record not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Record updated successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/records/<int:record_id>', methods=['DELETE'])
@token_required
def delete_table_record(table_name, record_id):
    """Delete a record from a specific table"""
    try:
        rows_affected = db_manager.delete_record(table_name, record_id)
        
        if rows_affected == 0:
            return jsonify({
                'success': False,
                'error': 'Record not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Record deleted successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/backup', methods=['POST'])
@token_required
def create_database_backup():
    """Create a backup of the database"""
    try:
        backup_path = db_manager.backup_database()
        
        return jsonify({
            'success': True,
            'backupPath': backup_path,
            'message': 'Database backup created successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/optimize', methods=['POST'])
@token_required
def optimize_database():
    """Optimize the database by running VACUUM and ANALYZE"""
    try:
        # Run VACUUM to reclaim space and defragment
        db_manager.execute_query("VACUUM")
        
        # Run ANALYZE to update statistics
        db_manager.execute_query("ANALYZE")
        
        return jsonify({
            'success': True,
            'message': 'Database optimized successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/query', methods=['POST'])
@token_required
def execute_custom_query():
    """Execute a custom SQL query (admin only)"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({
                'success': False,
                'error': 'Query is required'
            }), 400

        # Security check - only allow SELECT statements for safety
        if not query.upper().startswith('SELECT'):
            return jsonify({
                'success': False,
                'error': 'Only SELECT queries are allowed'
            }), 400

        result = db_manager.execute_query(query)

        return jsonify({
            'success': True,
            'data': result,
            'rowCount': len(result)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Database Schema Management Endpoints

@database_admin_bp.route('/admin/database/schema/tables', methods=['GET'])
@token_required
def get_all_tables_with_schema():
    """Get all tables with their schema information"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Get all tables
        tables = db_manager.get_all_tables()

        # Get schema for each table
        tables_with_schema = []
        for table_name in tables:
            try:
                schema = db_manager.get_table_info(table_name)

                # Get row count
                count_result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                row_count = count_result[0]['count'] if count_result else 0

                tables_with_schema.append({
                    'name': table_name,
                    'row_count': row_count,
                    'columns': schema
                })
            except Exception as e:
                # Skip tables that can't be accessed
                continue

        return jsonify({
            'success': True,
            'tables': tables_with_schema
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



@database_admin_bp.route('/admin/database/schema/tables', methods=['POST'])
@token_required
def create_new_table():
    """Create a new table with specified fields"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        data = request.get_json()
        table_name = data.get('table_name', '').strip()
        fields = data.get('fields', [])

        if not table_name:
            return jsonify({
                'success': False,
                'error': 'Table name is required'
            }), 400

        if not fields:
            return jsonify({
                'success': False,
                'error': 'At least one field is required'
            }), 400

        # Validate table name
        import re
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name):
            return jsonify({
                'success': False,
                'error': 'Table name must start with a letter or underscore and contain only letters, numbers, and underscores'
            }), 400

        # Check if table already exists
        existing_tables = db_manager.get_all_tables()
        if table_name in existing_tables:
            return jsonify({
                'success': False,
                'error': f'Table "{table_name}" already exists'
            }), 400

        # Build CREATE TABLE statement
        field_definitions = []
        primary_key_field = None

        for field in fields:
            field_name = field.get('name', '').strip()
            field_type = field.get('type', '').strip()
            is_primary_key = field.get('is_primary_key', False)
            is_nullable = field.get('is_nullable', True)
            is_unique = field.get('is_unique', False)
            default_value = field.get('default_value')

            if not field_name or not field_type:
                return jsonify({
                    'success': False,
                    'error': 'All fields must have name and type'
                }), 400

            # Validate field name
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_name):
                return jsonify({
                    'success': False,
                    'error': f'Field name "{field_name}" is invalid'
                }), 400

            field_def = f"{field_name} {field_type.upper()}"

            if is_primary_key:
                if primary_key_field:
                    return jsonify({
                        'success': False,
                        'error': 'Only one primary key field is allowed'
                    }), 400
                primary_key_field = field_name
                field_def += " PRIMARY KEY"
                if field_type.upper() == 'INTEGER':
                    field_def += " AUTOINCREMENT"

            if not is_nullable and not is_primary_key:
                field_def += " NOT NULL"

            if is_unique and not is_primary_key:
                field_def += " UNIQUE"

            if default_value is not None and not is_primary_key:
                if field_type.upper() in ['TEXT', 'VARCHAR']:
                    field_def += f" DEFAULT '{default_value}'"
                else:
                    field_def += f" DEFAULT {default_value}"

            field_definitions.append(field_def)

        # Add default fields if not present
        field_names = [f.get('name') for f in fields]
        if 'created_at' not in field_names:
            field_definitions.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        if 'updated_at' not in field_names:
            field_definitions.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")

        field_list = ',\n    '.join(field_definitions)
        create_sql = f"CREATE TABLE {table_name} (\n    {field_list}\n)"

        # Execute the CREATE TABLE statement
        db_manager.execute_ddl(create_sql)

        return jsonify({
            'success': True,
            'message': f'Table "{table_name}" created successfully',
            'sql': create_sql
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/schema/data-types', methods=['GET'])
@token_required
def get_supported_data_types():
    """Get list of supported SQLite data types"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        data_types = [
            {
                'name': 'INTEGER',
                'description': 'Signed integer (1, 2, 3, 4, 6, or 8 bytes)',
                'examples': ['1', '42', '-100']
            },
            {
                'name': 'REAL',
                'description': 'Floating point number (8-byte IEEE floating point)',
                'examples': ['3.14', '2.718', '-1.5']
            },
            {
                'name': 'TEXT',
                'description': 'Text string (UTF-8, UTF-16BE or UTF-16LE)',
                'examples': ['Hello World', 'John Doe', 'Description text']
            },
            {
                'name': 'BLOB',
                'description': 'Binary data stored exactly as input',
                'examples': ['Binary files', 'Images', 'Documents']
            },
            {
                'name': 'BOOLEAN',
                'description': 'Boolean value (stored as INTEGER 0 or 1)',
                'examples': ['TRUE', 'FALSE', '1', '0']
            },
            {
                'name': 'DATE',
                'description': 'Date value (stored as TEXT in ISO8601 format)',
                'examples': ['2024-01-15', '2023-12-31']
            },
            {
                'name': 'DATETIME',
                'description': 'Date and time (stored as TEXT in ISO8601 format)',
                'examples': ['2024-01-15 14:30:00', '2023-12-31 23:59:59']
            },
            {
                'name': 'TIMESTAMP',
                'description': 'Timestamp (stored as TEXT in ISO8601 format)',
                'examples': ['2024-01-15T14:30:00Z', 'CURRENT_TIMESTAMP']
            },
            {
                'name': 'VARCHAR(n)',
                'description': 'Variable character string with maximum length',
                'examples': ['VARCHAR(50)', 'VARCHAR(255)']
            },
            {
                'name': 'DECIMAL(p,s)',
                'description': 'Decimal number with precision and scale',
                'examples': ['DECIMAL(10,2)', 'DECIMAL(8,4)']
            }
        ]

        return jsonify({
            'success': True,
            'data_types': data_types
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/export/<table_name>', methods=['GET'])
@token_required
def export_table_data(table_name):
    """Export table data as JSON"""
    try:
        data = db_manager.get_all(table_name)
        
        return jsonify({
            'success': True,
            'table': table_name,
            'data': data,
            'recordCount': len(data),
            'exportedAt': datetime.now().isoformat()
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/import/<table_name>', methods=['POST'])
@token_required
def import_table_data(table_name):
    """Import data into a table"""
    try:
        data = request.get_json()
        records = data.get('records', [])
        
        if not records:
            return jsonify({
                'success': False,
                'error': 'No records provided'
            }), 400
        
        imported_count = 0
        errors = []
        
        for i, record in enumerate(records):
            try:
                db_manager.insert_record(table_name, record)
                imported_count += 1
            except Exception as e:
                errors.append(f"Record {i+1}: {str(e)}")
        
        return jsonify({
            'success': True,
            'importedCount': imported_count,
            'totalRecords': len(records),
            'errors': errors,
            'message': f'Imported {imported_count} out of {len(records)} records'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Enhanced Database Schema Management Endpoints

@database_admin_bp.route('/admin/database/tables/<table_name>/fields', methods=['GET'])
@token_required
def get_table_fields(table_name):
    """Get detailed field information for a specific table"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Get table schema with enhanced information
        schema = db_manager.get_table_info(table_name)

        # Get foreign key information
        foreign_keys_query = f"PRAGMA foreign_key_list({table_name})"
        foreign_keys = db_manager.execute_query(foreign_keys_query)

        # Get index information
        indexes_query = f"PRAGMA index_list({table_name})"
        indexes = db_manager.execute_query(indexes_query)

        # Enhance schema with additional metadata
        enhanced_schema = []
        for field in schema:
            field_info = {
                'cid': field['cid'],
                'name': field['name'],
                'type': field['type'],
                'notnull': bool(field['notnull']),
                'dflt_value': field['dflt_value'],
                'pk': bool(field['pk']),
                'foreign_key': None,
                'indexed': False,
                'has_data': False
            }

            # Check for foreign keys
            for fk in foreign_keys:
                if fk['from'] == field['name']:
                    field_info['foreign_key'] = {
                        'table': fk['table'],
                        'to': fk['to']
                    }
                    break

            # Check if field is indexed
            for index in indexes:
                index_info_query = f"PRAGMA index_info({index['name']})"
                index_info = db_manager.execute_query(index_info_query)
                for idx_field in index_info:
                    if idx_field['name'] == field['name']:
                        field_info['indexed'] = True
                        break

            # Check if field has data
            try:
                count_query = f"SELECT COUNT(*) as count FROM {table_name} WHERE {field['name']} IS NOT NULL"
                count_result = db_manager.execute_query(count_query)
                field_info['has_data'] = count_result[0]['count'] > 0 if count_result else False
            except:
                field_info['has_data'] = False

            enhanced_schema.append(field_info)

        return jsonify({
            'success': True,
            'fields': enhanced_schema,
            'table_name': table_name
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/fields', methods=['POST'])
@token_required
def add_table_field(table_name):
    """Add a new field to an existing table"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        data = request.get_json()
        field_name = data.get('field_name', '').strip()
        field_type = data.get('field_type', 'TEXT')
        not_null = data.get('not_null', False)
        default_value = data.get('default_value')

        if not field_name:
            return jsonify({
                'success': False,
                'error': 'Field name is required'
            }), 400

        # Validate field name (alphanumeric and underscore only)
        import re
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_name):
            return jsonify({
                'success': False,
                'error': 'Field name must start with a letter or underscore and contain only letters, numbers, and underscores'
            }), 400

        # Check if field already exists
        existing_schema = db_manager.get_table_info(table_name)
        existing_fields = [field['name'] for field in existing_schema]

        if field_name in existing_fields:
            return jsonify({
                'success': False,
                'error': f'Field "{field_name}" already exists in table "{table_name}"'
            }), 400

        # Build ALTER TABLE statement
        alter_statement = f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type}"

        if not_null and default_value is not None:
            alter_statement += f" NOT NULL DEFAULT '{default_value}'"
        elif default_value is not None:
            alter_statement += f" DEFAULT '{default_value}'"
        elif not_null:
            # For NOT NULL without default, we need to provide a default value
            if field_type.upper() in ['INTEGER', 'INT']:
                alter_statement += " NOT NULL DEFAULT 0"
            elif field_type.upper() in ['REAL', 'FLOAT', 'DOUBLE']:
                alter_statement += " NOT NULL DEFAULT 0.0"
            elif field_type.upper() in ['TEXT', 'VARCHAR', 'CHAR']:
                alter_statement += " NOT NULL DEFAULT ''"
            else:
                alter_statement += " NOT NULL DEFAULT ''"

        # Execute the ALTER TABLE statement
        db_manager.execute_query(alter_statement)

        return jsonify({
            'success': True,
            'message': f'Field "{field_name}" added successfully to table "{table_name}"'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/tables/<table_name>/fields/<field_name>', methods=['DELETE'])
@token_required
def delete_table_field(table_name, field_name):
    """Delete a field from a table (with data integrity checks)"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Check if field exists
        schema = db_manager.get_table_info(table_name)
        field_exists = any(field['name'] == field_name for field in schema)

        if not field_exists:
            return jsonify({
                'success': False,
                'error': f'Field "{field_name}" does not exist in table "{table_name}"'
            }), 404

        # Check if field is primary key
        field_info = next((field for field in schema if field['name'] == field_name), None)
        if field_info and field_info['pk']:
            return jsonify({
                'success': False,
                'error': 'Cannot delete primary key field'
            }), 400

        # Check if field has data
        force_delete = request.args.get('force', 'false').lower() == 'true'

        try:
            count_query = f"SELECT COUNT(*) as count FROM {table_name} WHERE {field_name} IS NOT NULL"
            count_result = db_manager.execute_query(count_query)
            has_data = count_result[0]['count'] > 0 if count_result else False

            if has_data and not force_delete:
                return jsonify({
                    'success': False,
                    'error': f'Field "{field_name}" contains data. Use force=true to delete anyway.',
                    'has_data': True,
                    'record_count': count_result[0]['count']
                }), 400
        except:
            # If we can't check for data, proceed with caution
            if not force_delete:
                return jsonify({
                    'success': False,
                    'error': 'Cannot verify if field contains data. Use force=true to delete anyway.',
                    'has_data': True
                }), 400

        # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
        # Get all fields except the one to delete
        remaining_fields = [field for field in schema if field['name'] != field_name]

        if not remaining_fields:
            return jsonify({
                'success': False,
                'error': 'Cannot delete the last field in a table'
            }), 400

        # Create new table structure
        field_definitions = []
        for field in remaining_fields:
            field_def = f"{field['name']} {field['type']}"
            if field['notnull']:
                field_def += " NOT NULL"
            if field['dflt_value'] is not None:
                field_def += f" DEFAULT {field['dflt_value']}"
            if field['pk']:
                field_def += " PRIMARY KEY"
            field_definitions.append(field_def)

        # Get table data
        select_fields = [field['name'] for field in remaining_fields]
        data_query = f"SELECT {', '.join(select_fields)} FROM {table_name}"
        table_data = db_manager.execute_query(data_query)

        # Start transaction for table recreation
        with db_manager.transaction() as conn:
            cursor = conn.cursor()

            # Create temporary table
            temp_table = f"{table_name}_temp_{int(datetime.now().timestamp())}"
            create_temp_query = f"CREATE TABLE {temp_table} ({', '.join(field_definitions)})"
            cursor.execute(create_temp_query)

            # Copy data to temporary table
            if table_data:
                placeholders = ', '.join(['?' for _ in select_fields])
                insert_query = f"INSERT INTO {temp_table} ({', '.join(select_fields)}) VALUES ({placeholders})"
                for row in table_data:
                    values = [row[field] for field in select_fields]
                    cursor.execute(insert_query, values)

            # Drop original table
            cursor.execute(f"DROP TABLE {table_name}")

            # Rename temporary table
            cursor.execute(f"ALTER TABLE {temp_table} RENAME TO {table_name}")

        return jsonify({
            'success': True,
            'message': f'Field "{field_name}" deleted successfully from table "{table_name}"'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500







# Database Configuration Endpoints

@database_admin_bp.route('/admin/database/test-connection', methods=['POST'])
@token_required
def test_database_connection():
    """Test database connection with provided configuration"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        data = request.get_json()
        db_type = data.get('db_type', 'sqlite')
        db_host = data.get('db_host', 'localhost')
        db_port = data.get('db_port', '5432')
        db_name = data.get('db_name', 'lab_management.db')
        db_username = data.get('db_username', '')
        db_password = data.get('db_password', '')

        if db_type == 'sqlite':
            # Test SQLite connection
            try:
                # Check if file exists or can be created
                db_path = db_name if os.path.isabs(db_name) else os.path.join('data', db_name)

                # Try to connect
                test_conn = sqlite3.connect(db_path)
                test_conn.execute("SELECT 1")
                test_conn.close()

                return jsonify({
                    'success': True,
                    'message': 'SQLite database connection successful!',
                    'details': f'Connected to database at: {db_path}'
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': 'SQLite connection failed',
                    'error': str(e)
                }), 400

        elif db_type == 'postgresql':
            try:
                import psycopg2

                # Build connection string
                conn_string = f"host='{db_host}' port='{db_port}' dbname='{db_name}' user='{db_username}' password='{db_password}'"

                # Test connection
                test_conn = psycopg2.connect(conn_string)
                test_conn.close()

                return jsonify({
                    'success': True,
                    'message': 'PostgreSQL database connection successful!',
                    'details': f'Connected to {db_name} on {db_host}:{db_port}'
                })

            except ImportError:
                return jsonify({
                    'success': False,
                    'message': 'PostgreSQL driver not installed',
                    'error': 'Please install psycopg2-binary package'
                }), 400
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': 'PostgreSQL connection failed',
                    'error': str(e)
                }), 400

        elif db_type == 'mysql':
            try:
                import mysql.connector

                # Test connection
                test_conn = mysql.connector.connect(
                    host=db_host,
                    port=int(db_port),
                    database=db_name,
                    user=db_username,
                    password=db_password
                )
                test_conn.close()

                return jsonify({
                    'success': True,
                    'message': 'MySQL database connection successful!',
                    'details': f'Connected to {db_name} on {db_host}:{db_port}'
                })

            except ImportError:
                return jsonify({
                    'success': False,
                    'message': 'MySQL driver not installed',
                    'error': 'Please install mysql-connector-python package'
                }), 400
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': 'MySQL connection failed',
                    'error': str(e)
                }), 400
        else:
            return jsonify({
                'success': False,
                'message': 'Unsupported database type',
                'error': f'Database type "{db_type}" is not supported'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Connection test failed',
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/config', methods=['GET'])
@token_required
def get_database_config():
    """Get current database configuration"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Try to read existing config from settings
        try:
            settings = read_data('settings.json')
            db_config = settings.get('database', {})
        except:
            # Default configuration
            db_config = {
                'db_type': 'sqlite',
                'db_host': 'localhost',
                'db_port': '5432',
                'db_name': 'avini_labs.db',
                'db_username': '',
                'db_password': '',
                'backup_enabled': True,
                'backup_frequency': 'daily',
                'backup_retention_days': 30,
                'auto_optimize': True
            }

        # Don't return password in response
        safe_config = db_config.copy()
        safe_config['db_password'] = '***' if db_config.get('db_password') else ''

        return jsonify({
            'success': True,
            'config': safe_config
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/database/config', methods=['POST'])
@token_required
def update_database_config():
    """Update database configuration"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        data = request.get_json()

        # Read existing settings
        try:
            settings = read_data('settings.json')
        except:
            settings = {}

        # Update database configuration
        if 'database' not in settings:
            settings['database'] = {}

        # Update only provided fields
        for key, value in data.items():
            if key in ['db_type', 'db_host', 'db_port', 'db_name', 'db_username', 'db_password',
                      'backup_enabled', 'backup_frequency', 'backup_retention_days', 'auto_optimize']:
                settings['database'][key] = value

        # Add timestamp
        settings['database']['updated_at'] = datetime.now().isoformat()
        settings['database']['updated_by'] = request.current_user.get('id')

        # Save settings
        write_data('settings.json', settings)

        return jsonify({
            'success': True,
            'message': 'Database configuration updated successfully',
            'note': 'Changes will take effect after system restart'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Migration Management Endpoints

@database_admin_bp.route('/admin/migration/status', methods=['GET'])
@token_required
def get_migration_status():
    """Get current migration status"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Try to read migration status from file
        try:
            migration_status = read_data('migration_status.json')
        except:
            # Default status if no migration has been run
            migration_status = {
                'status': 'pending',
                'progress': 0,
                'started_at': None,
                'completed_at': None,
                'error': None,
                'tables': {}
            }

        return jsonify(migration_status)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/migration/start', methods=['POST'])
@token_required
def start_migration():
    """Start data migration from JSON to SQLite"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Check if migration is already running
        try:
            current_status = read_data('migration_status.json')
            if current_status.get('status') == 'running':
                return jsonify({
                    'success': False,
                    'message': 'Migration is already running'
                }), 400
        except:
            pass

        # Initialize migration status
        migration_status = {
            'status': 'running',
            'progress': 0,
            'started_at': datetime.now().isoformat(),
            'completed_at': None,
            'error': None,
            'tables': {},
            'started_by': request.current_user.get('id')
        }

        # Save initial status
        write_data('migration_status.json', migration_status)

        # Start migration in background (you would typically use a task queue like Celery)
        # For now, we'll simulate the migration process
        import threading

        def run_migration():
            try:
                # Simulate migration process
                tables = ['tenants', 'users', 'patients', 'departments', 'test_master', 'samples', 'billings', 'results']
                total_tables = len(tables)

                for i, table in enumerate(tables):
                    # Update table status
                    migration_status['tables'][table] = {
                        'status': 'running',
                        'progress': 0,
                        'processed': 0,
                        'total': 0,
                        'error': None,
                        'duration': None
                    }
                    migration_status['progress'] = int((i / total_tables) * 100)
                    write_data('migration_status.json', migration_status)

                    # Simulate processing time
                    import time
                    time.sleep(2)

                    # Mark table as completed
                    migration_status['tables'][table]['status'] = 'completed'
                    migration_status['tables'][table]['progress'] = 100
                    migration_status['progress'] = int(((i + 1) / total_tables) * 100)
                    write_data('migration_status.json', migration_status)

                # Mark migration as completed
                migration_status['status'] = 'completed'
                migration_status['progress'] = 100
                migration_status['completed_at'] = datetime.now().isoformat()
                write_data('migration_status.json', migration_status)

            except Exception as e:
                # Mark migration as failed
                migration_status['status'] = 'failed'
                migration_status['error'] = str(e)
                migration_status['completed_at'] = datetime.now().isoformat()
                write_data('migration_status.json', migration_status)

        # Start migration thread
        migration_thread = threading.Thread(target=run_migration)
        migration_thread.daemon = True
        migration_thread.start()

        return jsonify({
            'success': True,
            'message': 'Migration started successfully',
            'status': migration_status
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_admin_bp.route('/admin/migration/logs', methods=['GET'])
@token_required
def get_migration_logs():
    """Get migration logs"""
    try:
        # Check if user has admin privileges
        if request.current_user.get('role') not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Unauthorized'}), 403

        # Try to read migration logs
        try:
            log_file_path = os.path.join('logs', 'migration.log')
            if os.path.exists(log_file_path):
                with open(log_file_path, 'r') as f:
                    logs = f.readlines()
                return jsonify({
                    'success': True,
                    'logs': logs
                })
            else:
                return jsonify({
                    'success': True,
                    'logs': ['No migration logs found.']
                })
        except Exception as e:
            return jsonify({
                'success': True,
                'logs': [f'Error reading logs: {str(e)}']
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Error handlers
@database_admin_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@database_admin_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
