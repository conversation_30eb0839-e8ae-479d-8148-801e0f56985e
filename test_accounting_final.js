const axios = require('axios');

const baseURL = 'http://localhost:5002';

// Get a valid token first
async function getAuthToken() {
    try {
        const response = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        return response.data.token;
    } catch (error) {
        console.log('❌ Failed to get auth token');
        return null;
    }
}

// Test routes with authentication
const testRoutes = [
    { url: '/api/accounting/health', method: 'GET', auth: false },
    { url: '/api/accounting/chart-of-accounts?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/customers?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/vendors?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/journal-entries?tenant_id=1&page=1&per_page=50', method: 'GET', auth: true },
    { url: '/api/accounting/sales-invoices?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/customer-payments?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/aged-receivables?tenant_id=1', method: 'GET', auth: true },
    { url: '/api/accounting/tax/gst-dashboard?tenant_id=1&start_date=2025-08-31&end_date=2025-09-16', method: 'GET', auth: true },
    { url: '/api/accounting/inventory/valuation?tenant_id=1&costing_method=FIFO', method: 'GET', auth: true },
    { url: '/api/accounting/reports/trial-balance?tenant_id=1&as_of_date=2025-09-16', method: 'GET', auth: true },
    { url: '/api/accounting/reports/profit-loss?tenant_id=1&start_date=2024-12-31&end_date=2025-09-16', method: 'GET', auth: true },
    { url: '/api/accounting/reports/balance-sheet?tenant_id=1&as_of_date=2025-09-16', method: 'GET', auth: true },
    { url: '/api/accounting/reports/cash-flow?tenant_id=1&start_date=2024-12-31&end_date=2025-09-16', method: 'GET', auth: true }
];

async function testRoute(route, token) {
    try {
        const config = {
            timeout: 10000,
            validateStatus: function (status) {
                return status < 500; // Accept any status code less than 500
            }
        };
        
        if (route.auth && token) {
            config.headers = {
                'Authorization': `Bearer ${token}`
            };
        }
        
        const response = await axios.get(baseURL + route.url, config);
        
        if (response.status === 200) {
            console.log(`✅ ${route.url} - SUCCESS (200)`);
            return 'SUCCESS';
        } else if (response.status === 401) {
            console.log(`🔒 ${route.url} - UNAUTHORIZED (401)`);
            return 'UNAUTHORIZED';
        } else if (response.status === 404) {
            console.log(`❌ ${route.url} - NOT FOUND (404)`);
            return 'NOT_FOUND';
        } else {
            console.log(`⚠️  ${route.url} - Status: ${response.status}`);
            return response.status;
        }
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log(`❌ ${route.url} - Server not running`);
            return 'SERVER_DOWN';
        } else if (error.response) {
            console.log(`⚠️  ${route.url} - Status: ${error.response.status}`);
            return error.response.status;
        } else {
            console.log(`❌ ${route.url} - Error: ${error.message}`);
            return 'ERROR';
        }
    }
}

async function testCustomerCreation(token) {
    try {
        const customerData = {
            customer_name: `Test Customer ${Date.now()}`,
            email: '<EMAIL>',
            phone: '**********',
            address: 'Test Address',
            tenant_id: 1
        };
        
        const response = await axios.post(`${baseURL}/api/accounting/customers`, customerData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000,
            validateStatus: function (status) {
                return status < 500;
            }
        });
        
        if (response.status === 201) {
            console.log(`✅ Customer Creation - SUCCESS (201)`);
            return 'SUCCESS';
        } else {
            console.log(`⚠️  Customer Creation - Status: ${response.status}`);
            return response.status;
        }
    } catch (error) {
        console.log(`❌ Customer Creation - Error: ${error.message}`);
        return 'ERROR';
    }
}

async function runComprehensiveTest() {
    console.log('🧪 COMPREHENSIVE ACCOUNTING SYSTEM TEST');
    console.log('=' + '='.repeat(60));
    
    // Get authentication token
    console.log('\n🔑 Getting authentication token...');
    const token = await getAuthToken();
    
    if (!token) {
        console.log('❌ Cannot proceed without authentication token');
        return;
    }
    
    console.log('✅ Authentication token obtained');
    
    // Test all routes
    console.log('\n📡 Testing API Routes...');
    let successCount = 0;
    let totalCount = testRoutes.length;
    
    for (const route of testRoutes) {
        const result = await testRoute(route, token);
        if (result === 'SUCCESS') {
            successCount++;
        }
    }
    
    // Test customer creation
    console.log('\n👤 Testing Customer Creation...');
    const customerResult = await testCustomerCreation(token);
    if (customerResult === 'SUCCESS') {
        successCount++;
        totalCount++;
    }
    
    // Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('=' + '='.repeat(40));
    console.log(`✅ Successful: ${successCount}/${totalCount}`);
    console.log(`❌ Failed: ${totalCount - successCount}/${totalCount}`);
    console.log(`📈 Success Rate: ${Math.round((successCount / totalCount) * 100)}%`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 ALL TESTS PASSED! Accounting system is fully operational!');
    } else {
        console.log('\n⚠️  Some tests failed. Check the logs above for details.');
    }
}

runComprehensiveTest();
