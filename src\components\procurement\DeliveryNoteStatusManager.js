import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge, Modal, <PERSON>, Alert, Spinner } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEdit,
  faCheck,
  faTimes,
  faHistory,
  faExclamationTriangle,
  faInfoCircle,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';

const DeliveryNoteStatusManager = ({ deliveryNote, onStatusUpdate }) => {
  const { currentUser } = useAuth();
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusType, setStatusType] = useState(''); // 'hub' or 'franchise'
  const [newStatus, setNewStatus] = useState('');
  const [reason, setReason] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [allowedTransitions, setAllowedTransitions] = useState({ hub_transitions: [], franchise_transitions: [] });
  const [statusHistory, setStatusHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);

  useEffect(() => {
    if (deliveryNote?.id) {
      loadAllowedTransitions();
    }
  }, [deliveryNote]);

  const loadAllowedTransitions = async () => {
    try {
      const response = await procurementAPI.getDeliveryNoteAllowedTransitions(deliveryNote.id);
      if (response.data.success) {
        setAllowedTransitions(response.data.data);
      }
    } catch (err) {
      console.error('Error loading allowed transitions:', err);
    }
  };

  const loadStatusHistory = async () => {
    try {
      const response = await procurementAPI.getDeliveryNoteStatusHistory(deliveryNote.id);
      if (response.data.success) {
        setStatusHistory(response.data.data);
      }
    } catch (err) {
      console.error('Error loading status history:', err);
    }
  };

  const handleStatusUpdate = async () => {
    if (!newStatus || !statusType) return;

    try {
      setLoading(true);
      setError(null);

      const updateData = {
        status: newStatus,
        reason: reason,
        notes: notes
      };

      let response;
      if (statusType === 'hub') {
        response = await procurementAPI.updateDeliveryNoteHubStatus(deliveryNote.id, updateData);
      } else {
        response = await procurementAPI.updateDeliveryNoteFranchiseStatus(deliveryNote.id, updateData);
      }

      if (response.data.success) {
        setShowStatusModal(false);
        setNewStatus('');
        setReason('');
        setNotes('');
        if (onStatusUpdate) {
          onStatusUpdate();
        }
        loadAllowedTransitions();
      } else {
        setError(response.data.error || 'Failed to update status');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to update status');
    } finally {
      setLoading(false);
    }
  };

  const openStatusModal = (type) => {
    setStatusType(type);
    setNewStatus('');
    setReason('');
    setNotes('');
    setError(null);
    setShowStatusModal(true);
  };

  const openHistoryModal = () => {
    loadStatusHistory();
    setShowHistory(true);
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'prepared': return 'secondary';
      case 'dispatched': return 'primary';
      case 'in_transit': return 'info';
      case 'delivered': return 'success';
      case 'cancelled': return 'danger';
      case 'returned': return 'warning';
      case 'reprocessed': return 'info';
      case 'partially_delivered': return 'warning';
      case 'pending_receipt': return 'secondary';
      case 'received': return 'info';
      case 'confirmed': return 'success';
      case 'rejected': return 'danger';
      case 'partially_accepted': return 'warning';
      default: return 'secondary';
    }
  };

  const formatStatusName = (status) => {
    return status?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const canManageHubStatus = () => {
    return currentUser?.role === 'admin' || currentUser?.role === 'hub_admin';
  };

  const canManageFranchiseStatus = () => {
    return (
      currentUser?.role === 'admin' || 
      currentUser?.role === 'hub_admin' ||
      (currentUser?.role === 'franchise_admin' && deliveryNote?.to_tenant_id === currentUser?.tenant_id)
    );
  };

  return (
    <Card className="mb-4">
      <Card.Header className="d-flex text-primary justify-content-between align-items-center">
        <h5 className="mb-0">
          <FontAwesomeIcon icon={faEdit} className="me-2" />
          Status Management
        </h5>
        <Button variant="outline-secondary" size="sm" onClick={openHistoryModal}>
          <FontAwesomeIcon icon={faHistory} className="me-1" />
          History
        </Button>
      </Card.Header>
      <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
        <div className="row">
          {/* Hub Status */}
          <div className="col-md-6 mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <strong>Hub Status:</strong>
              {canManageHubStatus() && allowedTransitions.hub_transitions.length > 0 && (
                <Button 
                  variant="outline-primary" 
                  size="sm"
                  onClick={() => openStatusModal('hub')}
                >
                  <FontAwesomeIcon icon={faEdit} className="me-1" />
                  Update
                </Button>
              )}
            </div>
            <Badge bg={getStatusBadgeVariant(deliveryNote.hub_status)} className="fs-6">
              {formatStatusName(deliveryNote.hub_status)}
            </Badge>
          </div>

          {/* Franchise Status */}
          <div className="col-md-6 mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <strong>Franchise Status:</strong>
              {canManageFranchiseStatus() && allowedTransitions.franchise_transitions.length > 0 && (
                <Button 
                  variant="outline-primary" 
                  size="sm"
                  onClick={() => openStatusModal('franchise')}
                >
                  <FontAwesomeIcon icon={faEdit} className="me-1" />
                  Update
                </Button>
              )}
            </div>
            <Badge bg={getStatusBadgeVariant(deliveryNote.franchise_status)} className="fs-6">
              {formatStatusName(deliveryNote.franchise_status)}
            </Badge>
          </div>
        </div>

        {/* Legacy Status */}
        <div className="mt-3 pt-3 border-top">
          <small className="text-muted">
            <FontAwesomeIcon icon={faInfoCircle} className="me-1" />
            Legacy Status: <Badge bg={getStatusBadgeVariant(deliveryNote.status)} className="ms-1">
              {formatStatusName(deliveryNote.status)}
            </Badge>
          </small>
        </div>
      </Card.Body>

      {/* Status Update Modal */}
      <Modal show={showStatusModal} onHide={() => setShowStatusModal(false)} size="md">
        <Modal.Header closeButton>
          <Modal.Title>
            Update {statusType === 'hub' ? 'Hub' : 'Franchise'} Status
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && (
            <Alert variant="danger">
              <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
              {error}
            </Alert>
          )}

          <Form>
            <Form.Group className="mb-3">
              <Form.Label>New Status *</Form.Label>
              <Form.Select
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
                required
              >
                <option value="">Select new status...</option>
                {(statusType === 'hub' ? allowedTransitions.hub_transitions : allowedTransitions.franchise_transitions).map(status => (
                  <option key={status} value={status}>
                    {formatStatusName(status)}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Reason</Form.Label>
              <Form.Control
                type="text"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Enter reason for status change..."
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Notes</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Additional notes..."
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStatusModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleStatusUpdate}
            disabled={!newStatus || loading}
          >
            {loading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faCheck} className="me-2" />
                Update Status
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Status History Modal */}
      <Modal show={showHistory} onHide={() => setShowHistory(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Status Change History</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {statusHistory.length === 0 ? (
            <p className="text-muted">No status changes recorded.</p>
          ) : (
            <div className="timeline">
              {statusHistory.map((change, index) => (
                <div key={index} className="timeline-item mb-3 pb-3 border-bottom">
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <strong>{change.status_type.replace('_', ' ').toUpperCase()}</strong>
                      <div>
                        {change.old_status && (
                          <Badge bg="secondary" className="me-2">
                            {formatStatusName(change.old_status)}
                          </Badge>
                        )}
                        <FontAwesomeIcon icon={faArrowRight} className="me-2 text-muted" />
                        <Badge bg={getStatusBadgeVariant(change.new_status)}>
                          {formatStatusName(change.new_status)}
                        </Badge>
                      </div>
                      {change.change_reason && (
                        <div className="mt-1">
                          <small className="text-muted">Reason: {change.change_reason}</small>
                        </div>
                      )}
                      {change.notes && (
                        <div className="mt-1">
                          <small className="text-muted">Notes: {change.notes}</small>
                        </div>
                      )}
                    </div>
                    <div className="text-end">
                      <small className="text-muted">
                        {new Date(change.created_at).toLocaleString()}
                      </small>
                      <br />
                      <small className="text-muted">
                        by {change.changed_by_username}
                      </small>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowHistory(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default DeliveryNoteStatusManager;
