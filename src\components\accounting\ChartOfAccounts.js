import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Alert, Spinner, Badge } from 'react-bootstrap';
import { FaPlus, FaEdit, FaEye } from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const ChartOfAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [filterType, setFilterType] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    account_code: '',
    account_name: '',
    account_type: 'ASSET',
    account_subtype: 'CURRENT_ASSET',
    parent_account_id: null,
    description: '',
    is_active: true
  });

  useEffect(() => {
    loadAccounts();
  }, [filterType]);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tenantId = user.tenant_id || 1;
      
      const response = await accountingService.getChartOfAccounts(tenantId, filterType || null);
      setAccounts(response.data || []);
    } catch (err) {
      setError('Failed to load chart of accounts');
      console.error('Error loading accounts:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleViewAccount = (account) => {
    setSelectedAccount(account);
    setShowModal(true);
  };

  const handleEditAccount = (account) => {
    setSelectedAccount(account);
    setFormData({
      account_code: account.account_code,
      account_name: account.account_name,
      account_type: account.account_type,
      account_subtype: account.account_subtype || 'CURRENT_ASSET',
      parent_account_id: account.parent_account_id,
      description: account.description || '',
      is_active: account.is_active
    });
    setShowAddModal(true);
  };

  const handleAddAccount = () => {
    setShowAddModal(true);
  };

  const handleCloseAddModal = () => {
    setShowAddModal(false);
    setFormData({
      account_code: '',
      account_name: '',
      account_type: 'ASSET',
      account_subtype: 'CURRENT_ASSET',
      parent_account_id: null,
      description: '',
      is_active: true
    });
  };

  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmitAccount = async (e) => {
    e.preventDefault();
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tenantId = user.tenant_id || 1;

      const accountData = {
        ...formData,
        tenant_id: tenantId,
        created_by: user.id
      };

      if (selectedAccount && selectedAccount.id) {
        // Update existing account
        await accountingService.updateAccount(selectedAccount.id, accountData);
        setError(null);
      } else {
        // Create new account
        await accountingService.createAccount(accountData);
      }

      setShowAddModal(false);
      setSelectedAccount(null);
      loadAccounts(); // Reload the accounts list
      setFormData({
        account_code: '',
        account_name: '',
        account_type: 'ASSET',
        account_subtype: 'CURRENT_ASSET',
        parent_account_id: null,
        description: '',
        is_active: true
      });
    } catch (err) {
      console.error('Error saving account:', err);

      // Provide specific error messages based on the error
      let errorMessage = selectedAccount ? 'Failed to update account' : 'Failed to create account';

      if (err.response && err.response.data && err.response.data.error) {
        const backendError = err.response.data.error;

        // Handle specific business rule errors
        if (backendError.includes('existing transactions')) {
          errorMessage = 'Cannot edit this account because it has existing transactions. Accounts with transaction history cannot be modified to maintain data integrity.';
        } else if (backendError.includes('not found')) {
          errorMessage = 'Account not found. It may have been deleted by another user.';
        } else if (backendError.includes('already exists')) {
          errorMessage = 'An account with this code already exists. Please use a different account code.';
        } else if (backendError.includes('Invalid account type')) {
          errorMessage = 'Invalid account type selected. Please choose a valid account type.';
        } else if (backendError.includes('Missing required field')) {
          errorMessage = 'Please fill in all required fields.';
        } else if (err.response.status === 403) {
          errorMessage = 'You do not have permission to edit accounts. Please contact your administrator.';
        } else if (err.response.status === 405) {
          errorMessage = 'Account editing is currently disabled. Please contact your administrator if you need to modify account information.';
        } else {
          errorMessage = backendError;
        }
      } else if (err.response && err.response.status === 405) {
        errorMessage = 'Account editing is currently disabled. Please contact your administrator if you need to modify account information.';
      }

      setError(errorMessage);
    }
  };

  const getAccountTypeColor = (type) => {
    const colors = {
      'ASSET': 'success',
      'LIABILITY': 'danger',
      'EQUITY': 'primary',
      'REVENUE': 'info',
      'EXPENSE': 'warning',
      'COST_OF_GOODS_SOLD': 'secondary'
    };
    return colors[type] || 'light';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0 text-primary">Chart of Accounts</h5>
          <div className="d-flex gap-2">
            <Form.Select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              style={{ width: 'auto' }}
            >
              <option value="">All Account Types</option>
              <option value="ASSET">Assets</option>
              <option value="LIABILITY">Liabilities</option>
              <option value="EQUITY">Equity</option>
              <option value="REVENUE">Revenue</option>
              <option value="EXPENSE">Expenses</option>
              <option value="COST_OF_GOODS_SOLD">Cost of Goods Sold</option>
            </Form.Select>
            <Button variant="primary" size="sm" onClick={handleAddAccount}>
              <FaPlus className="me-1" />
              Add Account
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          
          {accounts.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-muted">No accounts found</p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover>
                <thead>
                  <tr>
                    <th>Account Code</th>
                    <th>Account Name</th>
                    <th>Type</th>
                    <th>Parent Account</th>
                    <th>Current Balance</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map((account) => (
                    <tr key={account.id}>
                      <td>
                        <code>{account.account_code}</code>
                      </td>
                      <td>{account.account_name}</td>
                      <td>
                        <Badge bg={getAccountTypeColor(account.account_type)}>
                          {account.account_type}
                        </Badge>
                      </td>
                      <td>{account.parent_account_name || '-'}</td>
                      <td className="text-end">
                        {formatCurrency(account.current_balance)}
                      </td>
                      <td>
                        <Badge bg={account.is_active ? 'success' : 'secondary'}>
                          {account.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleViewAccount(account)}
                          >
                            <FaEye />
                          </Button>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => handleEditAccount(account)}
                            disabled={account.current_balance !== 0 || account.current_balance !== '0.00'}
                            title={account.current_balance !== 0 && account.current_balance !== '0.00' ?
                              'Cannot edit account with existing transactions' : 'Edit account'}
                          >
                            <FaEdit />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Account Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Account Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedAccount && (
            <div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <strong>Account Code:</strong>
                  <p><code>{selectedAccount.account_code}</code></p>
                </div>
                <div className="col-md-6">
                  <strong>Account Name:</strong>
                  <p>{selectedAccount.account_name}</p>
                </div>
              </div>
              
              <div className="row mb-3">
                <div className="col-md-6">
                  <strong>Account Type:</strong>
                  <p>
                    <Badge bg={getAccountTypeColor(selectedAccount.account_type)}>
                      {selectedAccount.account_type}
                    </Badge>
                  </p>
                </div>
                <div className="col-md-6">
                  <strong>Current Balance:</strong>
                  <p className="h5">{formatCurrency(selectedAccount.current_balance)}</p>
                </div>
              </div>

              <div className="row mb-3">
                <div className="col-md-6">
                  <strong>Parent Account:</strong>
                  <p>{selectedAccount.parent_account_name || 'None'}</p>
                </div>
                <div className="col-md-6">
                  <strong>Status:</strong>
                  <p>
                    <Badge bg={selectedAccount.is_active ? 'success' : 'secondary'}>
                      {selectedAccount.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </p>
                </div>
              </div>

              {selectedAccount.description && (
                <div className="row mb-3">
                  <div className="col-12">
                    <strong>Description:</strong>
                    <p>{selectedAccount.description}</p>
                  </div>
                </div>
              )}

              <div className="row">
                <div className="col-md-6">
                  <strong>Created:</strong>
                  <p>{new Date(selectedAccount.created_at).toLocaleDateString()}</p>
                </div>
                <div className="col-md-6">
                  <strong>Last Updated:</strong>
                  <p>{new Date(selectedAccount.updated_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <Button variant="primary">
            Edit Account
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Add Account Modal */}
      <Modal show={showAddModal} onHide={handleCloseAddModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{selectedAccount ? 'Edit Account' : 'Add New Account'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmitAccount}>
            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Account Code *</Form.Label>
                  <Form.Control
                    type="text"
                    name="account_code"
                    value={formData.account_code}
                    onChange={handleFormChange}
                    required
                    placeholder="e.g., 1001"
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Account Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="account_name"
                    value={formData.account_name}
                    onChange={handleFormChange}
                    required
                    placeholder="e.g., Cash in Hand"
                  />
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Account Type *</Form.Label>
                  <Form.Select
                    name="account_type"
                    value={formData.account_type}
                    onChange={handleFormChange}
                    required
                  >
                    <option value="ASSET">Asset</option>
                    <option value="LIABILITY">Liability</option>
                    <option value="EQUITY">Equity</option>
                    <option value="REVENUE">Revenue</option>
                    <option value="EXPENSE">Expense</option>
                    <option value="COST_OF_GOODS_SOLD">Cost of Goods Sold</option>
                  </Form.Select>
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Account Subtype *</Form.Label>
                  <Form.Select
                    name="account_subtype"
                    value={formData.account_subtype}
                    onChange={handleFormChange}
                    required
                  >
                    {formData.account_type === 'ASSET' && (
                      <>
                        <option value="CURRENT_ASSET">Current Asset</option>
                        <option value="FIXED_ASSET">Fixed Asset</option>
                        <option value="INTANGIBLE_ASSET">Intangible Asset</option>
                      </>
                    )}
                    {formData.account_type === 'LIABILITY' && (
                      <>
                        <option value="CURRENT_LIABILITY">Current Liability</option>
                        <option value="LONG_TERM_LIABILITY">Long Term Liability</option>
                      </>
                    )}
                    {formData.account_type === 'EQUITY' && (
                      <>
                        <option value="SHARE_CAPITAL">Share Capital</option>
                        <option value="RETAINED_EARNINGS">Retained Earnings</option>
                        <option value="OTHER_EQUITY">Other Equity</option>
                      </>
                    )}
                    {formData.account_type === 'REVENUE' && (
                      <>
                        <option value="OPERATING_REVENUE">Operating Revenue</option>
                        <option value="NON_OPERATING_REVENUE">Non-Operating Revenue</option>
                      </>
                    )}
                    {formData.account_type === 'EXPENSE' && (
                      <>
                        <option value="OPERATING_EXPENSE">Operating Expense</option>
                        <option value="NON_OPERATING_EXPENSE">Non-Operating Expense</option>
                      </>
                    )}
                    {formData.account_type === 'COST_OF_GOODS_SOLD' && (
                      <option value="DIRECT_COST">Direct Cost</option>
                    )}
                  </Form.Select>
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Parent Account</Form.Label>
                  <Form.Select
                    name="parent_account_id"
                    value={formData.parent_account_id || ''}
                    onChange={handleFormChange}
                  >
                    <option value="">None (Top Level)</option>
                    {accounts.filter(acc => acc.account_type === formData.account_type).map(account => (
                      <option key={account.id} value={account.id}>
                        {account.account_code} - {account.account_name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </div>
            </div>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData.description}
                onChange={handleFormChange}
                placeholder="Optional description for this account"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleFormChange}
                label="Active Account"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseAddModal}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmitAccount}>
            {selectedAccount ? 'Update Account' : 'Create Account'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ChartOfAccounts;
