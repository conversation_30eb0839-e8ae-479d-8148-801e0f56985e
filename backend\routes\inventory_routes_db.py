"""
Database-based Inventory Management Routes
Provides storeroom-wise inventory management with tenant-based access control
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
from database_manager import db_manager
from utils import token_required, require_module_access, require_role

inventory_db_bp = Blueprint('inventory_db', __name__)

@inventory_db_bp.route('/api/inventory-db', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_inventory_items():
    """Get inventory items with storeroom-wise organization and tenant-based filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Get query parameters
        storeroom_id = request.args.get('storeroom_id', type=int)
        category = request.args.get('category')
        low_stock = request.args.get('low_stock', 'false').lower() == 'true'
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        # Base query with storeroom and tenant information
        query = """
            SELECT i.*, 
                   s.name as storeroom_name, 
                   s.storeroom_id as storeroom_code,
                   t.name as tenant_name, 
                   t.site_code,
                   t.is_hub,
                   CASE WHEN i.quantity <= i.reorder_level THEN 1 ELSE 0 END as is_low_stock
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            WHERE 1=1
        """
        params = []
        
        # Apply tenant-based filtering
        if user_role in ['admin', 'hub_admin']:
            # Mayiladuthurai Hub users can see ALL inventory across all storerooms
            pass
        else:
            # Franchise users can only see their own inventory
            query += " AND i.tenant_id = ?"
            params.append(user_tenant_id)
        
        # Filter by active status
        if active_only:
            query += " AND i.is_active = 1"
        
        # Filter by storeroom
        if storeroom_id:
            query += " AND i.storeroom_id = ?"
            params.append(storeroom_id)
        
        # Filter by category
        if category:
            query += " AND i.category = ?"
            params.append(category)
        
        # Filter by low stock
        if low_stock:
            query += " AND i.quantity <= i.reorder_level"
        
        query += " ORDER BY t.name, s.name, i.category, i.name"
        
        inventory_items = db_manager.execute_query(query, tuple(params))
        
        # Group by storeroom for better organization
        storeroom_inventory = {}
        for item in inventory_items:
            storeroom_key = f"{item['tenant_name']} - {item['storeroom_name']}"
            if storeroom_key not in storeroom_inventory:
                storeroom_inventory[storeroom_key] = {
                    'storeroom_id': item['storeroom_id'],
                    'storeroom_name': item['storeroom_name'],
                    'storeroom_code': item['storeroom_code'],
                    'tenant_id': item['tenant_id'],
                    'tenant_name': item['tenant_name'],
                    'site_code': item['site_code'],
                    'is_hub': item['is_hub'],
                    'items': []
                }
            storeroom_inventory[storeroom_key]['items'].append(item)
        
        return jsonify({
            'success': True,
            'data': {
                'storeroom_inventory': storeroom_inventory,
                'total_items': len(inventory_items),
                'low_stock_count': sum(1 for item in inventory_items if item['is_low_stock'])
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/<int:item_id>', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_inventory_item(item_id):
    """Get a specific inventory item with full details"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        query = """
            SELECT i.*, 
                   s.name as storeroom_name, 
                   s.storeroom_id as storeroom_code,
                   t.name as tenant_name, 
                   t.site_code,
                   u.username as created_by_username
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            LEFT JOIN users u ON i.created_by = u.id
            WHERE i.id = ?
        """
        
        items = db_manager.execute_query(query, (item_id,))
        if not items:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404
        
        item = items[0]
        
        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if item['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        # Get recent transactions for this item
        transactions_query = """
            SELECT it.*, u.username as created_by_username
            FROM inventory_transactions it
            LEFT JOIN users u ON it.created_by = u.id
            WHERE it.inventory_id = ?
            ORDER BY it.created_at DESC
            LIMIT 10
        """
        transactions = db_manager.execute_query(transactions_query, (item_id,))
        
        return jsonify({
            'success': True,
            'data': {
                'item': item,
                'recent_transactions': transactions
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db', methods=['POST'])
@token_required
@require_module_access('INVENTORY')
def create_inventory_item():
    """Create a new inventory item"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        
        # Validate required fields
        required_fields = ['name', 'sku', 'category', 'quantity', 'unit', 'reorder_level', 'storeroom_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        # Check if SKU already exists
        existing = db_manager.execute_query(
            "SELECT id FROM inventory WHERE sku = ?", (data['sku'],)
        )
        if existing:
            return jsonify({
                'success': False,
                'error': 'SKU already exists'
            }), 400
        
        # Verify storeroom exists and user has access
        storeroom_query = """
            SELECT s.*, t.name as tenant_name 
            FROM storerooms s 
            LEFT JOIN tenants t ON s.tenant_id = t.id 
            WHERE s.id = ?
        """
        storerooms = db_manager.execute_query(storeroom_query, (data['storeroom_id'],))
        if not storerooms:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404
        
        storeroom = storerooms[0]
        
        # Check tenant access for storeroom
        if user.get('role') not in ['admin', 'hub_admin']:
            if storeroom['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied to this storeroom'
                }), 403
        
        # Create inventory item
        insert_query = """
            INSERT INTO inventory (
                name, sku, category, description, quantity, unit, reorder_level,
                cost_price, selling_price, supplier, location, expiry_date,
                storeroom_id, tenant_id, batch_number, barcode, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            data['name'],
            data['sku'],
            data['category'],
            data.get('description', ''),
            int(data['quantity']),
            data['unit'],
            int(data['reorder_level']),
            float(data.get('cost_price', 0)),
            float(data.get('selling_price', 0)),
            data.get('supplier', ''),
            data.get('location', ''),
            data.get('expiry_date'),
            data['storeroom_id'],
            storeroom['tenant_id'],  # Use storeroom's tenant_id
            data.get('batch_number', ''),
            data.get('barcode', ''),
            user.get('id')
        )
        
        result = db_manager.execute_query(insert_query, params)
        item_id = result
        
        # Create initial inventory transaction
        if int(data['quantity']) > 0:
            transaction_query = """
                INSERT INTO inventory_transactions (
                    inventory_id, transaction_type, quantity, reference_type, 
                    reason, unit_cost, total_cost, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            total_cost = float(data.get('cost_price', 0)) * int(data['quantity'])
            db_manager.execute_query(transaction_query, (
                item_id, 'in', int(data['quantity']), 'manual_adjustment',
                'Initial stock entry', float(data.get('cost_price', 0)), total_cost, user.get('id')
            ))
        
        return jsonify({
            'success': True,
            'message': 'Inventory item created successfully',
            'item_id': item_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/<int:item_id>/transaction', methods=['POST'])
@token_required
@require_module_access('INVENTORY')
def add_inventory_transaction(item_id):
    """Add a stock transaction (in/out/adjustment)"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Validate required fields
        required_fields = ['transaction_type', 'quantity', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        # Get inventory item with access check
        item_query = """
            SELECT i.*, s.tenant_id as storeroom_tenant_id
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            WHERE i.id = ? AND i.is_active = 1
        """
        items = db_manager.execute_query(item_query, (item_id,))
        if not items:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404
        
        item = items[0]
        
        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if item['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        transaction_type = data['transaction_type']
        quantity = int(data['quantity'])
        
        # Validate transaction type
        if transaction_type not in ['in', 'out', 'adjustment']:
            return jsonify({
                'success': False,
                'error': 'Invalid transaction type'
            }), 400
        
        # Check stock availability for 'out' transactions
        if transaction_type == 'out' and item['quantity'] < quantity:
            return jsonify({
                'success': False,
                'error': f'Insufficient stock. Available: {item["quantity"]}, Requested: {quantity}'
            }), 400
        
        # Calculate new quantity
        if transaction_type == 'in':
            new_quantity = item['quantity'] + quantity
        elif transaction_type == 'out':
            new_quantity = item['quantity'] - quantity
        else:  # adjustment
            new_quantity = quantity
        
        # Update inventory quantity
        update_query = "UPDATE inventory SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        db_manager.execute_query(update_query, (new_quantity, item_id))
        
        # Create transaction record
        transaction_query = """
            INSERT INTO inventory_transactions (
                inventory_id, transaction_type, quantity, reference_type, reference_id,
                reason, notes, unit_cost, total_cost, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        unit_cost = float(data.get('unit_cost', item.get('cost_price', 0)))
        total_cost = unit_cost * quantity
        
        db_manager.execute_query(transaction_query, (
            item_id,
            transaction_type,
            quantity,
            data.get('reference_type', 'manual_adjustment'),
            data.get('reference_id'),
            data['reason'],
            data.get('notes', ''),
            unit_cost,
            total_cost,
            user.get('id')
        ))
        
        return jsonify({
            'success': True,
            'message': 'Transaction completed successfully',
            'new_quantity': new_quantity,
            'previous_quantity': item['quantity']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ============================================================================
# INVENTORY INTEGRATION FOR DELIVERY NOTES
# ============================================================================

@inventory_db_bp.route('/api/inventory-db/storerooms', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_storerooms():
    """Get all storerooms for inventory selection"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Get query parameters
        active_only = request.args.get('active_only', 'true').lower() == 'true'

        # Build query for storerooms
        query = "SELECT * FROM storerooms WHERE 1=1"
        params = []

        # Filter by tenant access
        if user_role not in ['admin', 'hub_admin']:
            query += " AND tenant_id = ?"
            params.append(user_tenant_id)

        # Filter by active status
        if active_only:
            query += " AND status = 'active'"

        query += " ORDER BY name ASC"

        storerooms = db_manager.execute_query(query, params)

        # Format storerooms
        formatted_storerooms = []
        for storeroom in storerooms:
            formatted_storerooms.append({
                'id': storeroom['id'],
                'storeroom_id': storeroom['storeroom_id'],
                'name': storeroom['name'],
                'description': storeroom['description'],
                'tenant_id': storeroom['tenant_id'],
                'location': storeroom['location_details'],
                'status': storeroom['status'],
                'is_active': storeroom['status'] == 'active'
            })

        return jsonify({
            'success': True,
            'data': {
                'storerooms': formatted_storerooms
            }
        })

    except Exception as e:
        import traceback
        print(f"Error in get_storerooms: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/storeroom/<int:storeroom_id>/items', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_inventory_items_by_storeroom(storeroom_id):
    """Get inventory items for a specific storeroom for delivery note creation"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Check storeroom access
        storeroom_query = "SELECT * FROM storerooms WHERE id = ?"
        storeroom_result = db_manager.execute_query(storeroom_query, (storeroom_id,))

        if not storeroom_result:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404

        storeroom = storeroom_result[0]

        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if storeroom['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied to this storeroom'
                }), 403

        # Get query parameters
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        in_stock_only = request.args.get('in_stock_only', 'false').lower() == 'true'
        search = request.args.get('search', '').strip()

        # Build query for inventory items
        query = """
            SELECT i.*,
                   s.name as storeroom_name,
                   t.name as tenant_name,
                   t.site_code,
                   CASE WHEN i.quantity <= i.reorder_level THEN 1 ELSE 0 END as is_low_stock
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            WHERE i.storeroom_id = ?
        """
        params = [storeroom_id]

        # Filter by active status
        if active_only:
            query += " AND i.is_active = 1"

        # Filter by stock availability
        if in_stock_only:
            query += " AND i.quantity > 0"

        # Search filter
        if search:
            query += " AND (i.name LIKE ? OR i.sku LIKE ? OR i.description LIKE ? OR i.category LIKE ?)"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param])

        query += " ORDER BY i.name ASC"

        items = db_manager.execute_query(query, params)

        # Format items for delivery note selection
        formatted_items = []
        for item in items:
            formatted_items.append({
                'id': item['id'],
                'name': item['name'],
                'sku': item['sku'],
                'description': item['description'],
                'category': item['category'],
                'unit': item['unit'],
                'quantity': item['quantity'],
                'available_stock': item['quantity'],
                'cost_price': item['cost_price'],
                'selling_price': item['selling_price'],
                'reorder_level': item['reorder_level'],
                'is_low_stock': bool(item['is_low_stock']),
                'batch_number': item.get('batch_number'),
                'expiry_date': item.get('expiry_date'),
                'storeroom_name': item['storeroom_name'],
                'tenant_name': item['tenant_name'],
                'site_code': item['site_code'],
                'display_name': f"{item['name']} ({item['sku']}) - Stock: {item['quantity']} {item['unit']}"
            })

        return jsonify({
            'success': True,
            'data': {
                'storeroom': {
                    'id': storeroom['id'],
                    'name': storeroom['name'],
                    'storeroom_id': storeroom['storeroom_id'],
                    'tenant_id': storeroom['tenant_id']
                },
                'items': formatted_items,
                'total_items': len(formatted_items)
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/items/<int:item_id>/validate-stock', methods=['POST'])
@token_required
@require_module_access('INVENTORY')
def validate_item_stock(item_id):
    """Validate stock availability for delivery note item"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Validate required fields
        if 'requested_quantity' not in data:
            return jsonify({
                'success': False,
                'error': 'Requested quantity is required'
            }), 400

        requested_quantity = int(data['requested_quantity'])

        # Get inventory item
        item_query = """
            SELECT i.*, s.name as storeroom_name, t.name as tenant_name
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            WHERE i.id = ?
        """
        item_result = db_manager.execute_query(item_query, (item_id,))

        if not item_result:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404

        item = item_result[0]

        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if item['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied to this inventory item'
                }), 403

        # Validate stock availability
        available_stock = item['quantity']
        is_valid = requested_quantity <= available_stock

        validation_result = {
            'is_valid': is_valid,
            'available_stock': available_stock,
            'requested_quantity': requested_quantity,
            'item': {
                'id': item['id'],
                'name': item['name'],
                'sku': item['sku'],
                'unit': item['unit'],
                'cost_price': item['cost_price'],
                'selling_price': item['selling_price']
            }
        }

        if not is_valid:
            validation_result['warning'] = f"Insufficient stock. Available: {available_stock}, Requested: {requested_quantity}"

        return jsonify({
            'success': True,
            'data': validation_result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
