import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Form, But<PERSON>, Alert, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTruck, 
  faArrowLeft, 
  faPlus, 
  faTimes,
  faExclamationTriangle,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { tenantAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import StoreroomSelector from '../../components/common/StoreroomSelector';
import InventoryItemSelector from '../../components/procurement/InventoryItemSelector';

const DeliveryNoteCreate = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [formData, setFormData] = useState({
    to_tenant_id: '',
    delivery_type: 'hub_to_franchise',
    delivery_date: new Date().toISOString().split('T')[0],
    expected_delivery_date: '',
    delivery_address: '',
    notes: '',
    storeroom_id: '',
    mode_of_transport: 'Road Transport',
    items: []
  });

  // Transport mode options for India
  const transportModes = [
    'Road Transport (Truck/Tempo)',
    'Rail Transport',
    'Air Cargo',
    'Sea Freight',
    'Courier Service',
    'Own Vehicle',
    'Third Party Logistics'
  ];
  
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validated, setValidated] = useState(false);
  const [tenantsLoading, setTenantsLoading] = useState(true);

  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    try {
      setTenantsLoading(true);
      const response = await tenantAPI.getAllTenants();
      setTenants(response.data || []);
    } catch (err) {
      console.error('Error loading tenants:', err);
      setError('Failed to load tenants');
    } finally {
      setTenantsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));
  };





  const validateForm = () => {
    const errors = [];
    
    if (!formData.to_tenant_id) {
      errors.push('Please select a destination tenant');
    }
    
    if (!formData.delivery_type) {
      errors.push('Please select a delivery type');
    }
    
    if (formData.items.length === 0) {
      errors.push('Please add at least one item');
    }
    
    for (let i = 0; i < formData.items.length; i++) {
      const item = formData.items[i];
      if (!item.item_name) {
        errors.push(`Item ${i + 1}: Item name is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Item ${i + 1}: Quantity must be greater than 0`);
      }
      if (!item.unit) {
        errors.push(`Item ${i + 1}: Unit is required`);
      }
      if (item.unit_price < 0) {
        errors.push(`Item ${i + 1}: Unit price cannot be negative`);
      }
    }
    
    if (errors.length > 0) {
      setError(errors.join(', '));
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setValidated(true);

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Prepare data for submission
      const submitData = {
        ...formData,
        to_tenant_id: parseInt(formData.to_tenant_id),
        storeroom_id: formData.storeroom_id ? parseInt(formData.storeroom_id) : null,
        items: formData.items.map(item => ({
          inventory_item_id: item.inventory_item_id || null,
          item_name: item.item_name,
          description: item.description || '',
          sku: item.sku || '',
          category: item.category || '',
          requested_quantity: parseInt(item.requested_quantity || item.quantity),
          quantity: parseInt(item.requested_quantity || item.quantity), // For backward compatibility
          unit: item.unit,
          unit_price: parseFloat(item.unit_price) || 0,
          batch_number: item.batch_number || '',
          expiry_date: item.expiry_date || null,
          notes: item.notes || ''
        }))
      };

      const response = await procurementAPI.createDeliveryNote(submitData);
      
      // Navigate to the created delivery note
      if (response.data.delivery_note?.id) {
        navigate(`/procurement/deliveries/${response.data.delivery_note.id}`);
      } else {
        navigate('/procurement/deliveries');
      }
    } catch (err) {
      console.error('Error creating delivery note:', err);
      setError(err.response?.data?.error || err.response?.data?.message || 'Failed to create delivery note');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      const quantity = parseInt(item.requested_quantity || item.quantity) || 0;
      const price = parseFloat(item.unit_price) || 0;
      return total + (price * quantity);
    }, 0);
  };

  if (!hasModuleAccess('PROCUREMENT')) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          You don't have permission to access this module.
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/procurement/deliveries')}
            className="mb-2"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Delivery Notes
          </Button>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
            Create Delivery Note
          </h2>
          <p className="text-muted mb-0">Create a new delivery note for items</p>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Row>
          <Col lg={8}>
            {/* Basic Information */}
            <Card className="mb-4">
              <Card.Header className="text-primary">
                <h5 className="mb-0">
                  <FontAwesomeIcon icon={faTruck} className="me-2" />
                  Delivery Information
                </h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Destination Tenant *</Form.Label>
                      {tenantsLoading ? (
                        <div className="text-center py-2">
                          <Spinner animation="border" size="sm" />
                        </div>
                      ) : (
                        <Form.Select
                          name="to_tenant_id"
                          value={formData.to_tenant_id}
                          onChange={handleInputChange}
                          required
                        >
                          <option value="">Select destination tenant...</option>
                          {tenants.map(tenant => (
                            <option key={tenant.id} value={tenant.id}>
                              {tenant.name} ({tenant.site_code})
                            </option>
                          ))}
                        </Form.Select>
                      )}
                      <Form.Control.Feedback type="invalid">
                        Please select a destination tenant.
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Delivery Type *</Form.Label>
                      <Form.Select
                        name="delivery_type"
                        value={formData.delivery_type}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="hub_to_franchise">Hub to Franchise</option>
                        <option value="supplier_to_hub">Supplier to Hub</option>
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        Please select a delivery type.
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Mode of Transport *</Form.Label>
                      <Form.Select
                        name="mode_of_transport"
                        value={formData.mode_of_transport}
                        onChange={handleInputChange}
                        required
                      >
                        {transportModes.map(mode => (
                          <option key={mode} value={mode}>
                            {mode}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        Please select a mode of transport.
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Expected Delivery Date</Form.Label>
                      <Form.Control
                        type="date"
                        name="expected_delivery_date"
                        value={formData.expected_delivery_date}
                        onChange={handleInputChange}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={12}>
                    <StoreroomSelector
                      value={formData.storeroom_id}
                      onChange={(value) => setFormData(prev => ({ ...prev, storeroom_id: value, items: [] }))}
                      label="Source Storeroom"
                      required={true}
                      placeholder="Select storeroom for inventory items..."
                      className="mb-3"
                    />
                    <Form.Text className="text-muted">
                      Select the storeroom from which items will be dispatched. This will enable inventory-based item selection.
                    </Form.Text>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Delivery Date</Form.Label>
                      <Form.Control
                        type="date"
                        name="delivery_date"
                        value={formData.delivery_date}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Expected Delivery Date</Form.Label>
                      <Form.Control
                        type="date"
                        name="expected_delivery_date"
                        value={formData.expected_delivery_date}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>Delivery Address</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={2}
                        name="delivery_address"
                        value={formData.delivery_address}
                        onChange={handleInputChange}
                        placeholder="Enter delivery address..."
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>Notes</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={2}
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        placeholder="Additional notes..."
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Items */}
            <Card className="mb-4">
              <Card.Header className="text-primary">
                <h5 className="mb-0">
                  <FontAwesomeIcon icon={faPlus} className="me-2" />
                  Items
                </h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <InventoryItemSelector
                  storeroomId={formData.storeroom_id}
                  selectedItems={formData.items}
                  onItemsChange={(items) => setFormData(prev => ({ ...prev, items }))}
                  disabled={loading}
                />
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4}>
            {/* Summary */}
            <Card className="mb-4">
              <Card.Header className="text-primary">
                <h5 className="mb-0">Summary</h5>
              </Card.Header>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <div className="d-flex justify-content-between mb-2">
                  <span>Total Items:</span>
                  <span className="fw-semibold">{formData.items.length}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Total Quantity:</span>
                  <span className="fw-semibold">
                    {formData.items.reduce((sum, item) => sum + (parseInt(item.requested_quantity || item.quantity) || 0), 0)}
                  </span>
                </div>
                <hr />
                <div className="d-flex justify-content-between">
                  <span className="fw-bold">Total Amount:</span>
                  <span className="fw-bold text-success">
                    ₹{calculateTotal().toFixed(2)}
                  </span>
                </div>
              </Card.Body>
            </Card>

            {/* Actions */}
            <Card>
              <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
                <div className="d-grid gap-2">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faCheck} className="me-2" />
                        Create Delivery Note
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline-secondary"
                    onClick={() => navigate('/procurement/deliveries')}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Form>
    </Container>
  );
};

export default DeliveryNoteCreate;
