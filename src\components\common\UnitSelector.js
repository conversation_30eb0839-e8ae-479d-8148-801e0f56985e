import React, { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import unitsAPI from '../../services/unitsAPI';

const UnitSelector = ({ 
  value, 
  onChange, 
  name = 'unit',
  label = 'Unit',
  required = false,
  disabled = false,
  size = 'md',
  className = '',
  placeholder = 'Select unit...',
  category = null, // Filter by category if specified
  showAllOption = false,
  allOptionText = 'All Units'
}) => {
  const [units, setUnits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadUnits();
  }, [category]);

  const loadUnits = async () => {
    try {
      setLoading(true);
      const params = { active_only: true };
      if (category) {
        params.category = category;
      }
      
      const response = await unitsAPI.getUnits(params);
      setUnits(response.data.data || []);
    } catch (err) {
      console.error('Error loading units:', err);
      setError('Failed to load units');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const selectedValue = e.target.value;
    if (onChange) {
      onChange(e);
    }
  };

  if (error) {
    return (
      <Form.Group className={className}>
        {label && <Form.Label>{label}</Form.Label>}
        <Form.Select disabled>
          <option>Error loading units</option>
        </Form.Select>
      </Form.Group>
    );
  }

  return (
    <Form.Group className={className}>
      {label && (
        <Form.Label>
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </Form.Label>
      )}
      <Form.Select
        name={name}
        value={value || ''}
        onChange={handleChange}
        required={required}
        disabled={disabled || loading}
        size={size}
      >
        <option value="">
          {loading ? 'Loading units...' : placeholder}
        </option>
        
        {showAllOption && (
          <option value="all">{allOptionText}</option>
        )}
        
        {units.map(unit => (
          <option key={unit.id} value={unit.code}>
            {unit.name} ({unit.code})
          </option>
        ))}
      </Form.Select>
    </Form.Group>
  );
};

export default UnitSelector;
