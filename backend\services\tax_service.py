"""
Tax Service for AVINI Labs Accounting Module
Handles tax calculations, GST, VAT, TDS, TCS and other tax types
"""

from datetime import datetime
from database_manager import db_manager
import logging

logger = logging.getLogger(__name__)

class TaxService:
    def __init__(self):
        self.db_manager = db_manager
    
    def get_tax_types(self, tenant_id=None):
        """Get all active tax types"""
        try:
            query = "SELECT * FROM tax_types WHERE is_active = 1"
            params = []
            
            if tenant_id:
                query += " AND (tenant_id = ? OR tenant_id IS NULL)"
                params.append(tenant_id)
            
            query += " ORDER BY tax_name"
            
            return self.db_manager.execute_query(query, params)
        except Exception as e:
            logger.error(f"Error fetching tax types: {str(e)}")
            raise
    
    def get_tax_type_by_code(self, tax_code, tenant_id=None):
        """Get tax type by code"""
        try:
            query = "SELECT * FROM tax_types WHERE tax_code = ? AND is_active = 1"
            params = [tax_code]
            
            if tenant_id:
                query += " AND (tenant_id = ? OR tenant_id IS NULL)"
                params.append(tenant_id)
            
            result = self.db_manager.execute_query(query, params)
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error fetching tax type {tax_code}: {str(e)}")
            raise
    
    def calculate_gst(self, amount, gst_rate=18, include_tax=False):
        """Calculate GST (CGST + SGST or IGST)"""
        try:
            if include_tax:
                # Amount includes tax, extract base amount
                base_amount = amount / (1 + (gst_rate / 100))
                tax_amount = amount - base_amount
            else:
                # Amount is base, calculate tax
                base_amount = amount
                tax_amount = amount * (gst_rate / 100)
            
            # For intra-state: CGST + SGST (each half of total GST)
            # For inter-state: IGST (full GST)
            cgst = sgst = tax_amount / 2
            igst = tax_amount
            
            return {
                'base_amount': round(base_amount, 2),
                'tax_rate': gst_rate,
                'total_tax': round(tax_amount, 2),
                'cgst': round(cgst, 2),
                'sgst': round(sgst, 2),
                'igst': round(igst, 2),
                'total_amount': round(base_amount + tax_amount, 2)
            }
        except Exception as e:
            logger.error(f"Error calculating GST: {str(e)}")
            raise
    
    def calculate_tds(self, amount, tds_rate=10, threshold=None):
        """Calculate TDS (Tax Deducted at Source)"""
        try:
            if threshold and amount < threshold:
                return {
                    'base_amount': amount,
                    'tds_rate': tds_rate,
                    'tds_amount': 0,
                    'net_amount': amount,
                    'threshold_applicable': True
                }
            
            tds_amount = amount * (tds_rate / 100)
            net_amount = amount - tds_amount
            
            return {
                'base_amount': round(amount, 2),
                'tds_rate': tds_rate,
                'tds_amount': round(tds_amount, 2),
                'net_amount': round(net_amount, 2),
                'threshold_applicable': False
            }
        except Exception as e:
            logger.error(f"Error calculating TDS: {str(e)}")
            raise
    
    def calculate_tcs(self, amount, tcs_rate=1, threshold=None):
        """Calculate TCS (Tax Collected at Source)"""
        try:
            if threshold and amount < threshold:
                return {
                    'base_amount': amount,
                    'tcs_rate': tcs_rate,
                    'tcs_amount': 0,
                    'total_amount': amount,
                    'threshold_applicable': True
                }
            
            tcs_amount = amount * (tcs_rate / 100)
            total_amount = amount + tcs_amount
            
            return {
                'base_amount': round(amount, 2),
                'tcs_rate': tcs_rate,
                'tcs_amount': round(tcs_amount, 2),
                'total_amount': round(total_amount, 2),
                'threshold_applicable': False
            }
        except Exception as e:
            logger.error(f"Error calculating TCS: {str(e)}")
            raise
    
    def calculate_composite_tax(self, amount, tax_config):
        """Calculate multiple taxes on an amount"""
        try:
            result = {
                'base_amount': amount,
                'taxes': [],
                'total_tax_amount': 0,
                'final_amount': amount
            }
            
            current_amount = amount
            
            for tax in tax_config:
                tax_type = tax.get('type')
                tax_rate = tax.get('rate', 0)
                
                if tax_type == 'GST':
                    gst_calc = self.calculate_gst(current_amount, tax_rate)
                    tax_amount = gst_calc['total_tax']
                    result['taxes'].append({
                        'type': 'GST',
                        'rate': tax_rate,
                        'amount': tax_amount,
                        'details': gst_calc
                    })
                    current_amount += tax_amount
                
                elif tax_type == 'TDS':
                    tds_calc = self.calculate_tds(current_amount, tax_rate)
                    tax_amount = -tds_calc['tds_amount']  # Negative because it's deducted
                    result['taxes'].append({
                        'type': 'TDS',
                        'rate': tax_rate,
                        'amount': tax_amount,
                        'details': tds_calc
                    })
                    current_amount += tax_amount
                
                elif tax_type == 'TCS':
                    tcs_calc = self.calculate_tcs(current_amount, tax_rate)
                    tax_amount = tcs_calc['tcs_amount']
                    result['taxes'].append({
                        'type': 'TCS',
                        'rate': tax_rate,
                        'amount': tax_amount,
                        'details': tcs_calc
                    })
                    current_amount += tax_amount
                
                result['total_tax_amount'] += tax_amount
            
            result['final_amount'] = round(current_amount, 2)
            result['total_tax_amount'] = round(result['total_tax_amount'], 2)
            
            return result
        except Exception as e:
            logger.error(f"Error calculating composite tax: {str(e)}")
            raise
    
    def create_tax_journal_entries(self, transaction_data, tax_calculation, user_id):
        """Create journal entries for tax transactions"""
        try:
            from services.accounting_service import AccountingService
            accounting_service = AccountingService()
            
            journal_entries = []
            
            # Get tax accounts
            tax_accounts = self.db_manager.execute_query(
                "SELECT * FROM tax_accounts WHERE tenant_id = ?",
                [transaction_data.get('tenant_id', 1)]
            )
            
            tax_account_map = {acc['tax_type']: acc for acc in tax_accounts}
            
            for tax in tax_calculation.get('taxes', []):
                tax_type = tax['type']
                tax_amount = abs(tax['amount'])
                
                if tax_amount > 0 and tax_type in tax_account_map:
                    tax_account = tax_account_map[tax_type]
                    
                    # Create journal entry for tax
                    journal_data = {
                        'journal_number': f"TAX-{datetime.now().strftime('%Y%m%d')}-{transaction_data.get('reference_id', '')}",
                        'journal_date': datetime.now().date().isoformat(),
                        'description': f"{tax_type} for {transaction_data.get('description', 'Transaction')}",
                        'reference_type': transaction_data.get('reference_type'),
                        'reference_id': transaction_data.get('reference_id'),
                        'tenant_id': transaction_data.get('tenant_id', 1),
                        'created_by': user_id
                    }
                    
                    journal_lines = []
                    
                    if tax_type in ['GST', 'TCS']:
                        # Tax collected/payable (Credit)
                        journal_lines.append({
                            'account_id': tax_account['tax_payable_account_id'],
                            'debit_amount': 0,
                            'credit_amount': tax_amount,
                            'description': f"{tax_type} Payable"
                        })
                        
                        # Expense/Revenue account (Debit for TCS, varies for GST)
                        journal_lines.append({
                            'account_id': transaction_data.get('contra_account_id'),
                            'debit_amount': tax_amount,
                            'credit_amount': 0,
                            'description': f"{tax_type} on transaction"
                        })
                    
                    elif tax_type == 'TDS':
                        # TDS Receivable (Debit)
                        journal_lines.append({
                            'account_id': tax_account['tax_receivable_account_id'],
                            'debit_amount': tax_amount,
                            'credit_amount': 0,
                            'description': f"{tax_type} Receivable"
                        })
                        
                        # Expense reduction (Credit)
                        journal_lines.append({
                            'account_id': transaction_data.get('contra_account_id'),
                            'debit_amount': 0,
                            'credit_amount': tax_amount,
                            'description': f"{tax_type} deducted"
                        })
                    
                    if journal_lines:
                        journal_id = accounting_service.create_journal_entry(journal_data, journal_lines)
                        accounting_service.post_journal_entry(journal_id, user_id)
                        journal_entries.append(journal_id)
            
            return journal_entries
        except Exception as e:
            logger.error(f"Error creating tax journal entries: {str(e)}")
            raise
    
    def get_tax_summary_report(self, tenant_id, start_date, end_date):
        """Generate tax summary report"""
        try:
            query = """
                SELECT 
                    tt.tax_name,
                    tt.tax_code,
                    SUM(CASE WHEN jel.credit_amount > 0 THEN jel.credit_amount ELSE 0 END) as tax_collected,
                    SUM(CASE WHEN jel.debit_amount > 0 THEN jel.debit_amount ELSE 0 END) as tax_paid,
                    COUNT(DISTINCT je.id) as transaction_count
                FROM journal_entries je
                JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                JOIN tax_accounts ta ON (coa.id = ta.tax_payable_account_id OR coa.id = ta.tax_receivable_account_id)
                JOIN tax_types tt ON ta.tax_type = tt.tax_code
                WHERE je.tenant_id = ?
                AND je.transaction_date BETWEEN ? AND ?
                AND je.status = 'POSTED'
                GROUP BY tt.tax_name, tt.tax_code
                ORDER BY tt.tax_name
            """
            
            return self.db_manager.execute_query(query, [tenant_id, start_date, end_date])
        except Exception as e:
            logger.error(f"Error generating tax summary report: {str(e)}")
            raise

    # ============================================================================
    # GST COMPLIANCE REPORTS
    # ============================================================================

    def generate_gstr1_report(self, tenant_id: int, month: int, year: int) -> dict:
        """Generate GSTR-1 report for outward supplies"""
        try:
            start_date = f"{year}-{month:02d}-01"
            if month == 12:
                end_date = f"{year + 1}-01-01"
            else:
                end_date = f"{year}-{month + 1:02d}-01"

            # B2B Supplies (Business to Business)
            # First check if we have the state_code column, if not use state
            try:
                b2b_query = """
                    SELECT
                        c.gst_number as gstin,
                        c.customer_name,
                        c.state_code,
                        si.invoice_number,
                        si.invoice_date,
                        si.total_amount,
                        si.tax_amount,
                        si.cgst_amount,
                        si.sgst_amount,
                        si.igst_amount,
                        si.gst_rate as tax_rate
                    FROM sales_invoices si
                    JOIN customers c ON si.customer_id = c.id
                    WHERE si.tenant_id = ?
                        AND si.invoice_date >= ?
                        AND si.invoice_date < ?
                        AND c.gst_number IS NOT NULL
                        AND c.gst_number != ''
                        AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                    ORDER BY si.invoice_date, si.invoice_number
                """

                b2b_supplies = self.db_manager.execute_query(b2b_query, [tenant_id, start_date, end_date])
            except Exception as e:
                if "no such column: c.state_code" in str(e):
                    # Fallback to using state column instead
                    b2b_query = """
                        SELECT
                            c.gst_number as gstin,
                            c.customer_name,
                            c.state as state_code,
                            si.invoice_number,
                            si.invoice_date,
                            si.total_amount,
                            si.tax_amount,
                            si.cgst_amount,
                            si.sgst_amount,
                            si.igst_amount,
                            si.gst_rate as tax_rate
                        FROM sales_invoices si
                        JOIN customers c ON si.customer_id = c.id
                        WHERE si.tenant_id = ?
                            AND si.invoice_date >= ?
                            AND si.invoice_date < ?
                            AND c.gst_number IS NOT NULL
                            AND c.gst_number != ''
                            AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                        ORDER BY si.invoice_date, si.invoice_number
                    """

                    b2b_supplies = self.db_manager.execute_query(b2b_query, [tenant_id, start_date, end_date])
                else:
                    raise e

            # B2C Supplies (Business to Consumer) - Aggregated by state and tax rate
            try:
                b2c_query = """
                    SELECT
                        COALESCE(c.state_code, 'UNKNOWN') as state_code,
                        si.gst_rate as tax_rate,
                        SUM(si.total_amount) as total_amount,
                        SUM(si.tax_amount) as total_tax,
                        SUM(si.cgst_amount) as total_cgst,
                        SUM(si.sgst_amount) as total_sgst,
                        SUM(si.igst_amount) as total_igst,
                        COUNT(*) as invoice_count
                    FROM sales_invoices si
                    LEFT JOIN customers c ON si.customer_id = c.id
                    WHERE si.tenant_id = ?
                        AND si.invoice_date >= ?
                        AND si.invoice_date < ?
                        AND (c.gst_number IS NULL OR c.gst_number = '')
                        AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                    GROUP BY COALESCE(c.state_code, 'UNKNOWN'), si.gst_rate
                    ORDER BY state_code, si.gst_rate
                """

                b2c_supplies = self.db_manager.execute_query(b2c_query, [tenant_id, start_date, end_date])
            except Exception as e:
                if "no such column: c.state_code" in str(e):
                    # Fallback to using state column instead
                    b2c_query = """
                        SELECT
                            COALESCE(c.state, 'UNKNOWN') as state_code,
                            si.gst_rate as tax_rate,
                            SUM(si.total_amount) as total_amount,
                            SUM(si.tax_amount) as total_tax,
                            SUM(si.cgst_amount) as total_cgst,
                            SUM(si.sgst_amount) as total_sgst,
                            SUM(si.igst_amount) as total_igst,
                            COUNT(*) as invoice_count
                        FROM sales_invoices si
                        LEFT JOIN customers c ON si.customer_id = c.id
                        WHERE si.tenant_id = ?
                            AND si.invoice_date >= ?
                            AND si.invoice_date < ?
                            AND (c.gst_number IS NULL OR c.gst_number = '')
                            AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                        GROUP BY COALESCE(c.state, 'UNKNOWN'), si.gst_rate
                        ORDER BY state_code, si.gst_rate
                    """

                    b2c_supplies = self.db_manager.execute_query(b2c_query, [tenant_id, start_date, end_date])
                else:
                    raise e

            # HSN Summary
            hsn_query = """
                SELECT
                    COALESCE(sil.hsn_code, 'UNKNOWN') as hsn_code,
                    sil.tax_rate,
                    SUM(sil.quantity) as total_quantity,
                    SUM(sil.amount) as total_amount,
                    SUM(sil.tax_amount) as total_tax
                FROM sales_invoice_lines sil
                JOIN sales_invoices si ON sil.sales_invoice_id = si.id
                WHERE si.tenant_id = ?
                    AND si.invoice_date >= ?
                    AND si.invoice_date < ?
                    AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                GROUP BY COALESCE(sil.hsn_code, 'UNKNOWN'), sil.tax_rate
                ORDER BY hsn_code, sil.tax_rate
            """

            hsn_summary = self.db_manager.execute_query(hsn_query, [tenant_id, start_date, end_date])

            # Calculate totals
            total_b2b_amount = sum(float(item['total_amount']) for item in b2b_supplies)
            total_b2c_amount = sum(float(item['total_amount']) for item in b2c_supplies)
            total_tax_amount = sum(float(item['tax_amount']) for item in b2b_supplies) + sum(float(item['total_tax']) for item in b2c_supplies)

            return {
                'report_type': 'GSTR-1',
                'period': {'month': month, 'year': year},
                'summary': {
                    'total_b2b_amount': total_b2b_amount,
                    'total_b2c_amount': total_b2c_amount,
                    'total_amount': total_b2b_amount + total_b2c_amount,
                    'total_tax_amount': total_tax_amount,
                    'b2b_invoice_count': len(b2b_supplies),
                    'b2c_invoice_count': sum(int(item['invoice_count']) for item in b2c_supplies)
                },
                'b2b_supplies': b2b_supplies,
                'b2c_supplies': b2c_supplies,
                'hsn_summary': hsn_summary,
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating GSTR-1 report: {str(e)}")
            raise

    def generate_gstr3b_report(self, tenant_id: int, month: int, year: int) -> dict:
        """Generate GSTR-3B report for monthly return"""
        try:
            start_date = f"{year}-{month:02d}-01"
            if month == 12:
                end_date = f"{year + 1}-01-01"
            else:
                end_date = f"{year}-{month + 1:02d}-01"

            # Outward Supplies (from GSTR-1 data)
            gstr1_data = self.generate_gstr1_report(tenant_id, month, year)

            # Inward Supplies (Purchases with GST)
            inward_query = """
                SELECT
                    v.gst_number as gstin,
                    pi.invoice_number,
                    pi.invoice_date,
                    pi.total_amount,
                    pi.tax_amount,
                    pi.cgst_amount,
                    pi.sgst_amount,
                    pi.igst_amount,
                    pi.tax_rate
                FROM purchase_invoices pi
                JOIN vendors v ON pi.vendor_id = v.id
                WHERE pi.tenant_id = ?
                    AND pi.invoice_date >= ?
                    AND pi.invoice_date < ?
                    AND pi.status IN ('RECEIVED', 'PAID', 'PARTIALLY_PAID')
                ORDER BY pi.invoice_date
            """

            inward_supplies = self.db_manager.execute_query(inward_query, [tenant_id, start_date, end_date])

            # Calculate Input Tax Credit (ITC)
            total_itc = sum(float(item['tax_amount']) for item in inward_supplies)

            # Tax Liability calculation
            output_tax = gstr1_data['summary']['total_tax_amount']
            net_tax_liability = max(0, output_tax - total_itc)

            # Interest and Late Fee (if any)
            interest_late_fee_query = """
                SELECT COALESCE(SUM(amount), 0) as total_interest
                FROM tax_payments
                WHERE tenant_id = ?
                    AND payment_date >= ?
                    AND payment_date < ?
                    AND payment_type IN ('INTEREST', 'LATE_FEE')
            """

            interest_result = self.db_manager.execute_query(interest_late_fee_query, [tenant_id, start_date, end_date])
            interest_amount = float(interest_result[0]['total_interest']) if interest_result else 0

            return {
                'report_type': 'GSTR-3B',
                'period': {'month': month, 'year': year},
                'outward_supplies': {
                    'taxable_value': gstr1_data['summary']['total_amount'] - gstr1_data['summary']['total_tax_amount'],
                    'tax_amount': gstr1_data['summary']['total_tax_amount']
                },
                'inward_supplies': {
                    'taxable_value': sum(float(item['total_amount']) - float(item['tax_amount']) for item in inward_supplies),
                    'tax_amount': total_itc
                },
                'itc_details': {
                    'itc_available': total_itc,
                    'itc_utilized': min(total_itc, output_tax)
                },
                'tax_liability': {
                    'output_tax': output_tax,
                    'input_tax_credit': total_itc,
                    'net_tax_liability': net_tax_liability,
                    'interest_late_fee': interest_amount,
                    'total_tax_payable': net_tax_liability + interest_amount
                },
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating GSTR-3B report: {str(e)}")
            raise

    def get_gst_dashboard(self, tenant_id: int, start_date: str = None, end_date: str = None) -> dict:
        """Get GST dashboard data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=30)).isoformat()

            # GST Collection Summary - with fallback for missing columns
            try:
                collection_query = """
                    SELECT
                        COALESCE(SUM(cgst_amount), 0) as total_cgst,
                        COALESCE(SUM(sgst_amount), 0) as total_sgst,
                        COALESCE(SUM(igst_amount), 0) as total_igst,
                        COALESCE(SUM(tax_amount), 0) as total_gst,
                        COUNT(*) as invoice_count
                    FROM sales_invoices
                    WHERE tenant_id = ?
                        AND invoice_date BETWEEN ? AND ?
                        AND status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                """

                collection_data = self.db_manager.execute_query(collection_query, [tenant_id, start_date, end_date])
                collection = collection_data[0] if collection_data else {}
            except Exception as e:
                if "no such column" in str(e).lower():
                    # Fallback: Use basic invoice data without GST breakdown
                    collection_query = """
                        SELECT
                            0 as total_cgst,
                            0 as total_sgst,
                            0 as total_igst,
                            COALESCE(SUM(total_amount * 0.18), 0) as total_gst,
                            COUNT(*) as invoice_count
                        FROM sales_invoices
                        WHERE tenant_id = ?
                            AND invoice_date BETWEEN ? AND ?
                            AND status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                    """

                    collection_data = self.db_manager.execute_query(collection_query, [tenant_id, start_date, end_date])
                    collection = collection_data[0] if collection_data else {
                        'total_cgst': 0,
                        'total_sgst': 0,
                        'total_igst': 0,
                        'total_gst': 0,
                        'invoice_count': 0
                    }
                else:
                    raise e

            # GST Payment Summary - with fallback for missing columns
            try:
                payment_query = """
                    SELECT
                        COALESCE(SUM(cgst_amount), 0) as paid_cgst,
                        COALESCE(SUM(sgst_amount), 0) as paid_sgst,
                        COALESCE(SUM(igst_amount), 0) as paid_igst,
                        COALESCE(SUM(tax_amount), 0) as paid_gst,
                        COUNT(*) as payment_count
                    FROM purchase_invoices
                    WHERE tenant_id = ?
                        AND invoice_date BETWEEN ? AND ?
                        AND status IN ('RECEIVED', 'PAID', 'PARTIALLY_PAID')
                """

                payment_data = self.db_manager.execute_query(payment_query, [tenant_id, start_date, end_date])
                payment = payment_data[0] if payment_data else {}
            except Exception as e:
                if "no such column" in str(e).lower() or "no such table" in str(e).lower():
                    # Fallback: Return zero values if table/columns don't exist
                    payment = {
                        'paid_cgst': 0,
                        'paid_sgst': 0,
                        'paid_igst': 0,
                        'paid_gst': 0,
                        'payment_count': 0
                    }
                else:
                    raise e

            # Recent transactions - simplified to avoid missing column errors
            try:
                recent_query = """
                    SELECT
                        'SALES' as type,
                        invoice_number as reference,
                        invoice_date as date,
                        total_amount,
                        COALESCE(tax_amount, 0) as tax_amount,
                        COALESCE(cgst_amount, 0) as cgst_amount,
                        COALESCE(sgst_amount, 0) as sgst_amount,
                        COALESCE(igst_amount, 0) as igst_amount
                    FROM sales_invoices
                    WHERE tenant_id = ?
                        AND invoice_date BETWEEN ? AND ?
                        AND status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                    ORDER BY invoice_date DESC
                    LIMIT 10
                """

                recent_data = self.db_manager.execute_query(recent_query, [tenant_id, start_date, end_date])
                recent_transactions = recent_data if recent_data else []
            except Exception as e:
                if "no such column" in str(e).lower():
                    # Fallback: Use basic invoice data without GST breakdown
                    recent_query = """
                        SELECT
                            'SALES' as type,
                            invoice_number as reference,
                            invoice_date as date,
                            total_amount,
                            COALESCE(total_amount * 0.18, 0) as tax_amount,
                            0 as cgst_amount,
                            0 as sgst_amount,
                            0 as igst_amount
                        FROM sales_invoices
                        WHERE tenant_id = ?
                            AND invoice_date BETWEEN ? AND ?
                            AND status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                        ORDER BY invoice_date DESC
                        LIMIT 10
                    """

                    recent_data = self.db_manager.execute_query(recent_query, [tenant_id, start_date, end_date])
                    recent_transactions = recent_data if recent_data else []
                else:
                    recent_transactions = []

            return {
                'success': True,
                'data': {
                    'collection_summary': {
                        'total_gst': float(collection.get('total_gst', 0)),
                        'total_cgst': float(collection.get('total_cgst', 0)),
                        'total_sgst': float(collection.get('total_sgst', 0)),
                        'total_igst': float(collection.get('total_igst', 0)),
                        'invoice_count': int(collection.get('invoice_count', 0))
                    },
                    'payment_summary': {
                        'paid_gst': float(payment.get('paid_gst', 0)),
                        'paid_cgst': float(payment.get('paid_cgst', 0)),
                        'paid_sgst': float(payment.get('paid_sgst', 0)),
                        'paid_igst': float(payment.get('paid_igst', 0)),
                        'payment_count': int(payment.get('payment_count', 0))
                    },
                    'net_liability': float(collection.get('total_gst', 0)) - float(payment.get('paid_gst', 0)),
                    'recent_transactions': recent_transactions,
                    'period': {'start_date': start_date, 'end_date': end_date}
                }
            }

        except Exception as e:
            logger.error(f"Error getting GST dashboard: {str(e)}")
            raise

    def get_gst_returns_data(self, tenant_id: int, start_date: str = None, end_date: str = None) -> dict:
        """Get GST returns data"""
        try:
            if not start_date or not end_date:
                from datetime import date
                end_date = date.today().isoformat()
                start_date = date.today().replace(day=1).isoformat()

            # Get current month and year for GSTR reports
            from datetime import datetime
            end_dt = datetime.fromisoformat(end_date)
            month = end_dt.month
            year = end_dt.year

            # Generate GSTR-1 and GSTR-3B data
            gstr1_data = self.generate_gstr1_report(tenant_id, month, year)
            gstr3b_data = self.generate_gstr3b_report(tenant_id, month, year)

            return {
                'success': True,
                'data': {
                    'gstr1': gstr1_data,
                    'gstr3b': gstr3b_data,
                    'period': {'start_date': start_date, 'end_date': end_date}
                }
            }

        except Exception as e:
            logger.error(f"Error getting GST returns data: {str(e)}")
            raise

    def get_tds_data(self, tenant_id: int, start_date: str = None, end_date: str = None) -> dict:
        """Get TDS data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=90)).isoformat()

            # TDS Deductions Summary
            tds_query = """
                SELECT
                    tt.tax_name,
                    tt.tax_code,
                    COALESCE(SUM(jel.debit_amount), 0) as total_deducted,
                    COUNT(DISTINCT je.id) as transaction_count
                FROM journal_entries je
                JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                JOIN tax_types tt ON coa.account_code LIKE CONCAT(tt.tax_code, '%')
                WHERE je.tenant_id = ?
                    AND je.transaction_date BETWEEN ? AND ?
                    AND je.status = 'POSTED'
                    AND tt.tax_code LIKE 'TDS%'
                GROUP BY tt.tax_name, tt.tax_code
                ORDER BY total_deducted DESC
            """

            tds_summary = self.db_manager.execute_query(tds_query, [tenant_id, start_date, end_date])

            # TDS Certificates to be generated - using journal entries since tds_amount column doesn't exist
            certificates_query = """
                SELECT
                    v.vendor_name,
                    v.pan_number,
                    pi.invoice_number,
                    pi.invoice_date,
                    pi.total_amount,
                    COALESCE(jel.debit_amount, 0) as tds_amount,
                    CASE
                        WHEN pi.total_amount > 0 THEN ROUND((COALESCE(jel.debit_amount, 0) / pi.total_amount) * 100, 2)
                        ELSE 0
                    END as tds_rate
                FROM purchase_invoices pi
                JOIN vendors v ON pi.vendor_id = v.id
                LEFT JOIN journal_entries je ON pi.journal_entry_id = je.id
                LEFT JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                LEFT JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE pi.tenant_id = ?
                    AND pi.invoice_date BETWEEN ? AND ?
                    AND pi.status IN ('RECEIVED', 'PAID')
                    AND (coa.account_name LIKE '%TDS%' OR jel.debit_amount IS NULL)
                GROUP BY pi.id, v.vendor_name, v.pan_number, pi.invoice_number, pi.invoice_date, pi.total_amount
                HAVING COALESCE(jel.debit_amount, 0) > 0
                ORDER BY pi.invoice_date DESC
            """

            certificates_data = self.db_manager.execute_query(certificates_query, [tenant_id, start_date, end_date])

            return {
                'success': True,
                'data': {
                    'tds_summary': tds_summary,
                    'certificates_pending': certificates_data,
                    'total_deducted': sum(float(item['total_deducted']) for item in tds_summary),
                    'period': {'start_date': start_date, 'end_date': end_date}
                }
            }

        except Exception as e:
            logger.error(f"Error getting TDS data: {str(e)}")
            raise

    def get_tcs_data(self, tenant_id: int, start_date: str = None, end_date: str = None) -> dict:
        """Get TCS data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=90)).isoformat()

            # TCS Collections Summary
            tcs_query = """
                SELECT
                    tt.tax_name,
                    tt.tax_code,
                    COALESCE(SUM(jel.credit_amount), 0) as total_collected,
                    COUNT(DISTINCT je.id) as transaction_count
                FROM journal_entries je
                JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                JOIN tax_types tt ON coa.account_code LIKE CONCAT(tt.tax_code, '%')
                WHERE je.tenant_id = ?
                    AND je.transaction_date BETWEEN ? AND ?
                    AND je.status = 'POSTED'
                    AND tt.tax_code LIKE 'TCS%'
                GROUP BY tt.tax_name, tt.tax_code
                ORDER BY total_collected DESC
            """

            tcs_summary = self.db_manager.execute_query(tcs_query, [tenant_id, start_date, end_date])

            # TCS on Sales
            sales_tcs_query = """
                SELECT
                    c.customer_name,
                    c.pan_number,
                    si.invoice_number,
                    si.invoice_date,
                    si.total_amount,
                    si.tcs_amount,
                    si.tcs_rate
                FROM sales_invoices si
                JOIN customers c ON si.customer_id = c.id
                WHERE si.tenant_id = ?
                    AND si.invoice_date BETWEEN ? AND ?
                    AND si.tcs_amount > 0
                    AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                ORDER BY si.invoice_date DESC
            """

            sales_tcs_data = self.db_manager.execute_query(sales_tcs_query, [tenant_id, start_date, end_date])

            return {
                'success': True,
                'data': {
                    'tcs_summary': tcs_summary,
                    'sales_with_tcs': sales_tcs_data,
                    'total_collected': sum(float(item['total_collected']) for item in tcs_summary),
                    'period': {'start_date': start_date, 'end_date': end_date}
                }
            }

        except Exception as e:
            logger.error(f"Error getting TCS data: {str(e)}")
            raise

    def generate_tds_certificate(self, payment_id: int, tenant_id: int) -> dict:
        """Generate TDS certificate for a payment"""
        try:
            # Get payment details
            payment_query = """
                SELECT
                    vp.*,
                    v.vendor_name,
                    v.vendor_code,
                    v.pan_number,
                    v.address,
                    v.city,
                    v.state,
                    v.pincode
                FROM vendor_payments vp
                JOIN vendors v ON vp.vendor_id = v.id
                WHERE vp.id = ? AND vp.tenant_id = ?
            """

            payment_result = self.db_manager.execute_query(payment_query, [payment_id, tenant_id])
            if not payment_result:
                raise ValueError(f"Payment with ID {payment_id} not found")

            payment = payment_result[0]

            # Get TDS details from journal entries
            tds_query = """
                SELECT
                    jel.debit_amount as tds_amount,
                    coa.account_name,
                    je.transaction_date as journal_date,
                    je.description
                FROM journal_entries je
                JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.reference_type = 'VENDOR_PAYMENT'
                    AND je.reference_id = ?
                    AND coa.account_name LIKE '%TDS%'
                    AND jel.debit_amount > 0
            """

            tds_result = self.db_manager.execute_query(tds_query, [str(payment_id)])
            tds_amount = float(tds_result[0]['tds_amount']) if tds_result else 0

            # Calculate TDS details
            gross_amount = float(payment['total_amount']) + tds_amount
            tds_rate = (tds_amount / gross_amount * 100) if gross_amount > 0 else 0

            return {
                'certificate_type': 'TDS_CERTIFICATE',
                'certificate_number': f"TDS-{payment['payment_number']}",
                'deductee_details': {
                    'name': payment['vendor_name'],
                    'pan': payment['pan_number'],
                    'address': f"{payment['address']}, {payment['city']}, {payment['state']} - {payment['pincode']}"
                },
                'payment_details': {
                    'payment_date': payment['payment_date'],
                    'payment_number': payment['payment_number'],
                    'gross_amount': gross_amount,
                    'tds_rate': round(tds_rate, 2),
                    'tds_amount': tds_amount,
                    'net_amount': float(payment['total_amount'])
                },
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating TDS certificate: {str(e)}")
            raise
