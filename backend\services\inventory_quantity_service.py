"""
Inventory Quantity Management Service

This service handles automatic inventory quantity adjustments based on delivery note status transitions.
It implements proper reservation, shipping, and consumption logic with audit trails.

Business Logic:
1. Status "prepared": Reserve requested quantity from available inventory
2. Status "dispatched": Move reserved quantity to shipped quantity
3. Status "received": Permanently deduct shipped quantity from total inventory
4. Handle status reversals and cancellations with proper quantity restoration

Database Fields:
- inventory.quantity: Total inventory quantity
- inventory.reserved_quantity: Quantity reserved for pending delivery notes
- inventory.shipped_quantity: Quantity shipped but not yet received
- Available quantity = quantity - reserved_quantity - shipped_quantity
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class InventoryQuantityService:
    """Service for managing inventory quantities based on delivery note status transitions"""
    
    def __init__(self):
        # Use the default database manager (it will find the correct path)
        self.db_manager = DatabaseManager()
    
    def get_available_quantity(self, inventory_item_id: int) -> int:
        """
        Get available quantity for an inventory item
        Available = total - reserved - shipped
        """
        try:
            query = """
                SELECT quantity, reserved_quantity, shipped_quantity
                FROM inventory 
                WHERE id = ? AND is_active = 1
            """
            result = self.db_manager.execute_query(query, (inventory_item_id,))
            
            if not result:
                return 0
                
            item = result[0]
            total_qty = item['quantity'] or 0
            reserved_qty = item['reserved_quantity'] or 0
            shipped_qty = item['shipped_quantity'] or 0
            
            available = total_qty - reserved_qty - shipped_qty
            return max(0, available)  # Ensure non-negative
            
        except Exception as e:
            logger.error(f"Error getting available quantity for item {inventory_item_id}: {e}")
            return 0
    
    def check_stock_availability(self, items: List[Dict]) -> Tuple[bool, List[str]]:
        """
        Check if requested quantities are available for all items
        Returns (is_available, error_messages)
        """
        errors = []
        
        try:
            for item in items:
                inventory_item_id = item.get('inventory_item_id')
                requested_qty = item.get('requested_quantity', 0)
                
                if not inventory_item_id:
                    continue
                    
                available_qty = self.get_available_quantity(inventory_item_id)
                
                if requested_qty > available_qty:
                    # Get item name for error message
                    name_query = "SELECT name FROM inventory WHERE id = ?"
                    name_result = self.db_manager.execute_query(name_query, (inventory_item_id,))
                    item_name = name_result[0][0] if name_result else f"Item {inventory_item_id}"
                    
                    errors.append(
                        f"{item_name}: Requested {requested_qty} but only {available_qty} available"
                    )
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error checking stock availability: {e}")
            return False, [f"Error checking stock availability: {str(e)}"]
    
    def reserve_inventory(self, delivery_note_id: int, items: List[Dict]) -> bool:
        """
        Reserve inventory quantities when delivery note status is 'prepared'
        """
        try:
            # First check availability
            is_available, errors = self.check_stock_availability(items)
            if not is_available:
                logger.error(f"Cannot reserve inventory for DN {delivery_note_id}: {'; '.join(errors)}")
                return False
            
            # Reserve quantities
            for item in items:
                inventory_item_id = item.get('inventory_item_id')
                requested_qty = item.get('requested_quantity', 0)
                
                if not inventory_item_id or requested_qty <= 0:
                    continue
                
                # Update reserved quantity
                update_query = """
                    UPDATE inventory
                    SET reserved_quantity = reserved_quantity + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                self.db_manager.execute_update(update_query, (requested_qty, inventory_item_id))
                
                # Log the reservation
                self._log_quantity_movement(
                    inventory_item_id, 
                    delivery_note_id,
                    'reserved',
                    requested_qty,
                    f"Reserved for delivery note {delivery_note_id}"
                )
            
            logger.info(f"Successfully reserved inventory for delivery note {delivery_note_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error reserving inventory for DN {delivery_note_id}: {e}")
            return False
    
    def dispatch_inventory(self, delivery_note_id: int, items: List[Dict]) -> bool:
        """
        Move reserved quantities to shipped when delivery note status is 'dispatched'
        """
        try:
            for item in items:
                inventory_item_id = item.get('inventory_item_id')
                requested_qty = item.get('requested_quantity', 0)
                
                if not inventory_item_id or requested_qty <= 0:
                    continue
                
                # Move from reserved to shipped
                update_query = """
                    UPDATE inventory
                    SET reserved_quantity = reserved_quantity - ?,
                        shipped_quantity = shipped_quantity + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? AND reserved_quantity >= ?
                """
                result = self.db_manager.execute_update(
                    update_query,
                    (requested_qty, requested_qty, inventory_item_id, requested_qty)
                )
                
                # Log the dispatch
                self._log_quantity_movement(
                    inventory_item_id,
                    delivery_note_id,
                    'dispatched',
                    requested_qty,
                    f"Dispatched for delivery note {delivery_note_id}"
                )
            
            logger.info(f"Successfully dispatched inventory for delivery note {delivery_note_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error dispatching inventory for DN {delivery_note_id}: {e}")
            return False
    
    def consume_inventory(self, delivery_note_id: int, items: List[Dict]) -> bool:
        """
        Permanently deduct shipped quantities when delivery note status is 'received'
        """
        try:
            for item in items:
                inventory_item_id = item.get('inventory_item_id')
                requested_qty = item.get('requested_quantity', 0)
                
                if not inventory_item_id or requested_qty <= 0:
                    continue
                
                # Permanently deduct from total quantity and clear shipped quantity
                update_query = """
                    UPDATE inventory
                    SET quantity = quantity - ?,
                        shipped_quantity = shipped_quantity - ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? AND shipped_quantity >= ?
                """
                self.db_manager.execute_update(
                    update_query,
                    (requested_qty, requested_qty, inventory_item_id, requested_qty)
                )
                
                # Log the consumption
                self._log_quantity_movement(
                    inventory_item_id,
                    delivery_note_id,
                    'consumed',
                    requested_qty,
                    f"Consumed for delivery note {delivery_note_id}"
                )
            
            logger.info(f"Successfully consumed inventory for delivery note {delivery_note_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error consuming inventory for DN {delivery_note_id}: {e}")
            return False
    
    def restore_inventory(self, delivery_note_id: int, items: List[Dict], from_status: str) -> bool:
        """
        Restore inventory quantities when delivery note is cancelled or status is reversed
        """
        try:
            for item in items:
                inventory_item_id = item.get('inventory_item_id')
                requested_qty = item.get('requested_quantity', 0)
                
                if not inventory_item_id or requested_qty <= 0:
                    continue
                
                if from_status == 'prepared':
                    # Return reserved quantity to available
                    update_query = """
                        UPDATE inventory
                        SET reserved_quantity = reserved_quantity - ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ? AND reserved_quantity >= ?
                    """
                    self.db_manager.execute_update(
                        update_query,
                        (requested_qty, inventory_item_id, requested_qty)
                    )
                    
                elif from_status == 'dispatched':
                    # Return shipped quantity to available
                    update_query = """
                        UPDATE inventory
                        SET shipped_quantity = shipped_quantity - ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ? AND shipped_quantity >= ?
                    """
                    self.db_manager.execute_update(
                        update_query,
                        (requested_qty, inventory_item_id, requested_qty)
                    )
                
                # Log the restoration
                self._log_quantity_movement(
                    inventory_item_id,
                    delivery_note_id,
                    'restored',
                    requested_qty,
                    f"Restored from {from_status} status for delivery note {delivery_note_id}"
                )
            
            logger.info(f"Successfully restored inventory for delivery note {delivery_note_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring inventory for DN {delivery_note_id}: {e}")
            return False
    
    def _log_quantity_movement(self, inventory_item_id: int, delivery_note_id: int, 
                              movement_type: str, quantity: int, notes: str):
        """Log inventory quantity movements for audit trail"""
        try:
            # Create inventory_movements table if it doesn't exist
            create_table_query = """
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    inventory_item_id INTEGER NOT NULL,
                    delivery_note_id INTEGER,
                    movement_type TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id),
                    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id)
                )
            """
            self.db_manager.execute_query(create_table_query)
            
            # Insert movement record
            insert_query = """
                INSERT INTO inventory_movements 
                (inventory_item_id, delivery_note_id, movement_type, quantity, notes)
                VALUES (?, ?, ?, ?, ?)
            """
            self.db_manager.execute_query(
                insert_query,
                (inventory_item_id, delivery_note_id, movement_type, quantity, notes)
            )
            
        except Exception as e:
            logger.error(f"Error logging quantity movement: {e}")
    
    def get_inventory_summary(self, inventory_item_id: int) -> Dict:
        """Get comprehensive inventory summary for an item"""
        try:
            query = """
                SELECT id, name, sku, quantity, reserved_quantity, shipped_quantity,
                       (quantity - reserved_quantity - shipped_quantity) as available_quantity
                FROM inventory 
                WHERE id = ?
            """
            result = self.db_manager.execute_query(query, (inventory_item_id,))
            
            if not result:
                return {}
            
            item = result[0]
            return {
                'id': item[0],
                'name': item[1],
                'sku': item[2],
                'total_quantity': item[3] or 0,
                'reserved_quantity': item[4] or 0,
                'shipped_quantity': item[5] or 0,
                'available_quantity': max(0, item[6] or 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting inventory summary for item {inventory_item_id}: {e}")
            return {}
