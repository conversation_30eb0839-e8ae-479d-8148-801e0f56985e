[{"id": 2, "invoice_number": "INV00002", "sid_number": "TNJ002", "patient_id": 29, "subtotal": 4050, "discount": 475.17, "tax": 729, "total_amount": 4303.83, "paid_amount": 0, "balance": 4303.83, "payment_method": "Card", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-08", "due_date": "2025-06-07", "tenant_id": 3, "created_by": 3, "created_at": "2025-05-08T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183"}, {"id": 3, "invoice_number": "INV00003", "sid_number": "SKZ003", "patient_id": 33, "subtotal": 1350, "discount": 94.44, "tax": 243, "total_amount": 1498.56, "paid_amount": 1273.46, "balance": 225.0999999999999, "payment_method": "Cash", "payment_status": "partial", "status": "active", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "tenant_id": 2, "created_by": 1, "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183"}, {"id": 4, "invoice_number": "INV00004", "sid_number": "TNJ004", "patient_id": 12, "subtotal": 1200, "discount": 94.55, "tax": 216, "total_amount": 1321.45, "paid_amount": 1321.45, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "tenant_id": 3, "created_by": 1, "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183"}, {"id": 5, "invoice_number": "INV00005", "sid_number": "MYD005", "patient_id": 31, "subtotal": 1900, "discount": 28.52, "tax": 342, "total_amount": 2213.48, "paid_amount": 2213.48, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-12", "due_date": "2025-05-12", "tenant_id": 1, "created_by": 1, "created_at": "2025-04-12T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180"}, {"id": 6, "invoice_number": "INV00006", "sid_number": "TNJ006", "patient_id": 31, "subtotal": 4700, "discount": 441.76, "tax": 846, "total_amount": 5104.24, "paid_amount": 5104.24, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-18", "due_date": "2025-05-18", "tenant_id": 3, "created_by": 1, "created_at": "2025-04-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180"}, {"id": 7, "invoice_number": "INV00007", "sid_number": "TNJ007", "patient_id": 40, "subtotal": 2550, "discount": 72.01, "tax": 459, "total_amount": 2936.99, "paid_amount": 0, "balance": 2936.99, "payment_method": "Bank Transfer", "payment_status": "pending", "status": "active", "invoice_date": "2025-04-07", "due_date": "2025-05-07", "tenant_id": 3, "created_by": 2, "created_at": "2025-04-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180"}, {"id": 8, "invoice_number": "INV00008", "sid_number": "SKZ008", "patient_id": 14, "subtotal": 2050, "discount": 125.58, "tax": 369, "total_amount": 2293.42, "paid_amount": 2293.42, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-05-05", "due_date": "2025-06-04", "tenant_id": 2, "created_by": 2, "created_at": "2025-05-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180"}, {"id": 9, "invoice_number": "INV00009", "sid_number": "SKZ009", "patient_id": 50, "subtotal": 850, "discount": 19.72, "tax": 153, "total_amount": 983.28, "paid_amount": 0, "balance": 983.28, "payment_method": "Card", "payment_status": "pending", "status": "active", "invoice_date": "2025-03-23", "due_date": "2025-04-22", "tenant_id": 2, "created_by": 1, "created_at": "2025-03-23T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181"}, {"id": 10, "invoice_number": "INV00010", "sid_number": "TNJ010", "patient_id": 29, "subtotal": 1650, "discount": 317.03, "tax": 297, "total_amount": 1629.97, "paid_amount": 0, "balance": 1629.97, "payment_method": "UPI", "payment_status": "pending", "status": "active", "invoice_date": "2025-03-28", "due_date": "2025-04-27", "tenant_id": 3, "created_by": 3, "created_at": "2025-03-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181"}, {"id": 11, "invoice_number": "INV00011", "sid_number": "TNJ011", "patient_id": 13, "subtotal": 5700, "discount": 1023.75, "tax": 1026, "total_amount": 5702.25, "paid_amount": 0, "balance": 5702.25, "payment_method": "Bank Transfer", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-07", "due_date": "2025-06-06", "tenant_id": 3, "created_by": 2, "created_at": "2025-05-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181"}, {"id": 12, "invoice_number": "INV00012", "sid_number": "TNJ012", "patient_id": 40, "subtotal": 3200, "discount": 514.27, "tax": 576, "total_amount": 3261.73, "paid_amount": 0, "balance": 3261.73, "payment_method": "Bank Transfer", "payment_status": "pending", "status": "active", "invoice_date": "2025-06-10", "due_date": "2025-07-10", "tenant_id": 3, "created_by": 2, "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181"}, {"id": 13, "invoice_number": "INV00013", "sid_number": "TNJ013", "patient_id": 42, "subtotal": 2400, "discount": 398.17, "tax": 432, "total_amount": 2433.83, "paid_amount": 0, "balance": 2433.83, "payment_method": "Card", "payment_status": "pending", "status": "active", "invoice_date": "2025-06-05", "due_date": "2025-07-05", "tenant_id": 3, "created_by": 2, "created_at": "2025-06-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182"}, {"id": 14, "invoice_number": "INV00014", "sid_number": "SKZ014", "patient_id": 23, "subtotal": 3950, "discount": 426.16, "tax": 711, "total_amount": 4234.84, "paid_amount": 0, "balance": 4234.84, "payment_method": "Cash", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-18", "due_date": "2025-06-17", "tenant_id": 2, "created_by": 2, "created_at": "2025-05-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182"}, {"id": 15, "invoice_number": "INV00015", "sid_number": "MYD015", "patient_id": 31, "subtotal": 2800, "discount": 303.39, "tax": 504, "total_amount": 3000.61, "paid_amount": 0, "balance": 3000.61, "payment_method": "UPI", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "tenant_id": 1, "created_by": 3, "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182"}, {"id": 16, "invoice_number": "INV00016", "sid_number": "MYD016", "patient_id": 27, "subtotal": 1000, "discount": 176.53, "tax": 180, "total_amount": 1003.47, "paid_amount": 1003.47, "balance": 0, "payment_method": "UPI", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-09", "due_date": "2025-05-09", "tenant_id": 1, "created_by": 1, "created_at": "2025-04-09T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181"}, {"id": 17, "invoice_number": "INV00017", "sid_number": "TNJ017", "patient_id": 16, "subtotal": 5950, "discount": 885.62, "tax": 1071, "total_amount": 6135.38, "paid_amount": 6135.38, "balance": 0, "payment_method": "Card", "payment_status": "paid", "status": "completed", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "tenant_id": 3, "created_by": 2, "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181"}, {"id": 18, "invoice_number": "INV00018", "sid_number": "TNJ018", "patient_id": 36, "subtotal": 600, "discount": 25.32, "tax": 108, "total_amount": 682.68, "paid_amount": 0, "balance": 682.68, "payment_method": "Cash", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-06", "due_date": "2025-06-05", "tenant_id": 3, "created_by": 2, "created_at": "2025-05-06T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181"}, {"id": 19, "invoice_number": "INV00019", "sid_number": "MYD019", "patient_id": 37, "subtotal": 3250, "discount": 398.74, "tax": 585, "total_amount": 3436.26, "paid_amount": 1501.97, "balance": 1934.2900000000002, "payment_method": "UPI", "payment_status": "partial", "status": "active", "invoice_date": "2025-04-29", "due_date": "2025-05-29", "tenant_id": 1, "created_by": 3, "created_at": "2025-04-29T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181"}, {"id": 20, "invoice_number": "INV00020", "sid_number": "MYD020", "patient_id": 23, "subtotal": 4850, "discount": 252.63, "tax": 873, "total_amount": 5470.37, "paid_amount": 5470.37, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "tenant_id": 1, "created_by": 1, "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191"}, {"id": 21, "invoice_number": "INV00021", "sid_number": "SKZ021", "patient_id": 4, "subtotal": 2600, "discount": 140.02, "tax": 468, "total_amount": 2927.98, "paid_amount": 2927.98, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "tenant_id": 2, "created_by": 1, "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191"}, {"id": 22, "invoice_number": "INV00022", "sid_number": "MYD022", "patient_id": 22, "subtotal": 3400, "discount": 616.45, "tax": 612, "total_amount": 3395.55, "paid_amount": 711.93, "balance": 2683.*************, "payment_method": "Card", "payment_status": "partial", "status": "active", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "tenant_id": 1, "created_by": 3, "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191"}, {"id": 23, "invoice_number": "INV00023", "sid_number": "MYD023", "patient_id": 28, "subtotal": 2900, "discount": 179.42, "tax": 522, "total_amount": 3242.58, "paid_amount": 598.59, "balance": 2643.99, "payment_method": "Bank Transfer", "payment_status": "partial", "status": "active", "invoice_date": "2025-03-27", "due_date": "2025-04-26", "tenant_id": 1, "created_by": 2, "created_at": "2025-03-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347183"}, {"id": 24, "invoice_number": "INV00024", "sid_number": "MYD024", "patient_id": 40, "subtotal": 4200, "discount": 284.55, "tax": 756, "total_amount": 4671.45, "paid_amount": 3954.97, "balance": 716.48, "payment_method": "Bank Transfer", "payment_status": "partial", "status": "active", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "tenant_id": 1, "created_by": 2, "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601"}, {"id": 25, "invoice_number": "INV00025", "sid_number": "MYD025", "patient_id": 20, "subtotal": 250, "discount": 7.14, "tax": 45, "total_amount": 287.86, "paid_amount": 89.33, "balance": 198.**************, "payment_method": "UPI", "payment_status": "partial", "status": "active", "invoice_date": "2025-06-04", "due_date": "2025-07-04", "tenant_id": 1, "created_by": 2, "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601"}, {"id": 26, "invoice_number": "INV00026", "sid_number": "SKZ026", "patient_id": 25, "subtotal": 5000, "discount": 275.61, "tax": 900, "total_amount": 5624.39, "paid_amount": 1133.07, "balance": 4491.************, "payment_method": "Bank Transfer", "payment_status": "partial", "status": "active", "invoice_date": "2025-05-28", "due_date": "2025-06-27", "tenant_id": 2, "created_by": 1, "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601"}, {"id": 27, "invoice_number": "INV00027", "sid_number": "MYD027", "patient_id": 31, "subtotal": 700, "discount": 60.48, "tax": 126, "total_amount": 765.52, "paid_amount": 765.52, "balance": 765.52, "payment_method": "Bank Transfer", "payment_status": "paid", "status": "active", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "tenant_id": 1, "created_by": 3, "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601"}, {"id": 28, "invoice_number": "INV00028", "sid_number": "MYD028", "patient_id": 4, "subtotal": 750, "discount": 142.96, "tax": 135, "total_amount": 742.04, "paid_amount": 0, "balance": 742.04, "payment_method": "UPI", "payment_status": "pending", "status": "active", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "tenant_id": 1, "created_by": 2, "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638"}, {"id": 29, "invoice_number": "INV00029", "sid_number": "TNJ029", "patient_id": 18, "subtotal": 5650, "discount": 476.17, "tax": 1017, "total_amount": 6190.83, "paid_amount": 0, "balance": 6190.83, "payment_method": "Bank Transfer", "payment_status": "pending", "status": "active", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "tenant_id": 3, "created_by": 1, "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638"}, {"id": 30, "invoice_number": "INV00030", "sid_number": "SKZ030", "patient_id": 4, "subtotal": 4750, "discount": 594.54, "tax": 855, "total_amount": 5010.46, "paid_amount": 542.76, "balance": 4467.7, "payment_method": "Card", "payment_status": "partial", "status": "active", "invoice_date": "2025-04-02", "due_date": "2025-05-02", "tenant_id": 2, "created_by": 3, "created_at": "2025-04-02T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638"}, {"id": 31, "invoice_number": "INV00031", "sid_number": "MYD003", "patient_id": 34, "subtotal": 944, "discount": 0, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-06-20", "due_date": "2025-07-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-06-20T21:58:22.614664", "updated_at": "2025-06-25T14:43:56.710105"}, {"id": 32, "invoice_number": "INV00032", "sid_number": "SKZ013", "patient_id": 37, "subtotal": 944, "discount": 0, "tax": 144, "total_amount": 944, "paid_amount": 944, "balance": 944, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "tenant_id": 2, "created_by": 5, "created_at": "2025-06-21T12:47:09.145759", "updated_at": "2025-06-21T14:31:12.269097"}, {"id": 33, "invoice_number": "INV00033", "sid_number": "MYD002", "patient_id": 34, "subtotal": 944, "discount": 0, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "tenant_id": 1, "created_by": 4, "created_at": "2025-06-21T14:21:32.518940", "updated_at": "2025-06-21T14:21:32.518940"}, {"id": 34, "invoice_number": "INV00034", "sid_number": "SKZ001", "patient_id": 37, "subtotal": 944, "discount": 0, "tax": 144, "total_amount": 944, "paid_amount": 944, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "tenant_id": 2, "created_by": 5, "created_at": "2025-06-21T14:32:20.748951", "updated_at": "2025-06-25T15:42:15.990422"}, {"id": 35, "invoice_number": "INV00035", "sid_number": "SKZ015", "patient_id": 37, "subtotal": 944, "discount": 0, "tax": 144, "total_amount": 944, "paid_amount": 944, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "completed", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "tenant_id": 2, "created_by": 4, "created_at": "2025-06-21T14:33:23.894705", "updated_at": "2025-06-25T15:41:45.508376"}, {"id": 36, "invoice_number": "INV00036", "sid_number": "008", "patient_id": 28, "subtotal": 6608, "discount": 0, "tax": 1008, "total_amount": 6608, "paid_amount": 6610, "balance": -2, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "tenant_id": 1, "created_by": 4, "created_at": "2025-06-21T18:40:40.093362", "updated_at": "2025-06-21T18:40:40.093364"}, {"id": 37, "invoice_number": "INV00037", "sid_number": "015", "patient_id": 51, "subtotal": 15104, "discount": 0, "tax": 2304, "total_amount": 15104, "paid_amount": 16000, "balance": -896, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-06-27", "due_date": "2025-07-27", "tenant_id": 1, "created_by": 4, "created_at": "2025-06-27T17:46:17.089903", "updated_at": "2025-06-27T17:46:17.089904"}, {"id": 38, "invoice_number": "INV00038", "sid_number": "017", "patient_id": 51, "subtotal": 7434, "discount": 0, "tax": 1134, "total_amount": 7434, "paid_amount": 8000, "balance": -566, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-06-29", "due_date": "2025-07-29", "tenant_id": 1, "created_by": 4, "created_at": "2025-06-29T11:59:27.311909", "updated_at": "2025-06-29T11:59:27.311910"}, {"id": 39, "invoice_number": "INV00039", "sid_number": "020", "patient_id": 51, "subtotal": 6313, "discount": 0, "tax": 963, "total_amount": 6313, "paid_amount": 4999.92, "balance": 1313.08, "payment_method": "", "payment_status": "partial", "status": "active", "invoice_date": "2025-07-06", "due_date": "2025-08-05", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-06T12:14:39.004091", "updated_at": "2025-07-06T12:14:39.004096"}, {"id": 40, "invoice_number": "INV00040", "sid_number": "025", "patient_id": 51, "subtotal": 2658.03, "discount": 0, "tax": 0, "total_amount": 2658.03, "paid_amount": 2699.96, "balance": -41.929999999999836, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-07", "due_date": "2025-08-06", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-07T12:53:00.903498", "updated_at": "2025-07-07T12:53:00.903500"}, {"id": 41, "invoice_number": "INV00041", "sid_number": "040", "patient_id": 1, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "tenant_id": 1, "created_by": 1, "created_at": "2025-07-08T17:22:11.439341", "updated_at": "2025-07-08T17:22:11.439342"}, {"id": 42, "invoice_number": "INV00042", "sid_number": "049", "patient_id": 1, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "tenant_id": 1, "created_by": 1, "created_at": "2025-07-08T17:33:16.720431", "updated_at": "2025-07-08T17:33:16.720433"}, {"id": 43, "invoice_number": "INV00043", "sid_number": "051", "patient_id": 1, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "tenant_id": 1, "created_by": 1, "created_at": "2025-07-08T17:37:39.459516", "updated_at": "2025-07-08T17:37:39.459517"}, {"id": 44, "invoice_number": "INV00044", "sid_number": "052", "patient_id": 1, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "tenant_id": 1, "created_by": 1, "created_at": "2025-07-08T17:38:54.820842", "updated_at": "2025-07-08T17:38:54.820844"}, {"id": 45, "invoice_number": "INV00045", "sid_number": "053", "patient_id": 1, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "pending", "status": "active", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "tenant_id": 1, "created_by": 1, "created_at": "2025-07-08T17:39:55.610404", "updated_at": "2025-07-08T17:39:55.610405"}, {"id": 47, "invoice_number": "INV00046", "sid_number": "001", "patient_id": 53, "subtotal": 100, "discount": 0, "tax": 0, "total_amount": 100, "paid_amount": 100, "balance": 0, "payment_method": "Cash", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "tenant_id": 2, "created_by": 5, "created_at": "2025-07-09T16:25:58.970022", "updated_at": "2025-07-09T16:25:58.970024"}, {"id": 51, "invoice_number": "INV00051", "sid_number": "116", "patient_id": 28, "subtotal": 5074, "discount": 0, "tax": 774, "total_amount": 5074, "paid_amount": 5999.96, "balance": -925.96, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-09T16:34:04.566473", "updated_at": "2025-07-09T16:34:04.566474"}, {"id": 55, "invoice_number": "INV00055", "sid_number": "208", "patient_id": 51, "subtotal": 2596, "discount": 0, "tax": 396, "total_amount": 2596, "paid_amount": 3000, "balance": -404, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-15", "due_date": "2025-08-14", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-15T15:14:41.074226", "updated_at": "2025-07-15T15:14:41.074229"}, {"id": 57, "invoice_number": "INV00057", "sid_number": "217", "patient_id": 59, "subtotal": 9700, "discount": 0, "tax": 1746, "total_amount": 9700, "paid_amount": 9700, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-16T10:51:45.327764", "updated_at": "2025-07-16T10:51:45.327766"}, {"id": 58, "invoice_number": "INV00058", "sid_number": "220", "patient_id": 59, "subtotal": 100, "discount": 0, "tax": 18, "total_amount": 100, "paid_amount": 100, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-16T11:02:04.702677", "updated_at": "2025-07-16T11:02:04.702679"}, {"id": 59, "invoice_number": "INV00059", "sid_number": "222", "patient_id": 44, "subtotal": 3100, "discount": 0, "tax": 558, "total_amount": 3100, "paid_amount": 3100, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-16T19:28:03.623684", "updated_at": "2025-07-16T19:28:03.623685"}, {"id": 60, "invoice_number": "INV00060", "sid_number": "234", "patient_id": 61, "subtotal": 4800, "discount": 0, "tax": 864, "total_amount": 4800, "paid_amount": 5000, "balance": -200, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-17T19:06:30.208055", "updated_at": "2025-07-17T19:06:30.208057"}, {"id": 61, "invoice_number": "INV00061", "sid_number": "248", "patient_id": 51, "subtotal": 3600, "discount": 0, "tax": 648, "total_amount": 3600, "paid_amount": 4000, "balance": -400, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-17T19:27:50.352678", "updated_at": "2025-07-17T19:27:50.352680"}, {"id": 62, "invoice_number": "INV00062", "sid_number": "251", "patient_id": 62, "subtotal": 200, "discount": 0, "tax": 36, "total_amount": 200, "paid_amount": 200, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-18T12:27:23.242000", "updated_at": "2025-07-18T12:27:23.242001"}, {"id": 63, "invoice_number": "INV00063", "sid_number": "253", "patient_id": 51, "subtotal": 4600, "discount": 0, "tax": 828, "total_amount": 4600, "paid_amount": 5000, "balance": -400, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-18T15:46:23.662750", "updated_at": "2025-07-18T15:46:23.662752"}, {"id": 64, "invoice_number": "INV00064", "sid_number": "344", "patient_id": 51, "subtotal": 9920, "discount": 0, "tax": 1785.6, "total_amount": 9920, "paid_amount": 10000, "balance": -80, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-21T17:03:48.312532", "updated_at": "2025-07-21T17:03:48.312533"}, {"id": 65, "invoice_number": "INV00065", "sid_number": "346", "patient_id": 64, "subtotal": 3720, "discount": 0, "tax": 669.6, "total_amount": 3720, "paid_amount": 4000, "balance": -280, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-22T11:58:08.452979", "updated_at": "2025-07-22T11:58:08.452980"}, {"id": 66, "invoice_number": "INV00066", "sid_number": "357", "patient_id": 65, "subtotal": 40, "discount": 0, "tax": 7.2, "total_amount": 40, "paid_amount": 50, "balance": -10, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-22T12:42:53.165966", "updated_at": "2025-07-22T12:42:53.165969"}, {"id": 67, "invoice_number": "INV00067", "sid_number": "359", "patient_id": 51, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 20, "paid_amount": 40, "balance": -20, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "tenant_id": 1, "created_by": 4, "created_at": "2025-07-22T12:47:47.017426", "updated_at": "2025-07-22T12:47:47.017427"}, {"id": 68, "invoice_number": "INV00068", "sid_number": "362", "patient_id": 66, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 20, "paid_amount": 20, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-01", "due_date": "2025-08-31", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-01T18:03:07.416509", "updated_at": "2025-08-01T18:03:07.416511"}, {"id": 69, "invoice_number": "INV00069", "sid_number": "364", "patient_id": 67, "subtotal": 8400, "discount": 0, "tax": 1512, "total_amount": 8400, "paid_amount": 9000, "balance": -600, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-01", "due_date": "2025-08-31", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-01T18:57:39.389151", "updated_at": "2025-08-01T18:57:39.389153"}, {"id": 70, "invoice_number": "INV00070", "sid_number": "367", "patient_id": 69, "subtotal": 4820, "discount": 0, "tax": 867.6, "total_amount": 4820, "paid_amount": 5000, "balance": -180, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-06", "due_date": "2025-09-05", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-06T14:38:11.717346", "updated_at": "2025-08-06T14:38:11.717348"}, {"id": 71, "invoice_number": "INV00071", "sid_number": "375", "patient_id": 70, "subtotal": 1100, "discount": 0, "tax": 198, "total_amount": 1100, "paid_amount": 1200, "balance": -100, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T14:49:08.846206", "updated_at": "2025-08-08T14:49:08.846210"}, {"id": 72, "invoice_number": "INV00072", "sid_number": "383", "patient_id": 69, "subtotal": 1100, "discount": 0, "tax": 198, "total_amount": 1100, "paid_amount": 1100, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T15:01:38.433660", "updated_at": "2025-08-08T15:01:38.433664"}, {"id": 73, "invoice_number": "INV00073", "sid_number": "387", "patient_id": 28, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 1200, "paid_amount": 20, "balance": 1396, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T15:06:14.763760", "updated_at": "2025-08-11T16:02:20.432620"}, {"id": 74, "invoice_number": "INV00074", "sid_number": "389", "patient_id": 28, "subtotal": 100, "discount": 0, "tax": 18, "total_amount": 100, "paid_amount": 200, "balance": -100, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T15:29:55.849737", "updated_at": "2025-08-08T15:29:55.849740"}, {"id": 75, "invoice_number": "INV00075", "sid_number": "409", "patient_id": 71, "subtotal": 700, "discount": 0, "tax": 126, "total_amount": 400, "paid_amount": 800, "balance": -328, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T15:57:00.962374", "updated_at": "2025-08-09T17:35:52.271473"}, {"id": 76, "invoice_number": "INV00076", "sid_number": "411", "patient_id": 72, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 1100, "paid_amount": 20, "balance": 1278, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T16:08:22.709395", "updated_at": "2025-08-11T17:56:08.565975"}, {"id": 77, "invoice_number": "INV00077", "sid_number": "414", "patient_id": 73, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 1060, "paid_amount": 400, "balance": 850.8, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T16:15:28.319915", "updated_at": "2025-08-09T17:29:00.394364"}, {"id": 78, "invoice_number": "INV00078", "sid_number": "427", "patient_id": 74, "subtotal": 80, "discount": 0, "tax": 14.4, "total_amount": 147, "paid_amount": 80, "balance": 1690, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T19:05:57.747571", "updated_at": "2025-08-09T17:29:56.320575"}, {"id": 79, "invoice_number": "INV00079", "sid_number": "438", "patient_id": 10, "subtotal": 1500, "discount": 0, "tax": 270, "total_amount": 800, "paid_amount": 400, "balance": 400, "payment_method": "", "payment_status": "partial", "status": "active", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-08T19:35:08.166291", "updated_at": "2025-08-09T16:03:39.367452"}, {"id": 80, "invoice_number": "INV00080", "sid_number": "440", "patient_id": 51, "subtotal": 700, "discount": 0, "tax": 126, "total_amount": 1500, "paid_amount": 700, "balance": 1070, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-09", "due_date": "2025-09-08", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-09T18:06:27.143918", "updated_at": "2025-08-09T18:38:43.756050"}, {"id": 81, "invoice_number": "INV00081", "sid_number": "442", "patient_id": 75, "subtotal": 1200, "discount": 0, "tax": 216, "total_amount": 801, "paid_amount": 1200, "balance": 8287.2, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-11", "due_date": "2025-09-10", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-11T16:06:36.844090", "updated_at": "2025-08-11T17:49:39.577643"}, {"id": 82, "invoice_number": "INV00082", "sid_number": "447", "patient_id": 76, "subtotal": 3100, "discount": 0, "tax": 558, "total_amount": 209, "paid_amount": 3100, "balance": -3076.4, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-11", "due_date": "2025-09-10", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-11T17:58:53.235666", "updated_at": "2025-08-11T17:59:44.936306"}, {"id": 83, "invoice_number": "INV00083", "sid_number": "449", "patient_id": 77, "subtotal": 820, "discount": 0, "tax": 147.6, "total_amount": 1500, "paid_amount": 820, "balance": 950, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-12", "due_date": "2025-09-11", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-12T13:43:27.999154", "updated_at": "2025-08-12T13:50:47.292314"}, {"id": 84, "invoice_number": "INV00084", "sid_number": "453", "patient_id": 78, "subtotal": 250, "discount": 0, "tax": 45, "total_amount": 250, "paid_amount": 250, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-13", "due_date": "2025-09-12", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-13T17:59:58.596594", "updated_at": "2025-08-13T17:59:58.596597"}, {"id": 85, "invoice_number": "INV00085", "sid_number": "455", "patient_id": 79, "subtotal": 640, "discount": 0, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-13", "due_date": "2025-09-12", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-13T18:39:54.964183", "updated_at": "2025-08-13T18:39:54.964187"}, {"id": 86, "invoice_number": "INV00086", "sid_number": "457", "patient_id": 80, "subtotal": 1520, "discount": 0, "tax": 273.6, "total_amount": 1520, "paid_amount": 1520, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-14T13:56:27.925822", "updated_at": "2025-08-14T13:56:27.925823"}, {"id": 87, "invoice_number": "INV00087", "sid_number": "460", "patient_id": 81, "subtotal": 640, "discount": 0, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-14T15:24:44.055555", "updated_at": "2025-08-14T15:24:44.055556"}, {"id": 88, "invoice_number": "INV00088", "sid_number": "462", "patient_id": 82, "subtotal": 790, "discount": 0, "tax": 142.2, "total_amount": 790, "paid_amount": 800, "balance": -10, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-14T15:30:13.105738", "updated_at": "2025-08-14T15:30:13.105741"}, {"id": 89, "invoice_number": "INV00089", "sid_number": "466", "patient_id": 83, "subtotal": 640, "discount": 0, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-14T15:45:53.013640", "updated_at": "2025-08-14T15:45:53.013646"}, {"id": 90, "invoice_number": "INV00090", "sid_number": "469", "patient_id": 84, "subtotal": 640, "discount": 0, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-14T16:13:06.082938", "updated_at": "2025-08-14T16:13:06.082940"}, {"id": 91, "invoice_number": "INV00091", "sid_number": "471", "patient_id": 69, "subtotal": 101, "discount": 0, "tax": 18.18, "total_amount": 101, "paid_amount": 101, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T15:56:29.545920", "updated_at": "2025-08-20T15:56:29.545922"}, {"id": 92, "invoice_number": "INV00092", "sid_number": "473", "patient_id": 51, "subtotal": 102, "discount": 0, "tax": 18.36, "total_amount": 102, "paid_amount": 102, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T17:10:28.687489", "updated_at": "2025-08-20T17:10:28.687491"}, {"id": 93, "invoice_number": "INV00093", "sid_number": "475", "patient_id": 59, "subtotal": 32, "discount": 0, "tax": 5.76, "total_amount": 32, "paid_amount": 32, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T17:38:47.046998", "updated_at": "2025-08-20T17:38:47.047000"}, {"id": 94, "invoice_number": "INV00094", "sid_number": "478", "patient_id": 69, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T17:56:21.387529", "updated_at": "2025-08-20T17:56:21.387531"}, {"id": 95, "invoice_number": "INV00095", "sid_number": "480", "patient_id": 72, "subtotal": 700, "discount": 0, "tax": 126, "total_amount": 700, "paid_amount": 700, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T17:58:13.236416", "updated_at": "2025-08-20T17:58:13.236418"}, {"id": 96, "invoice_number": "INV00096", "sid_number": "483", "patient_id": 69, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T18:11:12.585933", "updated_at": "2025-08-20T18:11:12.585935"}, {"id": 97, "invoice_number": "INV00097", "sid_number": "485", "patient_id": 59, "subtotal": 430, "discount": 0, "tax": 77.4, "total_amount": 430, "paid_amount": 430, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-20T19:24:35.070258", "updated_at": "2025-08-20T19:24:35.070259"}, {"id": 98, "invoice_number": "INV00098", "sid_number": "487", "patient_id": 51, "subtotal": 1200, "discount": 0, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-21T11:07:27.130694", "updated_at": "2025-08-21T11:07:27.130695"}, {"id": 99, "invoice_number": "INV00099", "sid_number": "489", "patient_id": 76, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-21T11:15:33.589211", "updated_at": "2025-08-21T11:15:33.589213"}, {"id": 100, "invoice_number": "INV00100", "sid_number": "492", "patient_id": 67, "subtotal": 1200, "discount": 0, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-21T11:18:01.823702", "updated_at": "2025-08-21T11:18:01.823704"}, {"id": 101, "invoice_number": "INV00101", "sid_number": "494", "patient_id": 51, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-21T11:46:54.281422", "updated_at": "2025-08-21T11:46:54.281424"}, {"id": 102, "invoice_number": "INV00102", "sid_number": "498", "patient_id": 51, "subtotal": 1200, "discount": 0, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-25", "due_date": "2025-09-24", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-25T12:08:41.088434", "updated_at": "2025-08-25T12:08:41.088435"}, {"id": 103, "invoice_number": "INV00103", "sid_number": "505", "patient_id": 76, "subtotal": 300, "discount": 0, "tax": 54, "total_amount": 80, "paid_amount": 300, "balance": -205.6, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-25", "due_date": "2025-09-24", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-25T12:26:26.013587", "updated_at": "2025-08-29T16:42:58.315503"}, {"id": 104, "invoice_number": "INV00104", "sid_number": "507", "patient_id": 51, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-26", "due_date": "2025-09-25", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-26T11:26:33.413500", "updated_at": "2025-08-26T11:26:33.413502"}, {"id": 105, "invoice_number": "INV00105", "sid_number": "511", "patient_id": 86, "subtotal": 440, "discount": 0, "tax": 79.2, "total_amount": 440, "paid_amount": 440, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-26", "due_date": "2025-09-25", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-26T12:14:11.779280", "updated_at": "2025-08-26T12:14:11.779283"}, {"id": 107, "invoice_number": "INV00107", "sid_number": "518", "patient_id": 86, "subtotal": 440, "discount": 0, "tax": 79.2, "total_amount": 440, "paid_amount": 440, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-28", "due_date": "2025-09-27", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-28T16:38:55.675546", "updated_at": "2025-08-28T16:38:55.675548"}, {"id": 108, "invoice_number": "INV00108", "sid_number": "521", "patient_id": 86, "subtotal": 440, "discount": 0, "tax": 79.2, "total_amount": 3451, "paid_amount": 440, "balance": 3632.18, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-28", "due_date": "2025-09-27", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-28T16:54:31.545616", "updated_at": "2025-08-29T18:03:24.464163"}, {"id": 109, "invoice_number": "INV00001", "sid_number": "MYD001", "patient_id": 6, "subtotal": 2650, "discount": 10.47, "tax": 477, "total_amount": 3116.53, "paid_amount": 3116.53, "balance": 0, "payment_method": "UPI", "payment_status": "paid", "status": "completed", "invoice_date": "2025-04-27", "due_date": "2025-05-27", "tenant_id": 1, "created_by": 2, "created_at": "2025-04-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.340256"}, {"id": 111, "invoice_number": "INV00111", "sid_number": "526", "patient_id": 51, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 4501, "paid_amount": 25, "balance": 5286.18, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-28", "due_date": "2025-09-27", "tenant_id": 1, "created_by": "", "created_at": "2025-08-28T18:39:46.752837", "updated_at": "2025-08-29T15:50:22.270432"}, {"id": 112, "invoice_number": "INV00112", "sid_number": "532", "patient_id": 87, "subtotal": 1500, "discount": 0, "tax": 270, "total_amount": 10, "paid_amount": 1500, "balance": -1488.2, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-29", "due_date": "2025-09-28", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-29T16:45:30.416121", "updated_at": "2025-08-29T16:45:58.627960"}, {"id": 113, "invoice_number": "INV00113", "sid_number": "534", "patient_id": 88, "subtotal": 350, "discount": 0, "tax": 63, "total_amount": 300, "paid_amount": 350, "balance": 4, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-29", "due_date": "2025-09-28", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-29T17:21:44.171636", "updated_at": "2025-08-29T17:22:12.796376"}, {"id": 114, "invoice_number": "INV00114", "sid_number": "537", "patient_id": 67, "subtotal": 20, "discount": 0, "tax": 3.6, "total_amount": 20, "paid_amount": 20, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-29", "due_date": "2025-09-28", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-29T18:53:50.178466", "updated_at": "2025-08-29T18:53:50.178467"}, {"id": 115, "invoice_number": "INV00115", "sid_number": "540", "patient_id": 51, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-29", "due_date": "2025-09-28", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-29T19:07:37.775592", "updated_at": "2025-08-29T19:07:37.775593"}, {"id": 116, "invoice_number": "INV00116", "sid_number": "544", "patient_id": 70, "subtotal": 400, "discount": 0, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-30", "due_date": "2025-09-29", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-30T12:13:13.209479", "updated_at": "2025-08-30T12:13:13.209481"}, {"id": 117, "invoice_number": "INV00117", "sid_number": "546", "patient_id": 86, "subtotal": 440, "discount": 0, "tax": 79.2, "total_amount": 440, "paid_amount": 400, "balance": 40, "payment_method": "", "payment_status": "partial", "status": "active", "invoice_date": "2025-08-30", "due_date": "2025-09-29", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-30T12:15:19.172543", "updated_at": "2025-08-30T12:15:19.172545"}, {"id": 118, "invoice_number": "INV00118", "sid_number": "548", "patient_id": 89, "subtotal": 1200, "discount": 0, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "paid", "status": "active", "invoice_date": "2025-08-30", "due_date": "2025-09-29", "tenant_id": 1, "created_by": 4, "created_at": "2025-08-30T12:38:52.575035", "updated_at": "2025-08-30T12:38:52.575037"}, {"id": 119, "invoice_number": "INV00119", "sid_number": "550", "patient_id": 90, "items": [{"id": 1757143160405, "test_id": 509, "test_master_id": 509, "testName": "HbA1c", "test_name": "HbA1c", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "referralSource": "", "pricingScheme": "", "priceCalculationDetails": {"found": true, "price": 400, "source": "fallback", "reason": "Test not found in pricing configuration", "metadata": {}, "timestamp": "2025-09-06T07:19:14.899Z"}, "department": "BIOCHEMISTRY", "hms_code": "000086", "display_name": "HbA1c", "short_name": "A1c", "international_code": "", "method": "NEPHELOMETRY", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 509, "testName": "HbA1c", "test_profile": "HbA1c", "test_price": 400, "department": "BIOCHEMISTRY", "hmsCode": "000086", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "NEPHELOMETRY", "instructions": null, "notes": "HbA1c level reflects the mean glucose concentration over the previous period (approximately 6-8 weeks) and provides a much better\nindication of long term glycemic control than blood and urine glucose determinations. The American Diabetes Association recommends\nmeasurement of HbA1c every 3 months to determine whether a patient’s metabolic control has remained continuously within the target\nrange.A1C test should be performed at least 2 times a year in patients who are meeting treatment goals (and who have stable glycemic\ncontrol). A1C test should be performed quarterly in patients whose therapy has changed or who are not meeting glycemic goals. Predicting\ndevelopment and progression of diabetic microvascular complications. This assay is not useful in determining day to day glucose control\nand should not be used to replace routine blood glucose testing.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-08-30T18:34:26.779736", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 114, "min_sample_qty": null, "price": 400, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": null, "short_name": "A1c", "source_sheet": "BioChemistry", "specimen_code": 17, "test_code": "000086", "test_done_on": "all", "test_name": "HbA1c", "updated_at": "2025-08-30T18:34:26.779739"}}, {"id": 1757143191762, "test_id": 110, "test_master_id": 110, "testName": "URINE SUGAR", "test_name": "URINE SUGAR", "amount": 10, "price": 10, "test_price": 10, "quantity": 1, "referralSource": "", "pricingScheme": "", "priceCalculationDetails": {"found": true, "price": 10, "source": "fallback", "reason": "Test not found in pricing configuration", "metadata": {}, "timestamp": "2025-09-06T07:19:47.438Z"}, "department": "CLINICAL_PATHOLOGY", "hms_code": "000303", "display_name": "URINE SUGAR", "short_name": "US", "international_code": "", "method": "", "primary_specimen": "URINE", "specimen": "URINE", "container": "<PERSON><PERSON>tainer", "reference_range": "Not Present", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 110, "testName": "URINE SUGAR", "test_profile": "URINE SUGAR", "test_price": 10, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000303", "specimen": "URINE", "container": "<PERSON><PERSON>tainer", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Not Present", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 3, "created_at": "2025-08-30T18:34:26.016638", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 10, "reference_range": "Not Present", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "US", "source_sheet": "Clinical Pathology", "specimen_code": 55, "test_code": "000303", "test_done_on": "all", "test_name": "URINE SUGAR", "updated_at": "2025-08-30T18:34:26.016641"}}], "bill_amount": 410, "other_charges": 0, "discount_percent": 0, "subtotal": 410, "discount": 0, "gst_rate": 18, "gst_amount": 73.8, "tax": 73.8, "total_amount": 410, "paid_amount": 0, "balance": 410, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-09-06", "due_date": "2025-10-06", "notes": "", "branch": 1, "created_at": "2025-09-06T12:50:48.874865", "updated_at": "2025-09-06T12:50:48.874867", "tenant_id": 1, "created_by": 4}]