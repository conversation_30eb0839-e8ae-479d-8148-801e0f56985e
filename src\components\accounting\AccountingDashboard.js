import React, { useState, useEffect } from 'react';
import { <PERSON>, Row, Col, Tabs, Tab, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaCalculator, FaChartLine, FaFileInvoiceDollar, FaUsers, FaWarehouse } from 'react-icons/fa';
import ChartOfAccounts from './ChartOfAccounts';
import JournalEntries from './JournalEntries';
import AccountsPayable from './AccountsPayable';
import AccountsReceivable from './AccountsReceivable';
import FinancialReports from './FinancialReports';
import TaxManagement from './TaxManagement';
import InventoryAccounting from './InventoryAccounting';
import { accountingService } from '../../services/accountingService';

const AccountingDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      // Load summary data for dashboard
      const [healthCheck] = await Promise.all([
        accountingService.healthCheck()
      ]);
      
      setDashboardData({
        status: healthCheck.success ? 'Active' : 'Inactive',
        timestamp: healthCheck.timestamp
      });
    } catch (err) {
      setError('Failed to load accounting dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const DashboardOverview = () => (
    <div>
      <Row className="mb-4">
        <Col>
          <h2 className="mb-3">
            <FaCalculator className="me-2" />
            Accounting & Finance Dashboard
          </h2>
          {error && <Alert variant="danger">{error}</Alert>}
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      ) : (
        <Row>
          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-primary">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaChartLine size={40} className="text-primary mb-3" />
                <Card.Title>Chart of Accounts</Card.Title>
                <Card.Text>
                  Manage your chart of accounts and account structure
                </Card.Text>
                <button 
                  className="btn btn-primary btn-sm"
                  onClick={() => setActiveTab('chart-of-accounts')}
                >
                  View Accounts
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-success">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaFileInvoiceDollar size={40} className="text-success mb-3" />
                <Card.Title>Journal Entries</Card.Title>
                <Card.Text>
                  Create and manage journal entries for transactions
                </Card.Text>
                <button 
                  className="btn btn-success btn-sm"
                  onClick={() => setActiveTab('journal-entries')}
                >
                  View Entries
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-warning">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaUsers size={40} className="text-warning mb-3" />
                <Card.Title>Accounts Payable</Card.Title>
                <Card.Text>
                  Manage vendors, purchase invoices, and payments
                </Card.Text>
                <button 
                  className="btn btn-warning btn-sm"
                  onClick={() => setActiveTab('accounts-payable')}
                >
                  View Payables
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-info">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaUsers size={40} className="text-info mb-3" />
                <Card.Title>Accounts Receivable</Card.Title>
                <Card.Text>
                  Manage customers, sales invoices, and collections
                </Card.Text>
                <button 
                  className="btn btn-info btn-sm"
                  onClick={() => setActiveTab('accounts-receivable')}
                >
                  View Receivables
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-secondary">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaChartLine size={40} className="text-secondary mb-3" />
                <Card.Title>Financial Reports</Card.Title>
                <Card.Text>
                  Generate trial balance, P&L, balance sheet reports
                </Card.Text>
                <button 
                  className="btn btn-secondary btn-sm"
                  onClick={() => setActiveTab('financial-reports')}
                >
                  View Reports
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-danger">
              <Card.Body  style={{background:"#f8f9fa"}} className="text-center">
                <FaCalculator size={40} className="text-danger mb-3" />
                <Card.Title>Tax Management</Card.Title>
                <Card.Text>
                  Calculate GST, TDS, TCS and manage tax compliance
                </Card.Text>
                <button 
                  className="btn btn-danger btn-sm"
                  onClick={() => setActiveTab('tax-management')}
                >
                  Manage Taxes
                </button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card className="h-100 border-dark">
              <Card.Body style={{background:"#f8f9fa"}} className="text-center">
                <FaWarehouse size={40} className="text-dark mb-3" />
                <Card.Title>Inventory Accounting</Card.Title>
                <Card.Text>
                  FIFO/LIFO costing and inventory valuation
                </Card.Text>
                <button 
                  className="btn btn-dark btn-sm"
                  onClick={() => setActiveTab('inventory-accounting')}
                >
                  View Inventory
                </button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {dashboardData && (
        <Row className="mt-4">
          <Col>
            <Card>
              <Card.Body style={{background:"#f8f9fa"}}>
                <Card.Title>System Status</Card.Title>
                <p><strong>Status:</strong> {dashboardData.status}</p>
                <p><strong>Last Updated:</strong> {new Date(dashboardData.timestamp).toLocaleString()}</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );

  return (
    <div className="accounting-dashboard">
      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-3"
      >
        <Tab eventKey="dashboard" title="Dashboard">
          <DashboardOverview />
        </Tab>
        <Tab eventKey="chart-of-accounts" title="Chart of Accounts">
          <ChartOfAccounts />
        </Tab>
        <Tab eventKey="journal-entries" title="Journal Entries">
          <JournalEntries />
        </Tab>
        <Tab eventKey="accounts-payable" title="Accounts Payable">
          <AccountsPayable />
        </Tab>
        <Tab eventKey="accounts-receivable" title="Accounts Receivable">
          <AccountsReceivable />
        </Tab>
        <Tab eventKey="financial-reports" title="Financial Reports">
          <FinancialReports />
        </Tab>
        <Tab eventKey="tax-management" title="Tax Management">
          <TaxManagement />
        </Tab>
        <Tab eventKey="inventory-accounting" title="Inventory Accounting">
          <InventoryAccounting />
        </Tab>
      </Tabs>
    </div>
  );
};

export default AccountingDashboard;
