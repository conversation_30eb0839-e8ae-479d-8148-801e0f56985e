import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Form, Badge, Alert, Modal } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartLine,
  faPlus,
  faSearch,
  faFilter,
  faEye,
  faPaperPlane,
  faBoxOpen,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import ResponsiveDataTable from '../../components/common/ResponsiveDataTable';
import procurementAPI from '../../services/procurementAPI';

const PurchaseOrderList = () => {
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    supplier_id: '',
    start_date: '',
    end_date: ''
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [suppliers, setSuppliers] = useState([]);
  const [showReceiveModal, setShowReceiveModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [receivedItems, setReceivedItems] = useState([]);

  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  const navigate = useNavigate();

  useEffect(() => {
    loadPurchaseOrders();
    loadSuppliers();
  }, [filters, currentPage]);

  const loadPurchaseOrders = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        page: currentPage,
        limit: 20
      };
      
      if (searchQuery) {
        params.search = searchQuery;
      }

      const response = await procurementAPI.getPurchaseOrders(params);
      setPurchaseOrders(response.data.data || []);
      setTotalPages(response.data.total_pages || 1);
    } catch (err) {
      console.error('Error loading purchase orders:', err);
      setError('Failed to load purchase orders');
    } finally {
      setLoading(false);
    }
  };

  const loadSuppliers = async () => {
    try {
      const response = await procurementAPI.getSuppliers();
      setSuppliers(response.data || []);
    } catch (err) {
      console.error('Error loading suppliers:', err);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadPurchaseOrders();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'sent': return 'warning';
      case 'acknowledged': return 'info';
      case 'partial_delivered': return 'warning';
      case 'delivered': return 'success';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  };

  const handleSendOrder = async (orderId) => {
    try {
      await procurementAPI.sendPurchaseOrder(orderId);
      loadPurchaseOrders();
    } catch (err) {
      console.error('Error sending purchase order:', err);
      setError('Failed to send purchase order');
    }
  };

  const handleReceiveClick = (order) => {
    setSelectedOrder(order);
    setReceivedItems(order.items.map(item => ({
      item_id: item.id,
      received_quantity: 0
    })));
    setShowReceiveModal(true);
  };

  const handleReceiveSubmit = async () => {
    try {
      await procurementAPI.receivePurchaseOrderItems(selectedOrder.id, {
        received_items: receivedItems
      });
      
      setShowReceiveModal(false);
      loadPurchaseOrders();
    } catch (err) {
      console.error('Error receiving items:', err);
      setError('Failed to receive items');
    }
  };

  const updateReceivedQuantity = (itemId, quantity) => {
    setReceivedItems(prev => 
      prev.map(item => 
        item.item_id === itemId 
          ? { ...item, received_quantity: parseInt(quantity) || 0 }
          : item
      )
    );
  };

  const canSend = (order) => {
    return order.status === 'draft' && 
           (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin');
  };

  const canReceive = (order) => {
    return order.status === 'sent' && 
           (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin');
  };

  const getSupplierName = (supplierId) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'Unknown Supplier';
  };

  // Table columns
  const columns = [
    {
      key: 'po_number',
      label: 'PO #',
      render: (value, row) => (
        <Link to={`/procurement/orders/${row.id}`} className="text-decoration-none fw-semibold">
          {value}
        </Link>
      )
    },
    {
      key: 'order_date',
      label: 'Date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'supplier_id',
      label: 'Supplier',
      render: (value) => getSupplierName(value)
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge bg={getStatusBadgeVariant(value)} className="text-capitalize">
          {value?.replace('_', ' ')}
        </Badge>
      )
    },
    {
      key: 'total_amount',
      label: 'Amount',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'expected_delivery_date',
      label: 'Expected',
      render: (value) => value ? new Date(value).toLocaleDateString() : '-'
    },
    {
      key: 'items',
      label: 'Items',
      render: (value) => `${value?.length || 0} items`
    }
  ];

  // Mobile card configuration
  const mobileCardConfig = {
    title: (order) => order.po_number,
    subtitle: (order) => `${getSupplierName(order.supplier_id)} • ₹${parseFloat(order.total_amount || 0).toLocaleString()}`,
    primaryField: 'order_date',
    secondaryField: 'expected_delivery_date',
    statusField: 'status'
  };

  const getRowActions = (order) => {
    const actions = [];

    actions.push({
      label: 'View',
      icon: faEye,
      variant: 'outline-primary',
      onClick: () => navigate(`/procurement/orders/${order.id}`)
    });

    if (canSend(order)) {
      actions.push({
        label: 'Send',
        icon: faPaperPlane,
        variant: 'outline-success',
        onClick: () => handleSendOrder(order.id)
      });
    }

    if (canReceive(order)) {
      actions.push({
        label: 'Receive',
        icon: faBoxOpen,
        variant: 'outline-info',
        onClick: () => handleReceiveClick(order)
      });
    }

    return actions;
  };

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faChartLine} className="me-2 text-primary" />
            Purchase Orders
          </h2>
          <p className="text-muted mb-0">Manage supplier purchase orders</p>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header className='text-primary'>
          <FontAwesomeIcon icon={faFilter} className="me-2" />
          Filters
        </Card.Header>
        <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
          <Form onSubmit={handleSearch}>
            <Row>
              <Col md={3} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="acknowledged">Acknowledged</option>
                  <option value="partial_delivered">Partial Delivered</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Supplier</Form.Label>
                <Form.Select
                  value={filters.supplier_id}
                  onChange={(e) => handleFilterChange('supplier_id', e.target.value)}
                >
                  <option value="">All Suppliers</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => handleFilterChange('start_date', e.target.value)}
                />
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => handleFilterChange('end_date', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6} className="mb-3">
                <Form.Label>Search</Form.Label>
                <div className="input-group">
                  <Form.Control
                    type="text"
                    placeholder="Search by PO number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" variant="outline-secondary">
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Data Table */}
      <Card>
        <Card.Body className="p-0">
          <ResponsiveDataTable
            data={purchaseOrders}
            columns={columns}
            loading={loading}
            emptyMessage="No purchase orders found."
            mobileCardConfig={mobileCardConfig}
            getRowActions={getRowActions}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </Card.Body>
      </Card>

      {/* Receive Items Modal */}
      <Modal show={showReceiveModal} onHide={() => setShowReceiveModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Receive Items</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedOrder && (
            <>
              <div className="mb-3">
                <strong>Purchase Order:</strong> {selectedOrder.po_number}<br />
                <strong>Supplier:</strong> {getSupplierName(selectedOrder.supplier_id)}
              </div>
              
              <div className="table-responsive">
                <table className="table table-sm">
                  <thead>
                    <tr>
                      <th>Item</th>
                      <th>Ordered</th>
                      <th>Unit</th>
                      <th>Received</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedOrder.items?.map((item, index) => (
                      <tr key={item.id || index}>
                        <td>{item.item_name}</td>
                        <td>{item.quantity}</td>
                        <td>{item.unit}</td>
                        <td>
                          <Form.Control
                            type="number"
                            min="0"
                            max={item.quantity}
                            value={receivedItems.find(ri => ri.item_id === item.id)?.received_quantity || 0}
                            onChange={(e) => updateReceivedQuantity(item.id, e.target.value)}
                            size="sm"
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReceiveModal(false)}>
            Cancel
          </Button>
          <Button variant="success" onClick={handleReceiveSubmit}>
            Confirm Receipt
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PurchaseOrderList;
