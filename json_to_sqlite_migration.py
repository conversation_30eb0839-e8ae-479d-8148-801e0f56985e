#!/usr/bin/env python3
"""
JSON to SQLite Migration Script for AVINI Labs
Migrates all existing JSON data to SQLite database
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Any

class JSONToSQLiteMigrator:
    def __init__(self, db_path='backend/data/avini_labs.db', json_dir='backend/data'):
        self.db_path = db_path
        self.json_dir = json_dir
        self.migration_log = []
        
    def log_migration(self, table: str, action: str, count: int = 0, error: str = None):
        """Log migration actions"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'table': table,
            'action': action,
            'count': count,
            'error': error
        }
        self.migration_log.append(entry)
        
        if error:
            print(f"✗ {table}: {action} - Error: {error}")
        else:
            print(f"✓ {table}: {action} - {count} records")
    
    def read_json_file(self, filename: str) -> List[Dict]:
        """Read JSON file safely"""
        file_path = os.path.join(self.json_dir, filename)
        
        if not os.path.exists(file_path):
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data if isinstance(data, list) else []
        except Exception as e:
            self.log_migration(filename, 'read_error', error=str(e))
            return []
    
    def migrate_tenants(self, conn: sqlite3.Connection):
        """Migrate tenants data"""
        tenants_data = self.read_json_file('tenants.json')
        if not tenants_data:
            return
        
        cursor = conn.cursor()
        migrated = 0
        
        for tenant in tenants_data:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO tenants (
                        id, name, site_code, address, city, state, pincode,
                        contact_phone, email, contact_person, contact_person_phone,
                        license_number, is_hub, is_active, use_site_code_prefix,
                        established_date, franchise_fee, monthly_fee, commission_rate,
                        notes, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    tenant.get('id'),
                    tenant.get('name'),
                    tenant.get('site_code'),
                    tenant.get('address'),
                    tenant.get('city'),
                    tenant.get('state'),
                    tenant.get('pincode'),
                    tenant.get('contact_phone'),
                    tenant.get('email'),
                    tenant.get('contact_person'),
                    tenant.get('contact_person_phone'),
                    tenant.get('license_number'),
                    tenant.get('is_hub', False),
                    tenant.get('is_active', True),
                    tenant.get('use_site_code_prefix', True),
                    tenant.get('established_date'),
                    tenant.get('franchise_fee'),
                    tenant.get('monthly_fee'),
                    tenant.get('commission_rate'),
                    tenant.get('notes'),
                    tenant.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('tenants', 'insert_error', error=f"Record {tenant.get('id')}: {e}")
        
        self.log_migration('tenants', 'migrated', migrated)
    
    def migrate_users(self, conn: sqlite3.Connection):
        """Migrate users data"""
        users_data = self.read_json_file('users.json')
        if not users_data:
            return

        cursor = conn.cursor()
        migrated = 0

        # Role mapping for data validation
        role_mapping = {
            'lab_tech': 'lab_technician',
            'lab_technician': 'lab_technician',
            'admin': 'admin',
            'hub_admin': 'hub_admin',
            'franchise_admin': 'franchise_admin',
            'receptionist': 'receptionist'
        }

        for user in users_data:
            try:
                # Fix role validation
                original_role = user.get('role', '')
                mapped_role = role_mapping.get(original_role, 'lab_technician')

                cursor.execute("""
                    INSERT OR REPLACE INTO users (
                        id, username, password, email, first_name, last_name,
                        role, tenant_id, is_active, last_login, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user.get('id'),
                    user.get('username'),
                    user.get('password'),
                    user.get('email'),
                    user.get('first_name'),
                    user.get('last_name'),
                    mapped_role,
                    user.get('tenant_id'),
                    user.get('is_active', True),
                    user.get('last_login'),
                    user.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('users', 'insert_error', error=f"Record {user.get('id')}: {e}")

        self.log_migration('users', 'migrated', migrated)
    
    def migrate_patients(self, conn: sqlite3.Connection):
        """Migrate patients data"""
        patients_data = self.read_json_file('patients.json')
        if not patients_data:
            return
        
        cursor = conn.cursor()
        migrated = 0
        
        for patient in patients_data:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO patients (
                        id, patient_id, first_name, last_name, gender, date_of_birth,
                        phone, email, address, city, state, postal_code, blood_group,
                        tenant_id, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    patient.get('id'),
                    patient.get('patient_id'),
                    patient.get('first_name'),
                    patient.get('last_name'),
                    patient.get('gender'),
                    patient.get('date_of_birth'),
                    patient.get('phone'),
                    patient.get('email'),
                    patient.get('address'),
                    patient.get('city'),
                    patient.get('state'),
                    patient.get('postal_code'),
                    patient.get('blood_group'),
                    patient.get('tenant_id'),
                    patient.get('created_by'),
                    patient.get('created_at', datetime.now().isoformat()),
                    patient.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('patients', 'insert_error', error=f"Record {patient.get('id')}: {e}")
        
        self.log_migration('patients', 'migrated', migrated)
    
    def migrate_departments(self, conn: sqlite3.Connection):
        """Migrate departments data"""
        departments_data = self.read_json_file('departments.json')
        if not departments_data:
            return
        
        cursor = conn.cursor()
        migrated = 0
        
        for dept in departments_data:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO departments (
                        id, code, name, description, is_active, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    dept.get('id'),
                    dept.get('code'),
                    dept.get('department', dept.get('test_profile')),  # Handle different field names
                    dept.get('description', ''),
                    dept.get('is_active', True),
                    dept.get('created_at', datetime.now().isoformat()),
                    dept.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('departments', 'insert_error', error=f"Record {dept.get('id')}: {e}")
        
        self.log_migration('departments', 'migrated', migrated)
    
    def migrate_test_master(self, conn: sqlite3.Connection):
        """Migrate test master data"""
        test_data = self.read_json_file('test_master.json')
        if not test_data:
            return

        cursor = conn.cursor()
        migrated = 0

        for test in test_data:
            try:
                # Get department ID by name
                dept_name = test.get('department')
                dept_id = None
                if dept_name:
                    cursor.execute("SELECT id FROM departments WHERE name = ? OR code = ?", (dept_name, dept_name))
                    dept_result = cursor.fetchone()
                    if dept_result:
                        dept_id = dept_result[0]

                # Handle specimen field - convert list to string if needed
                specimen = test.get('specimen')
                if isinstance(specimen, list):
                    specimen = ', '.join(str(s) for s in specimen)
                elif specimen is None:
                    specimen = test.get('container', '')

                # Handle reportingDays field - convert list to string if needed
                reporting_days = test.get('reportingDays')
                if isinstance(reporting_days, list):
                    reporting_days = ', '.join(str(d) for d in reporting_days)
                elif reporting_days is None:
                    reporting_days = ''

                # Handle testDoneOn field - convert list to string if needed
                test_done_on = test.get('testDoneOn')
                if isinstance(test_done_on, list):
                    test_done_on = ', '.join(str(d) for d in test_done_on)
                elif isinstance(test_done_on, dict):
                    # Handle case where testDoneOn is an object/dict
                    test_done_on = ', '.join(f"{k}: {v}" for k, v in test_done_on.items()) if test_done_on else ''
                elif test_done_on is None:
                    test_done_on = ''

                # Handle unacceptableConditions field - convert list to string if needed
                unacceptable_conditions = test.get('unacceptableConditions')
                if isinstance(unacceptable_conditions, list):
                    unacceptable_conditions = ', '.join(str(c) for c in unacceptable_conditions)
                elif unacceptable_conditions is None:
                    unacceptable_conditions = ''

                cursor.execute("""
                    INSERT OR REPLACE INTO test_master (
                        id, test_name, test_code, hms_code, department_id, short_name,
                        display_name, method, specimen, reference_range, result_unit,
                        decimals, test_price, critical_low, critical_high, instructions,
                        interpretation, notes, min_sample_qty, service_time, reporting_days,
                        cutoff_time, emergency_process_time, emergency_process_period,
                        expiry_time, expiry_period, test_done_on, applicable_to,
                        emr_classification, international_code, alert_message, alert_period,
                        alert_sms, special_report, unacceptable_conditions, is_active,
                        created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    test.get('id'),
                    test.get('testName'),
                    test.get('test_code'),
                    test.get('hmsCode'),
                    dept_id,
                    test.get('shortName'),
                    test.get('displayName'),
                    test.get('method'),
                    specimen,
                    test.get('reference_range'),
                    test.get('result_unit'),
                    test.get('decimals', 2),
                    test.get('test_price'),
                    test.get('critical_low'),
                    test.get('critical_high'),
                    test.get('instructions'),
                    test.get('interpretation'),
                    test.get('notes'),
                    test.get('minSampleQty'),
                    test.get('serviceTime'),
                    reporting_days,
                    test.get('cutoffTime'),
                    test.get('emergencyProcessTime'),
                    test.get('emergencyProcessPeriod'),
                    test.get('expiryTime'),
                    test.get('expiryPeriod'),
                    test_done_on,
                    test.get('applicableTo'),
                    test.get('emrClassification'),
                    test.get('internationalCode'),
                    test.get('alertMessage'),
                    test.get('alertPeriod'),
                    test.get('alertSMS', False),
                    test.get('specialReport', False),
                    unacceptable_conditions,
                    test.get('is_active', True),
                    test.get('created_by'),
                    test.get('created_at', datetime.now().isoformat()),
                    test.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('test_master', 'insert_error', error=f"Record {test.get('id')}: {e}")

        self.log_migration('test_master', 'migrated', migrated)
    
    def migrate_samples(self, conn: sqlite3.Connection):
        """Migrate samples data"""
        samples_data = self.read_json_file('samples.json')
        if not samples_data:
            return
        
        cursor = conn.cursor()
        migrated = 0
        
        # Status mapping for data validation
        status_mapping = {
            'Received': 'collected',
            'received': 'collected',
            'Collected': 'collected',
            'collected': 'collected',
            'Processed': 'processing',
            'processed': 'processing',
            'Processing': 'processing',
            'processing': 'processing',
            'In Transit': 'processing',
            'in_transit': 'processing',
            'Completed': 'completed',
            'completed': 'completed',
            'Rejected': 'rejected',
            'rejected': 'rejected'
        }

        for sample in samples_data:
            try:
                # Fix status validation
                original_status = sample.get('status', 'collected')
                mapped_status = status_mapping.get(original_status, 'collected')

                cursor.execute("""
                    INSERT OR REPLACE INTO samples (
                        id, sample_id, patient_id, sample_type_id, container_id,
                        collection_date, collection_time, status, tenant_id,
                        collected_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    sample.get('id'),
                    sample.get('sample_id'),
                    sample.get('patient_id'),
                    sample.get('sample_type_id'),
                    sample.get('container_id'),
                    sample.get('collection_date'),
                    sample.get('collection_time'),
                    mapped_status,
                    sample.get('tenant_id'),
                    sample.get('collected_by'),
                    sample.get('created_at', datetime.now().isoformat()),
                    sample.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('samples', 'insert_error', error=f"Record {sample.get('id')}: {e}")
        
        self.log_migration('samples', 'migrated', migrated)
    
    def migrate_billings(self, conn: sqlite3.Connection):
        """Migrate billings data"""
        billings_data = self.read_json_file('billings.json')
        if not billings_data:
            return

        cursor = conn.cursor()
        migrated = 0

        # Payment status mapping for data validation
        payment_status_mapping = {
            'Paid': 'paid',
            'paid': 'paid',
            'Pending': 'pending',
            'pending': 'pending',
            'Partial': 'partial',
            'partial': 'partial',
            'Refunded': 'refunded',
            'refunded': 'refunded'
        }

        # Status mapping for data validation
        status_mapping = {
            'Paid': 'completed',
            'paid': 'completed',
            'Pending': 'active',
            'pending': 'active',
            'Partial': 'active',
            'partial': 'active',
            'active': 'active',
            'cancelled': 'cancelled',
            'completed': 'completed'
        }

        for billing in billings_data:
            try:
                # Fix payment status validation
                original_payment_status = billing.get('payment_status', 'pending')
                mapped_payment_status = payment_status_mapping.get(original_payment_status, 'pending')

                # Fix status validation
                original_status = billing.get('status', 'active')
                mapped_status = status_mapping.get(original_status, 'active')

                # Insert billing record
                cursor.execute("""
                    INSERT OR REPLACE INTO billings (
                        id, invoice_number, sid_number, patient_id, subtotal, discount,
                        tax, total_amount, paid_amount, balance, payment_method,
                        payment_status, status, invoice_date, due_date, tenant_id,
                        created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    billing.get('id'),
                    billing.get('invoice_number'),
                    billing.get('sid_number'),
                    billing.get('patient_id'),
                    billing.get('subtotal', 0),
                    billing.get('discount', 0),
                    billing.get('tax', 0),
                    billing.get('total_amount'),
                    billing.get('paid_amount', 0),
                    billing.get('balance', 0),
                    billing.get('payment_method'),
                    mapped_payment_status,
                    mapped_status,
                    billing.get('invoice_date'),
                    billing.get('due_date'),
                    billing.get('tenant_id'),
                    billing.get('created_by'),
                    billing.get('created_at', datetime.now().isoformat()),
                    billing.get('updated_at', datetime.now().isoformat())
                ))

                # Insert billing items
                items = billing.get('items', [])
                for item in items:
                    # Handle missing price values
                    price = item.get('price')
                    if price is None:
                        price = item.get('amount', 0)  # Use amount as fallback

                    amount = item.get('amount')
                    if amount is None:
                        amount = price * item.get('quantity', 1)  # Calculate amount

                    cursor.execute("""
                        INSERT OR REPLACE INTO billing_items (
                            id, billing_id, test_id, test_name, quantity, price, amount
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item.get('id'),
                        billing.get('id'),
                        item.get('test_id'),
                        item.get('test_name'),
                        item.get('quantity', 1),
                        price,
                        amount
                    ))

                migrated += 1
            except Exception as e:
                self.log_migration('billings', 'insert_error', error=f"Record {billing.get('id')}: {e}")

        self.log_migration('billings', 'migrated', migrated)
    
    def migrate_results(self, conn: sqlite3.Connection):
        """Migrate results data"""
        results_data = self.read_json_file('results.json')
        if not results_data:
            return

        cursor = conn.cursor()
        migrated = 0

        # Status mapping for data validation
        status_mapping = {
            'Pending': 'pending',
            'pending': 'pending',
            'Completed': 'completed',
            'completed': 'completed',
            'Verified': 'verified',
            'verified': 'verified',
            'Rejected': 'rejected',
            'rejected': 'rejected'
        }

        for result in results_data:
            try:
                # Fix status validation
                original_status = result.get('status', 'pending')
                mapped_status = status_mapping.get(original_status, 'pending')

                cursor.execute("""
                    INSERT OR REPLACE INTO results (
                        id, result_id, sample_id, test_id, value, unit, reference_range,
                        status, result_date, tenant_id, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    result.get('id'),
                    result.get('result_id'),
                    result.get('sample_id'),
                    result.get('test_id'),
                    result.get('value'),
                    result.get('unit'),
                    result.get('reference_range'),
                    mapped_status,
                    result.get('result_date'),
                    result.get('tenant_id'),
                    result.get('created_by'),
                    result.get('created_at', datetime.now().isoformat()),
                    result.get('updated_at', datetime.now().isoformat())
                ))
                migrated += 1
            except Exception as e:
                self.log_migration('results', 'insert_error', error=f"Record {result.get('id')}: {e}")

        self.log_migration('results', 'migrated', migrated)
    
    def migrate_all(self):
        """Migrate all JSON data to SQLite"""
        print("Starting JSON to SQLite Migration")
        print("=" * 50)
        
        if not os.path.exists(self.db_path):
            print(f"✗ Database not found at {self.db_path}")
            print("Please run database_init.py first to create the database")
            return False
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Disable foreign key constraints during migration
            conn.execute("PRAGMA foreign_keys = OFF")
            
            # Migrate in dependency order
            self.migrate_tenants(conn)
            self.migrate_users(conn)
            self.migrate_patients(conn)
            self.migrate_departments(conn)
            self.migrate_test_master(conn)
            self.migrate_samples(conn)
            self.migrate_billings(conn)
            self.migrate_results(conn)
            
            # Re-enable foreign key constraints
            conn.execute("PRAGMA foreign_keys = ON")
            
            conn.commit()
            
            # Save migration log
            with open('migration_log.json', 'w') as f:
                json.dump(self.migration_log, f, indent=2)
            
            print("\n" + "=" * 50)
            print("✓ Migration completed successfully!")
            print(f"Migration log saved to: migration_log.json")
            
            return True
            
        except Exception as e:
            print(f"\n✗ Migration failed: {e}")
            conn.rollback()
            return False
        
        finally:
            conn.close()

def main():
    """Main function"""
    migrator = JSONToSQLiteMigrator()
    migrator.migrate_all()

if __name__ == "__main__":
    main()
