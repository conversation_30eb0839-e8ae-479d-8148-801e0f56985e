-- Fix inventory and storerooms table schema for procurement delivery functionality
-- This addresses the 500 error in delivery confirmation

-- Add missing columns to inventory table
ALTER TABLE inventory ADD COLUMN storeroom_id INTEGER;
ALTER TABLE inventory ADD COLUMN is_active INTEGER DEFAULT 1;

-- Add missing columns to storerooms table  
ALTER TABLE storerooms ADD COLUMN is_active INTEGER DEFAULT 1;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_inventory_storeroom ON inventory(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_inventory_active ON inventory(is_active);
CREATE INDEX IF NOT EXISTS idx_storerooms_tenant_active ON storerooms(tenant_id, is_active);

-- Update existing inventory records to have a default storeroom
-- We'll set storeroom_id to 1 for existing records as a default
UPDATE inventory SET storeroom_id = 1 WHERE storeroom_id IS NULL;

-- Ensure all existing storerooms are marked as active
UPDATE storerooms SET is_active = 1 WHERE is_active IS NULL;
