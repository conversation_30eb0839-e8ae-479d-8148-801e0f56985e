"""
AVINI Labs Accounting Integration Service
Integrates accounting module with existing procurement and billing systems
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
import json

from database_manager import db_manager
from services.accounting_service import AccountingService
from services.accounts_receivable_service import AccountsReceivableService
from services.accounts_payable_service import AccountsPayableService
from utils import read_data, write_data

logger = logging.getLogger(__name__)

class AccountingIntegrationService:
    """Service for integrating accounting with existing systems"""
    
    def __init__(self):
        self.db = db_manager
        self.accounting_service = AccountingService()
        self.ar_service = AccountsReceivableService()
        self.ap_service = AccountsPayableService()
    
    # ============================================================================
    # BILLING INTEGRATION
    # ============================================================================
    
    def sync_billing_to_accounting(self, tenant_id: int, created_by: int) -> Dict:
        """Sync existing billing data to accounting system"""
        try:
            # Read existing billing data
            billings = read_data('billings.json')
            
            # Filter by tenant
            tenant_billings = [b for b in billings if b.get('tenant_id') == tenant_id]
            
            results = {
                'processed': 0,
                'errors': 0,
                'customers_created': 0,
                'invoices_created': 0,
                'journal_entries_created': 0
            }
            
            # Process each billing record
            for billing in tenant_billings:
                try:
                    # Create customer if not exists
                    customer_code = f"PAT{billing['patient_id']:04d}"
                    existing_customer = self.ar_service.get_customer_by_code(customer_code, tenant_id)
                    
                    if not existing_customer:
                        customer_data = {
                            'customer_code': customer_code,
                            'customer_name': f"Patient {billing['patient_id']}",
                            'customer_type': 'INDIVIDUAL',
                            'payment_terms': 'IMMEDIATE',
                            'credit_limit': 0,
                            'credit_days': 30,
                            'tenant_id': tenant_id,
                            'is_active': True
                        }
                        customer_id = self.ar_service.create_customer(customer_data)
                        results['customers_created'] += 1
                    else:
                        customer_id = existing_customer['id']
                    
                    # Create sales invoice
                    invoice_data = {
                        'customer_id': customer_id,
                        'invoice_number': billing['invoice_number'],
                        'invoice_date': billing['invoice_date'],
                        'due_date': billing['due_date'],
                        'subtotal': billing['subtotal'],
                        'discount_amount': billing.get('discount', 0),
                        'tax_amount': billing.get('tax', 0),
                        'total_amount': billing['total_amount'],
                        'paid_amount': billing.get('paid_amount', 0),
                        'status': 'SENT' if billing.get('status') == 'active' else 'DRAFT',
                        'tenant_id': tenant_id,
                        'reference_type': 'BILLING',
                        'reference_id': billing['id'],
                        'sid_number': billing.get('sid_number'),
                        'created_by': created_by
                    }
                    
                    # Create line items (simplified - single line for total)
                    line_items = [{
                        'line_number': 1,
                        'item_description': f"Medical Services - {billing.get('sid_number', billing['invoice_number'])}",
                        'quantity': 1,
                        'unit_price': billing['subtotal'],
                        'line_total': billing['subtotal']
                    }]
                    
                    invoice_id = self.ar_service.create_sales_invoice(invoice_data, line_items)
                    results['invoices_created'] += 1
                    
                    # Post the invoice if it's active
                    if billing.get('status') == 'active':
                        self.ar_service.post_sales_invoice(invoice_id, created_by)
                        results['journal_entries_created'] += 1
                    
                    results['processed'] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing billing {billing['id']}: {str(e)}")
                    results['errors'] += 1
            
            logger.info(f"Billing sync completed for tenant {tenant_id}: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error syncing billing to accounting: {str(e)}")
            raise
    
    # ============================================================================
    # PROCUREMENT INTEGRATION
    # ============================================================================
    
    def sync_procurement_to_accounting(self, tenant_id: int, created_by: int) -> Dict:
        """Sync existing procurement data to accounting system"""
        try:
            # Get procurement data from database
            purchase_orders = self.db.execute_query(
                "SELECT * FROM purchase_orders WHERE tenant_id = ?",
                (tenant_id,)
            )
            
            results = {
                'processed': 0,
                'errors': 0,
                'vendors_created': 0,
                'invoices_created': 0,
                'journal_entries_created': 0
            }
            
            # Process each purchase order
            for po in purchase_orders:
                try:
                    # Create vendor if not exists
                    vendor_code = f"VEN{po['id']:04d}"
                    existing_vendor = self.ap_service.get_vendor_by_code(vendor_code, tenant_id)
                    
                    if not existing_vendor:
                        vendor_data = {
                            'vendor_code': vendor_code,
                            'vendor_name': po.get('supplier_name', f"Vendor {po['id']}"),
                            'vendor_type': 'SUPPLIER',
                            'payment_terms': 'NET_30',
                            'credit_limit': 0,
                            'credit_days': 30,
                            'tenant_id': tenant_id,
                            'is_active': True
                        }
                        vendor_id = self.ap_service.create_vendor(vendor_data)
                        results['vendors_created'] += 1
                    else:
                        vendor_id = existing_vendor['id']
                    
                    # Create purchase invoice if PO is completed
                    if po.get('status') == 'COMPLETED':
                        invoice_data = {
                            'vendor_id': vendor_id,
                            'invoice_number': f"PI-{po['po_number']}",
                            'invoice_date': po.get('delivery_date', po['created_at'][:10]),
                            'due_date': po.get('delivery_date', po['created_at'][:10]),
                            'total_amount': po.get('total_amount', 0),
                            'status': 'RECEIVED',
                            'tenant_id': tenant_id,
                            'reference_type': 'PURCHASE_ORDER',
                            'reference_id': po['id'],
                            'po_number': po['po_number'],
                            'created_by': created_by
                        }
                        
                        # Get line items
                        line_items = self.db.execute_query(
                            "SELECT * FROM purchase_order_items WHERE purchase_order_id = ?",
                            (po['id'],)
                        )
                        
                        invoice_line_items = []
                        for i, item in enumerate(line_items, 1):
                            invoice_line_items.append({
                                'line_number': i,
                                'item_description': item.get('item_name', f"Item {item['id']}"),
                                'quantity': item.get('quantity', 1),
                                'unit_price': item.get('unit_price', 0),
                                'line_total': item.get('total_price', 0)
                            })
                        
                        if invoice_line_items:
                            invoice_id = self.ap_service.create_purchase_invoice(invoice_data, invoice_line_items)
                            results['invoices_created'] += 1

                            # Approve the invoice
                            self.ap_service.approve_purchase_invoice(invoice_id, created_by)
                            results['journal_entries_created'] += 1
                    
                    results['processed'] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing purchase order {po['id']}: {str(e)}")
                    results['errors'] += 1
            
            logger.info(f"Procurement sync completed for tenant {tenant_id}: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error syncing procurement to accounting: {str(e)}")
            raise
    
    # ============================================================================
    # FINANCIAL PERIOD SETUP
    # ============================================================================
    
    def setup_financial_periods(self, tenant_id: int, year: int, created_by: int) -> List[int]:
        """Setup financial periods for a year"""
        try:
            period_ids = []
            
            # Create 12 monthly periods
            for month in range(1, 13):
                if month == 12:
                    start_date = date(year, month, 1)
                    end_date = date(year + 1, 1, 1) - timedelta(days=1)
                else:
                    start_date = date(year, month, 1)
                    end_date = date(year, month + 1, 1) - timedelta(days=1)
                
                period_data = {
                    'period_name': f"{start_date.strftime('%B')} {year}",
                    'period_code': f"{year}{month:02d}",
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'fiscal_year': year,
                    'period_type': 'MONTHLY',
                    'status': 'OPEN',
                    'is_current': month == datetime.now().month and year == datetime.now().year,
                    'tenant_id': tenant_id,
                    'created_by': created_by
                }
                
                period_id = self.db.insert_record('financial_periods', period_data)
                period_ids.append(period_id)
            
            logger.info(f"Created {len(period_ids)} financial periods for tenant {tenant_id}, year {year}")
            return period_ids
            
        except Exception as e:
            logger.error(f"Error setting up financial periods: {str(e)}")
            raise
    
    # ============================================================================
    # COMPLETE SETUP
    # ============================================================================
    
    def complete_accounting_setup(self, tenant_id: int, created_by: int) -> Dict:
        """Complete accounting setup for a tenant"""
        try:
            results = {
                'financial_periods_created': 0,
                'billing_sync': {},
                'procurement_sync': {}
            }
            
            # Setup financial periods for current year
            current_year = datetime.now().year
            period_ids = self.setup_financial_periods(tenant_id, current_year, created_by)
            results['financial_periods_created'] = len(period_ids)
            
            # Sync billing data
            results['billing_sync'] = self.sync_billing_to_accounting(tenant_id, created_by)
            
            # Sync procurement data
            results['procurement_sync'] = self.sync_procurement_to_accounting(tenant_id, created_by)
            
            logger.info(f"Complete accounting setup finished for tenant {tenant_id}: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error in complete accounting setup: {str(e)}")
            raise
