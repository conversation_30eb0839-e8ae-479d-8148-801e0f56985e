import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Form, Badge, Alert, Modal } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileInvoiceDollar,
  faPlus,
  faSearch,
  faFilter,
  faEye,
  faCreditCard,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import ResponsiveDataTable from '../../components/common/ResponsiveDataTable';
import procurementAPI from '../../services/procurementAPI';

const ProformaInvoiceList = () => {
  const [proformaInvoices, setProformaInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    payment_terms: '',
    start_date: '',
    end_date: ''
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [paymentData, setPaymentData] = useState({
    payment_amount: '',
    payment_method: 'cash',
    reference_number: '',
    notes: ''
  });

  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  const navigate = useNavigate();

  useEffect(() => {
    loadProformaInvoices();
  }, [filters, currentPage]);

  const loadProformaInvoices = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        page: currentPage,
        limit: 20
      };
      
      if (searchQuery) {
        params.search = searchQuery;
      }

      const response = await procurementAPI.getProformaInvoices(params);
      setProformaInvoices(response.data.data || []);
      setTotalPages(response.data.total_pages || 1);
    } catch (err) {
      console.error('Error loading proforma invoices:', err);
      setError('Failed to load proforma invoices');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadProformaInvoices();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'sent': return 'warning';
      case 'approved': return 'info';
      case 'paid': return 'success';
      case 'partial_paid': return 'warning';
      case 'overdue': return 'danger';
      default: return 'secondary';
    }
  };

  const getPaymentTermsBadgeVariant = (terms) => {
    switch (terms) {
      case 'prepaid': return 'success';
      case 'credit': return 'info';
      default: return 'secondary';
    }
  };

  const handlePaymentClick = (invoice) => {
    setSelectedInvoice(invoice);
    setPaymentData({
      payment_amount: invoice.balance_amount || '',
      payment_method: 'cash',
      reference_number: '',
      notes: ''
    });
    setShowPaymentModal(true);
  };

  const handlePaymentSubmit = async () => {
    try {
      await procurementAPI.createPaymentTransaction({
        proforma_invoice_id: selectedInvoice.id,
        ...paymentData,
        payment_amount: parseFloat(paymentData.payment_amount)
      });
      
      setShowPaymentModal(false);
      loadProformaInvoices();
    } catch (err) {
      console.error('Error processing payment:', err);
      setError('Failed to process payment');
    }
  };

  const canMakePayment = (invoice) => {
    return invoice.status !== 'paid' && 
           parseFloat(invoice.balance_amount || 0) > 0 &&
           (currentUser?.role === 'admin' || 
            currentUser?.role === 'hub_admin' || 
            invoice.to_tenant_id === currentUser?.tenant_id);
  };

  // Table columns
  const columns = [
    {
      key: 'proforma_number',
      label: 'Proforma #',
      render: (value, row) => (
        <Link to={`/procurement/proforma/${row.id}`} className="text-decoration-none fw-semibold">
          {value}
        </Link>
      )
    },
    {
      key: 'invoice_date',
      label: 'Date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'payment_terms',
      label: 'Terms',
      render: (value) => (
        <Badge bg={getPaymentTermsBadgeVariant(value)} className="text-capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge bg={getStatusBadgeVariant(value)} className="text-capitalize">
          {value?.replace('_', ' ')}
        </Badge>
      )
    },
    {
      key: 'total_amount',
      label: 'Total',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'paid_amount',
      label: 'Paid',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'balance_amount',
      label: 'Balance',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'payment_due_date',
      label: 'Due Date',
      render: (value) => value ? new Date(value).toLocaleDateString() : '-'
    }
  ];

  // Mobile card configuration
  const mobileCardConfig = {
    title: (invoice) => invoice.proforma_number,
    subtitle: (invoice) => `${invoice.payment_terms} • ₹${parseFloat(invoice.total_amount || 0).toLocaleString()}`,
    primaryField: 'invoice_date',
    secondaryField: 'balance_amount',
    statusField: 'status'
  };

  const getRowActions = (invoice) => {
    const actions = [];

    actions.push({
      label: 'View',
      icon: faEye,
      variant: 'outline-primary',
      onClick: () => navigate(`/procurement/proforma/${invoice.id}`)
    });

    if (canMakePayment(invoice)) {
      actions.push({
        label: 'Pay',
        icon: faCreditCard,
        variant: 'outline-success',
        onClick: () => handlePaymentClick(invoice)
      });
    }

    return actions;
  };

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2 text-primary" />
            Proforma Invoices
          </h2>
          <p className="text-muted mb-0">Manage proforma invoices and payments</p>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header className='text-primary'>
          <FontAwesomeIcon icon={faFilter} className="me-2" />
          Filters
        </Card.Header>
        <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
          <Form onSubmit={handleSearch}>
            <Row>
              <Col md={3} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="approved">Approved</option>
                  <option value="paid">Paid</option>
                  <option value="partial_paid">Partial Paid</option>
                  <option value="overdue">Overdue</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Payment Terms</Form.Label>
                <Form.Select
                  value={filters.payment_terms}
                  onChange={(e) => handleFilterChange('payment_terms', e.target.value)}
                >
                  <option value="">All Terms</option>
                  <option value="prepaid">Prepaid</option>
                  <option value="credit">Credit</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => handleFilterChange('start_date', e.target.value)}
                />
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => handleFilterChange('end_date', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6} className="mb-3">
                <Form.Label>Search</Form.Label>
                <div className="input-group">
                  <Form.Control
                    type="text"
                    placeholder="Search by proforma number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" variant="outline-secondary">
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Data Table */}
      <Card>
        <Card.Body className="p-0">
          <ResponsiveDataTable
            data={proformaInvoices}
            columns={columns}
            loading={loading}
            emptyMessage="No proforma invoices found."
            mobileCardConfig={mobileCardConfig}
            getRowActions={getRowActions}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </Card.Body>
      </Card>

      {/* Payment Modal */}
      <Modal show={showPaymentModal} onHide={() => setShowPaymentModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Record Payment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedInvoice && (
            <>
              <div className="mb-3">
                <strong>Proforma Invoice:</strong> {selectedInvoice.proforma_number}<br />
                <strong>Total Amount:</strong> ₹{parseFloat(selectedInvoice.total_amount || 0).toLocaleString()}<br />
                <strong>Balance Amount:</strong> ₹{parseFloat(selectedInvoice.balance_amount || 0).toLocaleString()}
              </div>
              
              <Form>
                <Row>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Payment Amount <span className="text-danger">*</span></Form.Label>
                      <Form.Control
                        type="number"
                        step="0.01"
                        min="0"
                        max={selectedInvoice.balance_amount}
                        value={paymentData.payment_amount}
                        onChange={(e) => setPaymentData(prev => ({...prev, payment_amount: e.target.value}))}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Payment Method <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        value={paymentData.payment_method}
                        onChange={(e) => setPaymentData(prev => ({...prev, payment_method: e.target.value}))}
                        required
                      >
                        <option value="cash">Cash</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cheque">Cheque</option>
                        <option value="upi">UPI</option>
                        <option value="card">Card</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col className="mb-3">
                    <Form.Group>
                      <Form.Label>Reference Number</Form.Label>
                      <Form.Control
                        type="text"
                        value={paymentData.reference_number}
                        onChange={(e) => setPaymentData(prev => ({...prev, reference_number: e.target.value}))}
                        placeholder="Transaction/Cheque number"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col className="mb-3">
                    <Form.Group>
                      <Form.Label>Notes</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={2}
                        value={paymentData.notes}
                        onChange={(e) => setPaymentData(prev => ({...prev, notes: e.target.value}))}
                        placeholder="Additional notes..."
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Form>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPaymentModal(false)}>
            Cancel
          </Button>
          <Button variant="success" onClick={handlePaymentSubmit}>
            Record Payment
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ProformaInvoiceList;
