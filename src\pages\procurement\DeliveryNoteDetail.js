import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, <PERSON>, Badge, <PERSON><PERSON>, Al<PERSON>, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTruck,
  faArrowLeft,
  faCalendar,
  faMapMarkerAlt,
  faUser,
  faBoxes,
  faExclamationTriangle,
  faEdit,
  faCheck,
  faTimes,
  faFilePdf,
  faDownload
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import DeliveryNoteStatusManager from '../../components/procurement/DeliveryNoteStatusManager';

const DeliveryNoteDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [deliveryNote, setDeliveryNote] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDeliveryNote();
  }, [id]);

  const loadDeliveryNote = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getDeliveryNote(id);
      setDeliveryNote(response.data);
    } catch (err) {
      console.error('Error loading delivery note:', err);
      setError(err.response?.data?.message || 'Failed to load delivery note');
    } finally {
      setLoading(false);
    }
  };

  const downloadPDF = async () => {
    try {
      const response = await procurementAPI.downloadDeliveryNotePDF(id);

      // Create blob from response
      const blob = new Blob([response.data], { type: 'application/pdf' });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `delivery_note_${deliveryNote?.delivery_number || id}.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (err) {
      console.error('Error downloading PDF:', err);
      // Handle specific error messages from backend
      if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError('Failed to download PDF');
      }
    }
  };

  // Check if PDF download is available based on hub status
  const isPDFAvailable = () => {
    const hubStatus = deliveryNote?.hub_status || 'prepared';
    const allowedPDFStatuses = ['dispatched', 'in_transit', 'delivered', 'returned', 'reprocessed'];
    return allowedPDFStatuses.includes(hubStatus);
  };

  // Get PDF button tooltip message
  const getPDFButtonTooltip = () => {
    if (isPDFAvailable()) {
      return "Download courier-friendly PDF (no pricing)";
    }
    const currentStatus = deliveryNote?.hub_status || 'prepared';
    return `PDF not available. Delivery note must be dispatched first. Current status: ${currentStatus}`;
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'secondary';
      case 'pending': return 'warning';
      case 'prepared': return 'secondary';
      case 'dispatched': return 'primary';
      case 'in_transit': return 'info';
      case 'delivered': return 'success';
      case 'received': return 'success';
      case 'cancelled': return 'danger';
      case 'returned': return 'warning';
      case 'reprocessed': return 'info';
      case 'partially_delivered': return 'warning';
      case 'pending_receipt': return 'secondary';
      case 'confirmed': return 'success';
      case 'rejected': return 'danger';
      case 'partially_accepted': return 'warning';
      default: return 'secondary';
    }
  };

  const getHubStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'prepared': return 'secondary';
      case 'dispatched': return 'primary';
      case 'in_transit': return 'info';
      case 'delivered': return 'success';
      case 'cancelled': return 'danger';
      case 'returned': return 'warning';
      case 'reprocessed': return 'info';
      case 'partially_delivered': return 'warning';
      default: return 'secondary';
    }
  };

  const getFranchiseStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending_receipt': return 'secondary';
      case 'received': return 'info';
      case 'confirmed': return 'success';
      case 'rejected': return 'danger';
      case 'partially_accepted': return 'warning';
      default: return 'secondary';
    }
  };

  const getDeliveryTypeBadgeVariant = (type) => {
    switch (type?.toLowerCase()) {
      case 'purchase_order': return 'primary';
      case 'transfer': return 'info';
      case 'return': return 'warning';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading delivery note details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/deliveries')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Delivery Notes
        </Button>
      </Container>
    );
  }

  if (!deliveryNote) {
    return (
      <Container fluid className="py-4">
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Delivery note not found
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/deliveries')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Delivery Notes
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <div className="d-flex gap-2 mb-2">
            <Button
              variant="outline-primary"
              onClick={() => navigate('/procurement/deliveries')}
            >
              <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
              Back to Delivery Notes
            </Button>
            <Button
              variant={isPDFAvailable() ? "outline-success" : "outline-secondary"}
              onClick={downloadPDF}
              disabled={!isPDFAvailable()}
              title={getPDFButtonTooltip()}
              className={!isPDFAvailable() ? "opacity-50" : ""}
            >
              <FontAwesomeIcon icon={faFilePdf} className="me-2" />
              {isPDFAvailable() ? "Download PDF" : "PDF Not Available"}
            </Button>
          </div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
            Delivery Note Details
          </h2>
          <p className="text-muted mb-0">View delivery note information and items</p>
        </div>
        <div>
          {/* Legacy Status */}
          <Badge bg={getStatusBadgeVariant(deliveryNote.status)} className="fs-6 me-2">
            {deliveryNote.status?.toUpperCase()}
          </Badge>

          {/* Hub Status */}
          {deliveryNote.hub_status && (
            <Badge bg={getHubStatusBadgeVariant(deliveryNote.hub_status)} className="fs-6 me-2">
              Hub: {deliveryNote.hub_status?.replace('_', ' ').toUpperCase()}
            </Badge>
          )}

          {/* Franchise Status */}
          {deliveryNote.franchise_status && (
            <Badge bg={getFranchiseStatusBadgeVariant(deliveryNote.franchise_status)} className="fs-6 me-2">
              Franchise: {deliveryNote.franchise_status?.replace('_', ' ').toUpperCase()}
            </Badge>
          )}

          <Badge bg={getDeliveryTypeBadgeVariant(deliveryNote.delivery_type)} className="fs-6">
            {deliveryNote.delivery_type?.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </div>

      <Row>
        {/* Delivery Note Information */}
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faTruck} className="me-2" />
                Delivery Information
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Delivery Number</label>
                    <div className="fw-semibold">{deliveryNote.delivery_number}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Delivery Date
                    </label>
                    <div>{new Date(deliveryNote.delivery_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Delivery Type</label>
                    <div>
                      <Badge bg={getDeliveryTypeBadgeVariant(deliveryNote.delivery_type)}>
                        {deliveryNote.delivery_type?.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faTruck} className="me-1" />
                      Mode of Transport
                    </label>
                    <div>{deliveryNote.mode_of_transport || 'Road Transport'}</div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Status</label>
                    <div>
                      <Badge bg={getStatusBadgeVariant(deliveryNote.status)}>
                        {deliveryNote.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                      Delivery Address
                    </label>
                    <div>{deliveryNote.delivery_address || 'Not specified'}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Total Amount</label>
                    <div className="fw-semibold text-success">
                      ₹{parseFloat(deliveryNote.total_amount || 0).toLocaleString()}
                    </div>
                  </div>
                </Col>
              </Row>
              
              {deliveryNote.notes && (
                <div className="mt-3">
                  <label className="form-label text-muted">Notes</label>
                  <div className=" p-3 rounded">{deliveryNote.notes}</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary Card */}
        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faBoxes} className="me-2" />
                Summary
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Items:</span>
                <span className="fw-semibold">{deliveryNote.items?.length || 0}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Quantity:</span>
                <span className="fw-semibold">
                  {deliveryNote.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0}
                </span>
              </div>
              <hr />
              <div className="d-flex justify-content-between">
                <span>Total Amount:</span>
                <span className="fw-semibold text-success">
                  ₹{parseFloat(deliveryNote.total_amount || 0).toLocaleString()}
                </span>
              </div>
              
              {deliveryNote.created_at && (
                <div className="mt-3 pt-3 border-top">
                  <small className="text-muted">
                    <FontAwesomeIcon icon={faUser} className="me-1" />
                    Created: {new Date(deliveryNote.created_at).toLocaleString()}
                  </small>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Status Management */}
      <DeliveryNoteStatusManager
        deliveryNote={deliveryNote}
        onStatusUpdate={loadDeliveryNote}
      />

      {/* Delivery Items */}
      <Card>
        <Card.Header className="text-primary">
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faBoxes} className="me-2" />
            Delivery Items
          </h5>
        </Card.Header>
        <Card.Body className="p-0" style={{ backgroundColor: '#f8f9fa' }}>
          {deliveryNote.items && deliveryNote.items.length > 0 ? (
            <div className="table-responsive">
              <Table striped hover className="mb-0">
                <thead className="table-dark">
                  <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                  </tr>
                </thead>
                <tbody>
                  {deliveryNote.items.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="fw-semibold">{item.item_name}</td>
                      <td>{item.item_description || '-'}</td>
                      <td>{item.quantity}</td>
                      <td>{item.unit}</td>
                      <td>₹{parseFloat(item.unit_price || 0).toFixed(2)}</td>
                      <td className="fw-semibold">₹{parseFloat(item.total_price || 0).toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faBoxes} className="text-muted fa-2x mb-3" />
              <p className="text-muted">No items found for this delivery note</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default DeliveryNoteDetail;
