"""
Inventory Accounting Service for AVINI Labs
Handles inventory valuation, costing methods, and automatic journal entries
"""

from datetime import datetime
from database_manager import db_manager
import logging

logger = logging.getLogger(__name__)

class InventoryAccountingService:
    def __init__(self):
        self.db_manager = db_manager
    
    def get_inventory_valuation_methods(self):
        """Get all inventory valuation methods"""
        try:
            return self.db_manager.execute_query(
                "SELECT * FROM inventory_valuation_methods WHERE is_active = 1 ORDER BY method_name"
            )
        except Exception as e:
            logger.error(f"Error fetching inventory valuation methods: {str(e)}")
            raise
    
    def calculate_fifo_cost(self, item_id, quantity_needed, tenant_id):
        """Calculate cost using FIFO (First In, First Out) method"""
        try:
            # Get inventory transactions in FIFO order (oldest first)
            query = """
                SELECT it.*, im.item_name
                FROM inventory_transactions it
                JOIN inventory_master im ON it.item_id = im.id
                WHERE it.item_id = ? AND it.tenant_id = ? 
                AND it.transaction_type = 'IN' 
                AND it.remaining_quantity > 0
                ORDER BY it.transaction_date ASC, it.created_at ASC
            """
            
            transactions = self.db_manager.execute_query(query, [item_id, tenant_id])
            
            total_cost = 0
            remaining_needed = quantity_needed
            cost_breakdown = []
            
            for transaction in transactions:
                if remaining_needed <= 0:
                    break
                
                available_qty = transaction['remaining_quantity']
                qty_to_use = min(remaining_needed, available_qty)
                
                unit_cost = transaction['unit_cost']
                line_cost = qty_to_use * unit_cost
                total_cost += line_cost
                
                cost_breakdown.append({
                    'transaction_id': transaction['id'],
                    'transaction_date': transaction['transaction_date'],
                    'quantity_used': qty_to_use,
                    'unit_cost': unit_cost,
                    'line_cost': line_cost,
                    'remaining_in_lot': available_qty - qty_to_use
                })
                
                remaining_needed -= qty_to_use
            
            if remaining_needed > 0:
                raise ValueError(f"Insufficient inventory. Need {quantity_needed}, available {quantity_needed - remaining_needed}")
            
            return {
                'method': 'FIFO',
                'total_cost': round(total_cost, 2),
                'average_unit_cost': round(total_cost / quantity_needed, 2),
                'quantity': quantity_needed,
                'cost_breakdown': cost_breakdown
            }
            
        except Exception as e:
            logger.error(f"Error calculating FIFO cost: {str(e)}")
            raise
    
    def calculate_lifo_cost(self, item_id, quantity_needed, tenant_id):
        """Calculate cost using LIFO (Last In, First Out) method"""
        try:
            # Get inventory transactions in LIFO order (newest first)
            query = """
                SELECT it.*, im.item_name
                FROM inventory_transactions it
                JOIN inventory_master im ON it.item_id = im.id
                WHERE it.item_id = ? AND it.tenant_id = ? 
                AND it.transaction_type = 'IN' 
                AND it.remaining_quantity > 0
                ORDER BY it.transaction_date DESC, it.created_at DESC
            """
            
            transactions = self.db_manager.execute_query(query, [item_id, tenant_id])
            
            total_cost = 0
            remaining_needed = quantity_needed
            cost_breakdown = []
            
            for transaction in transactions:
                if remaining_needed <= 0:
                    break
                
                available_qty = transaction['remaining_quantity']
                qty_to_use = min(remaining_needed, available_qty)
                
                unit_cost = transaction['unit_cost']
                line_cost = qty_to_use * unit_cost
                total_cost += line_cost
                
                cost_breakdown.append({
                    'transaction_id': transaction['id'],
                    'transaction_date': transaction['transaction_date'],
                    'quantity_used': qty_to_use,
                    'unit_cost': unit_cost,
                    'line_cost': line_cost,
                    'remaining_in_lot': available_qty - qty_to_use
                })
                
                remaining_needed -= qty_to_use
            
            if remaining_needed > 0:
                raise ValueError(f"Insufficient inventory. Need {quantity_needed}, available {quantity_needed - remaining_needed}")
            
            return {
                'method': 'LIFO',
                'total_cost': round(total_cost, 2),
                'average_unit_cost': round(total_cost / quantity_needed, 2),
                'quantity': quantity_needed,
                'cost_breakdown': cost_breakdown
            }
            
        except Exception as e:
            logger.error(f"Error calculating LIFO cost: {str(e)}")
            raise
    
    def calculate_average_cost(self, item_id, quantity_needed, tenant_id):
        """Calculate cost using Weighted Average method"""
        try:
            # Get total quantity and value in inventory
            query = """
                SELECT 
                    SUM(remaining_quantity) as total_quantity,
                    SUM(remaining_quantity * unit_cost) as total_value
                FROM inventory_transactions
                WHERE item_id = ? AND tenant_id = ? 
                AND transaction_type = 'IN' 
                AND remaining_quantity > 0
            """
            
            result = self.db_manager.execute_query(query, [item_id, tenant_id])
            
            if not result or not result[0]['total_quantity']:
                raise ValueError("No inventory available for this item")
            
            total_quantity = result[0]['total_quantity']
            total_value = result[0]['total_value']
            
            if total_quantity < quantity_needed:
                raise ValueError(f"Insufficient inventory. Need {quantity_needed}, available {total_quantity}")
            
            average_unit_cost = total_value / total_quantity
            total_cost = quantity_needed * average_unit_cost
            
            return {
                'method': 'AVERAGE',
                'total_cost': round(total_cost, 2),
                'average_unit_cost': round(average_unit_cost, 2),
                'quantity': quantity_needed,
                'available_quantity': total_quantity,
                'total_inventory_value': round(total_value, 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating average cost: {str(e)}")
            raise
    
    def create_inventory_transaction(self, transaction_data):
        """Create inventory transaction with accounting entries"""
        try:
            # Insert inventory transaction
            insert_query = """
                INSERT INTO inventory_transactions (
                    item_id, transaction_type, quantity, unit_cost, total_cost,
                    remaining_quantity, transaction_date, reference_type, reference_id,
                    storeroom_id, tenant_id, notes, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            transaction_id = self.db_manager.execute_insert(insert_query, [
                transaction_data['item_id'],
                transaction_data['transaction_type'],
                transaction_data['quantity'],
                transaction_data['unit_cost'],
                transaction_data['total_cost'],
                transaction_data.get('remaining_quantity', transaction_data['quantity']),
                transaction_data['transaction_date'],
                transaction_data.get('reference_type'),
                transaction_data.get('reference_id'),
                transaction_data.get('storeroom_id'),
                transaction_data['tenant_id'],
                transaction_data.get('notes', ''),
                transaction_data['created_by'],
                datetime.now().isoformat()
            ])
            
            # Create accounting entries
            self.create_inventory_journal_entries(transaction_id, transaction_data)
            
            return transaction_id
            
        except Exception as e:
            logger.error(f"Error creating inventory transaction: {str(e)}")
            raise
    
    def create_inventory_journal_entries(self, transaction_id, transaction_data):
        """Create journal entries for inventory transactions"""
        try:
            from services.accounting_service import AccountingService
            accounting_service = AccountingService()
            
            # Get inventory accounts
            inventory_account = accounting_service.get_account_by_code('1300', transaction_data['tenant_id'])  # Inventory Asset
            cogs_account = accounting_service.get_account_by_code('5100', transaction_data['tenant_id'])  # Cost of Goods Sold
            
            if not inventory_account or not cogs_account:
                logger.warning("Inventory or COGS accounts not found, skipping journal entries")
                return None
            
            transaction_type = transaction_data['transaction_type']
            amount = transaction_data['total_cost']
            
            journal_data = {
                'journal_number': f"INV-{datetime.now().strftime('%Y%m%d')}-{transaction_id}",
                'journal_date': transaction_data['transaction_date'],
                'description': f"Inventory {transaction_type} - {transaction_data.get('notes', '')}",
                'reference_type': 'INVENTORY_TRANSACTION',
                'reference_id': transaction_id,
                'tenant_id': transaction_data['tenant_id'],
                'created_by': transaction_data['created_by']
            }
            
            journal_lines = []
            
            if transaction_type == 'IN':
                # Inventory IN: Debit Inventory, Credit varies based on source
                journal_lines.append({
                    'account_id': inventory_account['id'],
                    'debit_amount': amount,
                    'credit_amount': 0,
                    'description': f"Inventory received - {transaction_data.get('notes', '')}"
                })
                
                # Credit account depends on the source (AP for purchases, etc.)
                # For now, use a generic account
                journal_lines.append({
                    'account_id': cogs_account['id'],  # Temporary - should be AP or other source
                    'debit_amount': 0,
                    'credit_amount': amount,
                    'description': f"Inventory source - {transaction_data.get('notes', '')}"
                })
            
            elif transaction_type == 'OUT':
                # Inventory OUT: Credit Inventory, Debit COGS
                journal_lines.append({
                    'account_id': cogs_account['id'],
                    'debit_amount': amount,
                    'credit_amount': 0,
                    'description': f"Cost of goods sold - {transaction_data.get('notes', '')}"
                })
                
                journal_lines.append({
                    'account_id': inventory_account['id'],
                    'debit_amount': 0,
                    'credit_amount': amount,
                    'description': f"Inventory consumed - {transaction_data.get('notes', '')}"
                })
            
            elif transaction_type == 'ADJUSTMENT':
                # Inventory Adjustment: Debit/Credit Inventory, opposite to Inventory Adjustment account
                adjustment_account = accounting_service.get_account_by_code('5200', transaction_data['tenant_id'])  # Inventory Adjustments
                
                if adjustment_account:
                    if amount > 0:  # Positive adjustment (increase inventory)
                        journal_lines.append({
                            'account_id': inventory_account['id'],
                            'debit_amount': amount,
                            'credit_amount': 0,
                            'description': f"Inventory adjustment increase"
                        })
                        
                        journal_lines.append({
                            'account_id': adjustment_account['id'],
                            'debit_amount': 0,
                            'credit_amount': amount,
                            'description': f"Inventory adjustment"
                        })
                    else:  # Negative adjustment (decrease inventory)
                        journal_lines.append({
                            'account_id': adjustment_account['id'],
                            'debit_amount': abs(amount),
                            'credit_amount': 0,
                            'description': f"Inventory adjustment"
                        })
                        
                        journal_lines.append({
                            'account_id': inventory_account['id'],
                            'debit_amount': 0,
                            'credit_amount': abs(amount),
                            'description': f"Inventory adjustment decrease"
                        })
            
            if journal_lines:
                journal_id = accounting_service.create_journal_entry(journal_data, journal_lines)
                accounting_service.post_journal_entry(journal_id, transaction_data['created_by'])
                
                # Record the journal entry reference
                self.db_manager.execute_query(
                    "INSERT INTO inventory_accounting_entries (inventory_transaction_id, journal_entry_id, tenant_id) VALUES (?, ?, ?)",
                    [transaction_id, journal_id, transaction_data['tenant_id']]
                )
                
                return journal_id
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating inventory journal entries: {str(e)}")
            # Don't raise exception to avoid failing the inventory transaction
            return None
    
    def get_inventory_valuation_report(self, tenant_id, as_of_date=None, method='FIFO'):
        """Generate inventory valuation report"""
        try:
            if not as_of_date:
                as_of_date = datetime.now().date().isoformat()
            
            # Get all items with current inventory
            query = """
                SELECT
                    i.id, i.name as item_name, i.sku as item_code, i.category,
                    i.quantity as current_quantity
                FROM inventory i
                WHERE i.tenant_id = ? AND i.quantity > 0
                ORDER BY i.name
            """
            
            items = self.db_manager.execute_query(query, [tenant_id])
            
            valuation_report = []
            total_value = 0
            
            for item in items:
                try:
                    # Get cost price from inventory table
                    cost_query = "SELECT cost_price FROM inventory WHERE id = ?"
                    cost_result = self.db_manager.execute_query(cost_query, [item['id']])
                    cost_price = float(cost_result[0]['cost_price']) if cost_result and cost_result[0]['cost_price'] else 0

                    item_value = item['current_quantity'] * cost_price
                    total_value += item_value

                    valuation_report.append({
                        'item_id': item['id'],
                        'item_code': item['item_code'],
                        'item_name': item['item_name'],
                        'category': item['category'],
                        'quantity': item['current_quantity'],
                        'unit_cost': cost_price,
                        'total_value': round(item_value, 2),
                        'valuation_method': method
                    })

                except Exception as item_error:
                    logger.warning(f"Error valuing item {item['item_name']}: {str(item_error)}")
                    continue
            
            return {
                'as_of_date': as_of_date,
                'valuation_method': method,
                'total_items': len(valuation_report),
                'total_value': round(total_value, 2),
                'items': valuation_report
            }
            
        except Exception as e:
            logger.error(f"Error generating inventory valuation report: {str(e)}")
            raise

    def get_inventory_valuation(self, tenant_id: int, costing_method: str = 'FIFO', as_of_date: str = None) -> dict:
        """Get inventory valuation data for frontend"""
        try:
            if not as_of_date:
                as_of_date = datetime.now().date().isoformat()

            # Get inventory valuation report
            valuation_report = self.get_inventory_valuation_report(tenant_id, as_of_date, costing_method)

            # Add summary statistics
            total_items = len(valuation_report['items'])
            total_value = valuation_report['total_value']

            # Calculate low stock items (assuming low stock threshold is 10)
            low_stock_items = [item for item in valuation_report['items'] if item['quantity'] < 10]

            return {
                'success': True,
                'data': {
                    'summary': {
                        'total_items': total_items,
                        'total_value': total_value,
                        'low_stock_count': len(low_stock_items),
                        'valuation_method': costing_method,
                        'as_of_date': as_of_date
                    },
                    'items': valuation_report['items'],
                    'low_stock_items': low_stock_items
                }
            }

        except Exception as e:
            logger.error(f"Error getting inventory valuation: {str(e)}")
            raise

    def get_inventory_movements(self, tenant_id: int, start_date: str = None, end_date: str = None, item_id: int = None) -> dict:
        """Get inventory movements data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=30)).isoformat()

            # Build query based on parameters
            query = """
                SELECT
                    it.id,
                    it.transaction_date,
                    it.transaction_type,
                    it.quantity,
                    it.unit_cost,
                    it.total_cost,
                    it.reference_type,
                    CAST(it.reference_id AS TEXT) as reference_number,
                    i.name as item_name,
                    i.sku as item_code,
                    i.category
                FROM inventory_transactions it
                LEFT JOIN inventory i ON it.inventory_id = i.id
                WHERE it.transaction_date BETWEEN ? AND ?
            """

            params = [start_date, end_date]

            if item_id:
                query += " AND it.inventory_id = ?"
                params.append(item_id)

            query += " ORDER BY it.transaction_date DESC, it.id DESC"

            movements = self.db_manager.execute_query(query, params)

            # Calculate summary statistics
            total_in = sum(float(m['quantity']) for m in movements if m['transaction_type'] == 'IN')
            total_out = sum(float(m['quantity']) for m in movements if m['transaction_type'] == 'OUT')
            total_value_in = sum(float(m['total_cost']) for m in movements if m['transaction_type'] == 'IN')
            total_value_out = sum(float(m['total_cost']) for m in movements if m['transaction_type'] == 'OUT')

            return {
                'success': True,
                'data': {
                    'summary': {
                        'total_movements': len(movements),
                        'total_in': total_in,
                        'total_out': total_out,
                        'net_movement': total_in - total_out,
                        'total_value_in': total_value_in,
                        'total_value_out': total_value_out,
                        'period': {'start_date': start_date, 'end_date': end_date}
                    },
                    'movements': movements
                }
            }

        except Exception as e:
            logger.error(f"Error getting inventory movements: {str(e)}")
            raise

    def get_cogs_analysis(self, tenant_id: int, start_date: str = None, end_date: str = None, costing_method: str = 'FIFO') -> dict:
        """Get COGS analysis data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=30)).isoformat()

            # Get sales transactions with COGS - with fallback for missing columns
            try:
                query = """
                    SELECT
                        it.transaction_date,
                        it.inventory_id as item_id,
                        i.name as item_name,
                        i.sku as item_code,
                        it.quantity as quantity_sold,
                        it.unit_cost as cogs_per_unit,
                        it.total_cost as total_cogs,
                        it.reference_type,
                        CAST(it.reference_id AS TEXT) as reference_number
                    FROM inventory_transactions it
                    LEFT JOIN inventory i ON it.inventory_id = i.id
                    WHERE it.transaction_date BETWEEN ? AND ?
                        AND it.transaction_type = 'OUT'
                        AND it.reference_type IN ('SALES', 'INVOICE')
                    ORDER BY it.transaction_date DESC
                """

                cogs_transactions = self.db_manager.execute_query(query, [start_date, end_date])
            except Exception as e:
                if "no such column" in str(e).lower():
                    # Fallback: Use created_at or simplified query without transaction_date
                    query = """
                        SELECT
                            it.created_at as transaction_date,
                            it.inventory_id as item_id,
                            i.name as item_name,
                            i.sku as item_code,
                            it.quantity as quantity_sold,
                            it.unit_cost as cogs_per_unit,
                            it.total_cost as total_cogs,
                            it.reference_type,
                            CAST(it.reference_id AS TEXT) as reference_number
                        FROM inventory_transactions it
                        LEFT JOIN inventory i ON it.inventory_id = i.id
                        WHERE 1=1
                            AND it.transaction_type = 'OUT'
                            AND it.reference_type IN ('SALES', 'INVOICE')
                        ORDER BY it.created_at DESC
                        LIMIT 100
                    """

                    cogs_transactions = self.db_manager.execute_query(query, [])
                else:
                    raise e

            # Get corresponding sales revenue (if available)
            revenue_query = """
                SELECT
                    si.invoice_date,
                    sil.test_id as item_id,
                    sil.quantity,
                    sil.unit_price,
                    sil.line_total as revenue,
                    si.invoice_number
                FROM sales_invoice_lines sil
                JOIN sales_invoices si ON sil.sales_invoice_id = si.id
                WHERE si.invoice_date BETWEEN ? AND ?
                    AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')
                ORDER BY si.invoice_date DESC
            """

            revenue_transactions = self.db_manager.execute_query(revenue_query, [start_date, end_date])

            # Calculate analysis
            total_cogs = sum(float(t['total_cogs']) for t in cogs_transactions)
            total_revenue = sum(float(t['revenue']) for t in revenue_transactions)
            gross_profit = total_revenue - total_cogs
            gross_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

            # Group by item for item-wise analysis
            item_analysis = {}
            for transaction in cogs_transactions:
                item_id = transaction['item_id']
                if item_id not in item_analysis:
                    item_analysis[item_id] = {
                        'item_name': transaction['item_name'],
                        'item_code': transaction['item_code'],
                        'total_quantity_sold': 0,
                        'total_cogs': 0,
                        'total_revenue': 0,
                        'transactions': 0
                    }

                item_analysis[item_id]['total_quantity_sold'] += float(transaction['quantity_sold'])
                item_analysis[item_id]['total_cogs'] += float(transaction['total_cogs'])
                item_analysis[item_id]['transactions'] += 1

            # Add revenue data to item analysis
            for revenue in revenue_transactions:
                item_id = revenue['item_id']
                if item_id in item_analysis:
                    item_analysis[item_id]['total_revenue'] += float(revenue['revenue'])

            # Calculate margins for each item
            for item_id, data in item_analysis.items():
                if data['total_revenue'] > 0:
                    data['gross_profit'] = data['total_revenue'] - data['total_cogs']
                    data['gross_margin'] = (data['gross_profit'] / data['total_revenue'] * 100)
                    data['avg_cogs_per_unit'] = data['total_cogs'] / data['total_quantity_sold'] if data['total_quantity_sold'] > 0 else 0
                else:
                    data['gross_profit'] = -data['total_cogs']
                    data['gross_margin'] = -100
                    data['avg_cogs_per_unit'] = data['total_cogs'] / data['total_quantity_sold'] if data['total_quantity_sold'] > 0 else 0

            return {
                'success': True,
                'data': {
                    'summary': {
                        'total_cogs': total_cogs,
                        'total_revenue': total_revenue,
                        'gross_profit': gross_profit,
                        'gross_margin': round(gross_margin, 2),
                        'costing_method': costing_method,
                        'period': {'start_date': start_date, 'end_date': end_date}
                    },
                    'item_analysis': list(item_analysis.values()),
                    'cogs_transactions': cogs_transactions
                }
            }

        except Exception as e:
            logger.error(f"Error getting COGS analysis: {str(e)}")
            raise

    def get_inventory_adjustments(self, tenant_id: int, start_date: str = None, end_date: str = None) -> dict:
        """Get inventory adjustments data"""
        try:
            if not start_date or not end_date:
                from datetime import date, timedelta
                end_date = date.today().isoformat()
                start_date = (date.today() - timedelta(days=90)).isoformat()

            # Get inventory adjustment transactions
            query = """
                SELECT
                    it.id,
                    it.transaction_date,
                    it.transaction_type,
                    it.quantity,
                    it.unit_cost,
                    it.total_cost,
                    it.reference_type,
                    CAST(it.reference_id AS TEXT) as reference_number,
                    it.notes,
                    i.name as item_name,
                    i.sku as item_code,
                    i.category
                FROM inventory_transactions it
                LEFT JOIN inventory i ON it.inventory_id = i.id
                WHERE it.transaction_date BETWEEN ? AND ?
                    AND it.reference_type = 'ADJUSTMENT'
                ORDER BY it.transaction_date DESC, it.id DESC
            """

            adjustments = self.db_manager.execute_query(query, [start_date, end_date])

            # Calculate summary statistics
            positive_adjustments = [adj for adj in adjustments if float(adj['quantity']) > 0]
            negative_adjustments = [adj for adj in adjustments if float(adj['quantity']) < 0]

            total_positive_qty = sum(float(adj['quantity']) for adj in positive_adjustments)
            total_negative_qty = sum(abs(float(adj['quantity'])) for adj in negative_adjustments)
            total_positive_value = sum(float(adj['total_cost']) for adj in positive_adjustments)
            total_negative_value = sum(abs(float(adj['total_cost'])) for adj in negative_adjustments)

            return {
                'success': True,
                'data': {
                    'summary': {
                        'total_adjustments': len(adjustments),
                        'positive_adjustments': len(positive_adjustments),
                        'negative_adjustments': len(negative_adjustments),
                        'total_positive_qty': total_positive_qty,
                        'total_negative_qty': total_negative_qty,
                        'net_qty_adjustment': total_positive_qty - total_negative_qty,
                        'total_positive_value': total_positive_value,
                        'total_negative_value': total_negative_value,
                        'net_value_adjustment': total_positive_value - total_negative_value,
                        'period': {'start_date': start_date, 'end_date': end_date}
                    },
                    'adjustments': adjustments
                }
            }

        except Exception as e:
            logger.error(f"Error getting inventory adjustments: {str(e)}")
            raise

    # ============================================================================
    # ENHANCED INVENTORY ACCOUNTING FEATURES
    # ============================================================================

    def calculate_cogs_for_sale(self, item_id: int, quantity_sold: float, tenant_id: int, method: str = 'FIFO') -> dict:
        """Calculate Cost of Goods Sold for a sale transaction"""
        try:
            if method == 'FIFO':
                return self._calculate_cogs_fifo(item_id, quantity_sold, tenant_id)
            elif method == 'LIFO':
                return self._calculate_cogs_lifo(item_id, quantity_sold, tenant_id)
            elif method == 'AVERAGE':
                return self._calculate_cogs_average(item_id, quantity_sold, tenant_id)
            else:
                raise ValueError(f"Unsupported costing method: {method}")

        except Exception as e:
            logger.error(f"Error calculating COGS: {str(e)}")
            raise

    def _calculate_cogs_fifo(self, item_id: int, quantity_sold: float, tenant_id: int) -> dict:
        """Calculate COGS using FIFO method"""
        try:
            # Get available inventory in FIFO order (oldest first)
            query = """
                SELECT id, unit_cost, remaining_quantity, transaction_date
                FROM inventory_transactions
                WHERE item_id = ? AND tenant_id = ?
                    AND transaction_type = 'IN'
                    AND remaining_quantity > 0
                ORDER BY transaction_date ASC, id ASC
            """

            available_stock = self.db_manager.execute_query(query, [item_id, tenant_id])

            cogs_details = []
            total_cogs = 0
            remaining_to_sell = quantity_sold

            for stock in available_stock:
                if remaining_to_sell <= 0:
                    break

                quantity_from_this_lot = min(remaining_to_sell, stock['remaining_quantity'])
                cost_from_this_lot = quantity_from_this_lot * stock['unit_cost']

                cogs_details.append({
                    'transaction_id': stock['id'],
                    'transaction_date': stock['transaction_date'],
                    'unit_cost': stock['unit_cost'],
                    'quantity_used': quantity_from_this_lot,
                    'cost_amount': cost_from_this_lot
                })

                total_cogs += cost_from_this_lot
                remaining_to_sell -= quantity_from_this_lot

                # Update remaining quantity in the stock transaction
                new_remaining = stock['remaining_quantity'] - quantity_from_this_lot
                self.db_manager.execute_query(
                    "UPDATE inventory_transactions SET remaining_quantity = ? WHERE id = ?",
                    [new_remaining, stock['id']]
                )

            if remaining_to_sell > 0:
                logger.warning(f"Insufficient inventory for item {item_id}. Short by {remaining_to_sell} units")

            return {
                'method': 'FIFO',
                'total_cogs': round(total_cogs, 2),
                'average_unit_cost': round(total_cogs / quantity_sold, 2) if quantity_sold > 0 else 0,
                'cogs_details': cogs_details,
                'quantity_sold': quantity_sold,
                'shortage': max(0, remaining_to_sell)
            }

        except Exception as e:
            logger.error(f"Error calculating FIFO COGS: {str(e)}")
            raise

    def create_cogs_journal_entry(self, cogs_data: dict, sale_data: dict, user_id: int) -> int:
        """Create journal entry for Cost of Goods Sold"""
        try:
            from services.accounting_service import AccountingService
            accounting_service = AccountingService()

            # Get COGS and Inventory accounts
            cogs_account = self.db_manager.execute_query(
                "SELECT * FROM chart_of_accounts WHERE account_type = 'COST_OF_GOODS_SOLD' AND tenant_id = ? LIMIT 1",
                [sale_data['tenant_id']]
            )

            inventory_account = self.db_manager.execute_query(
                "SELECT * FROM chart_of_accounts WHERE account_name LIKE '%Inventory%' AND account_type = 'ASSET' AND tenant_id = ? LIMIT 1",
                [sale_data['tenant_id']]
            )

            if not cogs_account or not inventory_account:
                logger.error("COGS or Inventory account not found")
                return None

            # Create journal entry
            journal_data = {
                'journal_number': f"COGS-{datetime.now().strftime('%Y%m%d')}-{sale_data.get('reference_id', '')}",
                'journal_date': sale_data.get('sale_date', datetime.now().date().isoformat()),
                'description': f"COGS for sale - {sale_data.get('description', '')}",
                'reference_type': 'SALES_INVOICE',
                'reference_id': sale_data.get('reference_id'),
                'tenant_id': sale_data['tenant_id'],
                'created_by': user_id
            }

            journal_lines = [
                {
                    'account_id': cogs_account[0]['id'],
                    'debit_amount': cogs_data['total_cogs'],
                    'credit_amount': 0,
                    'description': f"COGS - {cogs_data['method']} method"
                },
                {
                    'account_id': inventory_account[0]['id'],
                    'debit_amount': 0,
                    'credit_amount': cogs_data['total_cogs'],
                    'description': f"Inventory reduction - {cogs_data['method']} method"
                }
            ]

            journal_id = accounting_service.create_journal_entry(journal_data, journal_lines)
            accounting_service.post_journal_entry(journal_id, user_id)

            return journal_id

        except Exception as e:
            logger.error(f"Error creating COGS journal entry: {str(e)}")
            raise
