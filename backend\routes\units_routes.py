from flask import Blueprint, request, jsonify
from datetime import datetime
import uuid
from utils import token_required, read_data, write_data, filter_data_by_tenant, check_tenant_access, require_role, require_module_access

units_bp = Blueprint('units', __name__)

@units_bp.route('/api/units', methods=['GET'])
@token_required
def get_units():
    """Get all units of measure"""
    try:
        units = read_data('units_of_measure.json')
        
        # Filter by category if specified
        category = request.args.get('category')
        if category:
            units = [unit for unit in units if unit.get('category') == category]
        
        # Filter by active status
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        if active_only:
            units = [unit for unit in units if unit.get('is_active', True)]
        
        return jsonify({
            'success': True,
            'data': units
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units/<int:unit_id>', methods=['GET'])
@token_required
def get_unit(unit_id):
    """Get a specific unit of measure"""
    try:
        units = read_data('units_of_measure.json')
        unit = next((u for u in units if u['id'] == unit_id), None)
        
        if not unit:
            return jsonify({
                'success': False,
                'error': 'Unit not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': unit
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def create_unit():
    """Create a new unit of measure"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['code', 'name', 'category']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        units = read_data('units_of_measure.json')
        
        # Check if code already exists
        if any(unit['code'].lower() == data['code'].lower() for unit in units):
            return jsonify({
                'success': False,
                'error': 'Unit code already exists'
            }), 400
        
        # Generate new ID
        new_id = max([unit['id'] for unit in units], default=0) + 1
        
        # Create new unit
        new_unit = {
            'id': new_id,
            'code': data['code'].lower(),
            'name': data['name'],
            'description': data.get('description', ''),
            'category': data['category'],
            'is_active': data.get('is_active', True),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        units.append(new_unit)
        write_data('units_of_measure.json', units)
        
        return jsonify({
            'success': True,
            'data': new_unit,
            'message': 'Unit created successfully'
        }), 201
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units/<int:unit_id>', methods=['PUT'])
@token_required
@require_role(['admin', 'hub_admin'])
def update_unit(unit_id):
    """Update a unit of measure"""
    try:
        data = request.get_json()
        units = read_data('units_of_measure.json')
        
        # Find the unit
        unit_index = next((i for i, u in enumerate(units) if u['id'] == unit_id), None)
        if unit_index is None:
            return jsonify({
                'success': False,
                'error': 'Unit not found'
            }), 404
        
        # Check if code already exists (excluding current unit)
        if 'code' in data:
            existing_unit = next((u for u in units if u['code'].lower() == data['code'].lower() and u['id'] != unit_id), None)
            if existing_unit:
                return jsonify({
                    'success': False,
                    'error': 'Unit code already exists'
                }), 400
        
        # Update unit
        unit = units[unit_index]
        unit.update({
            'code': data.get('code', unit['code']).lower(),
            'name': data.get('name', unit['name']),
            'description': data.get('description', unit['description']),
            'category': data.get('category', unit['category']),
            'is_active': data.get('is_active', unit['is_active']),
            'updated_at': datetime.now().isoformat()
        })
        
        write_data('units_of_measure.json', units)
        
        return jsonify({
            'success': True,
            'data': unit,
            'message': 'Unit updated successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units/<int:unit_id>', methods=['DELETE'])
@token_required
@require_role(['admin', 'hub_admin'])
def delete_unit(unit_id):
    """Delete a unit of measure (soft delete by setting is_active to False)"""
    try:
        units = read_data('units_of_measure.json')
        
        # Find the unit
        unit_index = next((i for i, u in enumerate(units) if u['id'] == unit_id), None)
        if unit_index is None:
            return jsonify({
                'success': False,
                'error': 'Unit not found'
            }), 404
        
        # Soft delete by setting is_active to False
        units[unit_index]['is_active'] = False
        units[unit_index]['updated_at'] = datetime.now().isoformat()
        
        write_data('units_of_measure.json', units)
        
        return jsonify({
            'success': True,
            'message': 'Unit deleted successfully'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units/categories', methods=['GET'])
@token_required
def get_unit_categories():
    """Get all unit categories"""
    try:
        units = read_data('units_of_measure.json')
        categories = list(set(unit['category'] for unit in units if unit.get('is_active', True)))
        categories.sort()
        
        return jsonify({
            'success': True,
            'data': categories
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@units_bp.route('/api/units/by-category', methods=['GET'])
@token_required
def get_units_by_category():
    """Get units grouped by category"""
    try:
        units = read_data('units_of_measure.json')
        
        # Filter active units only
        active_units = [unit for unit in units if unit.get('is_active', True)]
        
        # Group by category
        categories = {}
        for unit in active_units:
            category = unit['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(unit)
        
        return jsonify({
            'success': True,
            'data': categories
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
