import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, Mo<PERSON>, Form } from 'react-bootstrap';
import { FaPlus, FaEye, FaCheck, FaUndo, FaMinus } from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const JournalEntries = () => {
  const [journalEntries, setJournalEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedJournal, setSelectedJournal] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [formData, setFormData] = useState({
    journal_date: new Date().toISOString().split('T')[0],
    description: '',
    reference_type: '',
    reference_id: '',
    entries: [
      { account_id: '', debit_amount: '', credit_amount: '', description: '' },
      { account_id: '', debit_amount: '', credit_amount: '', description: '' }
    ]
  });

  useEffect(() => {
    loadJournalEntries();
    loadAccounts();
  }, []);

  const loadJournalEntries = async () => {
    try {
      setLoading(true);
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tenantId = user.tenant_id || 1;

      const response = await accountingService.getJournalEntries(tenantId, {
        page: 1,
        per_page: 50
      });
      setJournalEntries(response.data || []);
    } catch (err) {
      setError('Failed to load journal entries');
      console.error('Error loading journal entries:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAccounts = async () => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tenantId = user.tenant_id || 1;

      const response = await accountingService.getChartOfAccounts(tenantId);
      setAccounts(response.data || []);
    } catch (err) {
      console.error('Error loading accounts:', err);
    }
  };

  const handleAddJournal = () => {
    setShowAddModal(true);
  };

  const handleCloseAddModal = () => {
    setShowAddModal(false);
    setFormData({
      journal_date: new Date().toISOString().split('T')[0],
      description: '',
      reference_type: '',
      reference_id: '',
      entries: [
        { account_id: '', debit_amount: '', credit_amount: '', description: '' },
        { account_id: '', debit_amount: '', credit_amount: '', description: '' }
      ]
    });
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEntryChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      entries: prev.entries.map((entry, i) =>
        i === index ? { ...entry, [field]: value } : entry
      )
    }));
  };

  const addEntry = () => {
    setFormData(prev => ({
      ...prev,
      entries: [...prev.entries, { account_id: '', debit_amount: '', credit_amount: '', description: '' }]
    }));
  };

  const removeEntry = (index) => {
    if (formData.entries.length > 2) {
      setFormData(prev => ({
        ...prev,
        entries: prev.entries.filter((_, i) => i !== index)
      }));
    }
  };

  const handleSubmitJournal = async (e) => {
    e.preventDefault();
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tenantId = user.tenant_id || 1;

      // Validate entries balance
      const totalDebits = formData.entries.reduce((sum, entry) => sum + (parseFloat(entry.debit_amount) || 0), 0);
      const totalCredits = formData.entries.reduce((sum, entry) => sum + (parseFloat(entry.credit_amount) || 0), 0);

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        setError('Journal entries must balance. Total debits must equal total credits.');
        return;
      }

      // Prepare data in the format expected by backend
      const journalData = {
        journal_entry: {
          journal_type: 'MANUAL',
          transaction_date: formData.journal_date,
          description: formData.description,
          reference_type: formData.reference_type || null,
          reference_id: formData.reference_id || null,
          tenant_id: tenantId,
          created_by: user.id
        },
        line_items: formData.entries.filter(entry =>
          entry.account_id && (entry.debit_amount || entry.credit_amount)
        ).map(entry => ({
          account_id: parseInt(entry.account_id),
          debit_amount: parseFloat(entry.debit_amount) || 0,
          credit_amount: parseFloat(entry.credit_amount) || 0,
          description: entry.description || formData.description
        }))
      };

      await accountingService.createJournalEntry(journalData);
      setShowAddModal(false);
      loadJournalEntries();
      handleCloseAddModal();
    } catch (err) {
      setError('Failed to create journal entry');
      console.error('Error creating journal entry:', err);
    }
  };

  const handleViewJournal = (journal) => {
    setSelectedJournal(journal);
    setShowViewModal(true);
  };

  const handlePostJournal = async (journalId) => {
    try {
      await accountingService.postJournalEntry(journalId);
      loadJournalEntries(); // Reload to show updated status
    } catch (err) {
      setError('Failed to post journal entry');
      console.error('Error posting journal entry:', err);
    }
  };

  const handleReverseJournal = async (journalId) => {
    try {
      await accountingService.reverseJournalEntry(journalId);
      loadJournalEntries(); // Reload to show updated status
    } catch (err) {
      setError('Failed to reverse journal entry');
      console.error('Error reversing journal entry:', err);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'DRAFT': 'secondary',
      'POSTED': 'success',
      'REVERSED': 'danger'
    };
    return colors[status] || 'light';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0 text-primary">Journal Entries</h5>
          <Button variant="primary" size="sm" onClick={handleAddJournal}>
            <FaPlus className="me-1" />
            New Journal Entry
          </Button>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          
          {journalEntries.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-muted">No journal entries found</p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover>
                <thead>
                  <tr>
                    <th>Journal #</th>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Reference</th>
                    <th>Total Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntries.map((entry) => (
                    <tr key={entry.id}>
                      <td>
                        <code>{entry.journal_number}</code>
                      </td>
                      <td>{new Date(entry.posting_date).toLocaleDateString()}</td>
                      <td>{entry.description}</td>
                      <td>
                        {entry.reference_type && (
                          <small className="text-muted">
                            {entry.reference_type}: {entry.reference_id}
                          </small>
                        )}
                      </td>
                      <td className="text-end">
                        {formatCurrency(entry.total_amount)}
                      </td>
                      <td>
                        <Badge bg={getStatusColor(entry.status)}>
                          {entry.status}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleViewJournal(entry)}
                          >
                            <FaEye />
                          </Button>
                          {entry.status === 'DRAFT' && (
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handlePostJournal(entry.id)}
                            >
                              <FaCheck />
                            </Button>
                          )}
                          {entry.status === 'POSTED' && (
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleReverseJournal(entry.id)}
                            >
                              <FaUndo />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Add Journal Entry Modal */}
      <Modal show={showAddModal} onHide={handleCloseAddModal} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>New Journal Entry</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmitJournal}>
            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Journal Date *</Form.Label>
                  <Form.Control
                    type="date"
                    name="journal_date"
                    value={formData.journal_date}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Reference Type</Form.Label>
                  <Form.Select
                    name="reference_type"
                    value={formData.reference_type}
                    onChange={handleFormChange}
                  >
                    <option value="">Select Reference Type</option>
                    <option value="INVOICE">Invoice</option>
                    <option value="PAYMENT">Payment</option>
                    <option value="ADJUSTMENT">Adjustment</option>
                    <option value="OTHER">Other</option>
                  </Form.Select>
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Reference ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="reference_id"
                    value={formData.reference_id}
                    onChange={handleFormChange}
                    placeholder="Reference number or ID"
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Description *</Form.Label>
                  <Form.Control
                    type="text"
                    name="description"
                    value={formData.description}
                    onChange={handleFormChange}
                    required
                    placeholder="Journal entry description"
                  />
                </Form.Group>
              </div>
            </div>

            <hr />
            <h6 className="text-primary">Journal Entries</h6>

            {formData.entries.map((entry, index) => (
              <div key={index} className="border rounded p-3 mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <h6 className="mb-0">Entry {index + 1}</h6>
                  {formData.entries.length > 2 && (
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => removeEntry(index)}
                    >
                      <FaMinus />
                    </Button>
                  )}
                </div>

                <div className="row">
                  <div className="col-md-4">
                    <Form.Group className="mb-3">
                      <Form.Label>Account *</Form.Label>
                      <Form.Select
                        value={entry.account_id}
                        onChange={(e) => handleEntryChange(index, 'account_id', e.target.value)}
                        required
                      >
                        <option value="">Select Account</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.account_code} - {account.account_name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </div>
                  <div className="col-md-3">
                    <Form.Group className="mb-3">
                      <Form.Label>Debit Amount</Form.Label>
                      <Form.Control
                        type="number"
                        step="0.01"
                        value={entry.debit_amount}
                        onChange={(e) => handleEntryChange(index, 'debit_amount', e.target.value)}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </div>
                  <div className="col-md-3">
                    <Form.Group className="mb-3">
                      <Form.Label>Credit Amount</Form.Label>
                      <Form.Control
                        type="number"
                        step="0.01"
                        value={entry.credit_amount}
                        onChange={(e) => handleEntryChange(index, 'credit_amount', e.target.value)}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </div>
                  <div className="col-md-2">
                    <Form.Group className="mb-3">
                      <Form.Label>Description</Form.Label>
                      <Form.Control
                        type="text"
                        value={entry.description}
                        onChange={(e) => handleEntryChange(index, 'description', e.target.value)}
                        placeholder="Entry description"
                      />
                    </Form.Group>
                  </div>
                </div>
              </div>
            ))}

            <Button variant="outline-primary" onClick={addEntry} className="mb-3">
              <FaPlus className="me-1" />
              Add Entry
            </Button>

            <div className="row">
              <div className="col-md-6">
                <div className="alert alert-info">
                  <strong>Total Debits:</strong> ₹{formData.entries.reduce((sum, entry) => sum + (parseFloat(entry.debit_amount) || 0), 0).toFixed(2)}
                </div>
              </div>
              <div className="col-md-6">
                <div className="alert alert-info">
                  <strong>Total Credits:</strong> ₹{formData.entries.reduce((sum, entry) => sum + (parseFloat(entry.credit_amount) || 0), 0).toFixed(2)}
                </div>
              </div>
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseAddModal}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmitJournal}>
            Create Journal Entry
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Journal Entry Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Journal Entry Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedJournal && (
            <div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <strong>Journal ID:</strong> {selectedJournal.id}
                </div>
                <div className="col-md-6">
                  <strong>Date:</strong> {new Date(selectedJournal.transaction_date).toLocaleDateString()}
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <strong>Status:</strong>
                  <Badge
                    bg={selectedJournal.status === 'POSTED' ? 'success' :
                        selectedJournal.status === 'DRAFT' ? 'warning' : 'secondary'}
                    className="ms-2"
                  >
                    {selectedJournal.status}
                  </Badge>
                </div>
                <div className="col-md-6">
                  <strong>Total Amount:</strong> ${selectedJournal.total_amount || '0.00'}
                </div>
              </div>
              <div className="mb-3">
                <strong>Description:</strong> {selectedJournal.description}
              </div>
              {selectedJournal.line_items && selectedJournal.line_items.length > 0 && (
                <div>
                  <h6>Line Items:</h6>
                  <Table striped bordered hover size="sm">
                    <thead>
                      <tr>
                        <th>Account</th>
                        <th>Description</th>
                        <th>Debit</th>
                        <th>Credit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedJournal.line_items.map((item, index) => (
                        <tr key={index}>
                          <td>{item.account_name || item.account_id}</td>
                          <td>{item.description}</td>
                          <td>{item.debit_amount ? `$${parseFloat(item.debit_amount).toFixed(2)}` : '-'}</td>
                          <td>{item.credit_amount ? `$${parseFloat(item.credit_amount).toFixed(2)}` : '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default JournalEntries;
