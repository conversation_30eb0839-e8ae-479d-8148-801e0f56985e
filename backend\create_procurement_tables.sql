-- Create Procurement and Storeroom Tables
-- This script creates all the necessary tables for the procurement module and storeroom management

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- 1. Storerooms with Enhanced Inventory Management
CREATE TABLE IF NOT EXISTS storerooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    storeroom_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    tenant_id INTEGER NOT NULL,
    location_details TEXT,
    capacity DECIMAL(10,2),
    status TEXT CHECK (status IN ('active', 'inactive')) DEFAULT 'active',
    manager_name TEXT,
    manager_contact TEXT,
    -- Enhanced inventory management fields
    min_quantity_threshold DECIMAL(10,2) DEFAULT 0,
    max_quantity_limit DECIMAL(10,2) DEFAULT 0,
    safety_stock_level DECIMAL(10,2) DEFAULT 0,
    reorder_quantity DECIMAL(10,2) DEFAULT 0,
    auto_reorder_enabled BOOLEAN DEFAULT 0,
    reorder_point_days INTEGER DEFAULT 7,
    -- Standard fields
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 2. Purchase Requests
CREATE TABLE IF NOT EXISTS purchase_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number TEXT UNIQUE NOT NULL,
    requesting_tenant_id INTEGER NOT NULL,
    hub_tenant_id INTEGER NOT NULL,
    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    status TEXT CHECK (status IN ('draft', 'submitted', 'approved', 'rejected', 'processing', 'completed', 'cancelled')) DEFAULT 'draft',
    request_date DATE NOT NULL,
    required_date DATE NOT NULL,
    notes TEXT,
    total_estimated_amount DECIMAL(12,2) DEFAULT 0,
    storeroom_id INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requesting_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (hub_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 3. Purchase Request Items
CREATE TABLE IF NOT EXISTS purchase_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_request_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    requested_quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    estimated_unit_price DECIMAL(10,2) DEFAULT 0,
    total_estimated_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id) ON DELETE CASCADE
);

-- 4. Purchase Orders
CREATE TABLE IF NOT EXISTS purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    po_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER,
    tenant_id INTEGER NOT NULL,
    status TEXT CHECK (status IN ('draft', 'sent', 'confirmed', 'received', 'cancelled')) DEFAULT 'draft',
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    payment_terms TEXT,
    delivery_address TEXT,
    notes TEXT,
    purchase_request_id INTEGER,
    received_at TIMESTAMP,
    received_by INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (received_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 5. Purchase Order Items
CREATE TABLE IF NOT EXISTS purchase_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_order_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE
);

-- 6. Proforma Invoices
CREATE TABLE IF NOT EXISTS proforma_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_number TEXT UNIQUE NOT NULL,
    purchase_request_id INTEGER,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    status TEXT CHECK (status IN ('draft', 'sent', 'accepted', 'rejected', 'expired')) DEFAULT 'draft',
    invoice_date DATE NOT NULL,
    valid_until DATE,
    payment_terms TEXT,
    credit_period_days INTEGER DEFAULT 30,
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    balance_amount DECIMAL(12,2) DEFAULT 0,
    payment_due_date DATE,
    notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 7. Proforma Invoice Items
CREATE TABLE IF NOT EXISTS proforma_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_invoice_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proforma_invoice_id) REFERENCES proforma_invoices(id) ON DELETE CASCADE
);

-- 8. Delivery Notes with Comprehensive Status Management
CREATE TABLE IF NOT EXISTS delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_number TEXT UNIQUE NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    delivery_type TEXT CHECK (delivery_type IN ('hub_to_franchise', 'supplier_to_hub')) NOT NULL,

    -- Source/Hub side status
    hub_status TEXT CHECK (hub_status IN (
        'prepared', 'dispatched', 'delivered', 'returned',
        'reprocessed', 'cancelled', 'partially_delivered', 'in_transit'
    )) DEFAULT 'prepared',

    -- Destination/Franchise side status
    franchise_status TEXT CHECK (franchise_status IN (
        'pending_receipt', 'received', 'confirmed', 'rejected', 'partially_accepted'
    )) DEFAULT 'pending_receipt',

    -- Legacy status field for backward compatibility
    status TEXT CHECK (status IN (
        'prepared', 'dispatched', 'in_transit', 'delivered', 'cancelled',
        'returned', 'reprocessed', 'partially_delivered', 'pending_receipt',
        'received', 'confirmed', 'rejected', 'partially_accepted'
    )) DEFAULT 'prepared',

    delivery_date DATE,
    expected_delivery_date DATE,
    delivery_address TEXT,
    total_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    purchase_request_id INTEGER,
    purchase_order_id INTEGER,
    tracking_number TEXT,
    carrier TEXT,

    -- Enhanced tracking fields
    dispatched_at TIMESTAMP,
    dispatched_by INTEGER,
    delivered_at TIMESTAMP,
    received_by INTEGER,
    receiver_signature TEXT,
    delivery_notes TEXT,

    -- New status tracking fields
    confirmed_at TIMESTAMP,
    confirmed_by INTEGER,
    rejected_at TIMESTAMP,
    rejected_by INTEGER,
    rejection_reason TEXT,
    returned_at TIMESTAMP,
    returned_by INTEGER,
    return_reason TEXT,
    reprocessed_at TIMESTAMP,
    reprocessed_by INTEGER,

    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (dispatched_by) REFERENCES users(id),
    FOREIGN KEY (received_by) REFERENCES users(id),
    FOREIGN KEY (confirmed_by) REFERENCES users(id),
    FOREIGN KEY (rejected_by) REFERENCES users(id),
    FOREIGN KEY (returned_by) REFERENCES users(id),
    FOREIGN KEY (reprocessed_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 9. Delivery Note Items with Inventory Integration
CREATE TABLE IF NOT EXISTS delivery_note_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_note_id INTEGER NOT NULL,

    -- Inventory integration fields
    inventory_item_id INTEGER,
    storeroom_id INTEGER,

    -- Item details (can be from inventory or manual entry)
    item_name TEXT NOT NULL,
    description TEXT,
    sku TEXT,
    category TEXT,

    -- Quantity and pricing
    requested_quantity INTEGER NOT NULL,
    delivered_quantity INTEGER DEFAULT 0,
    accepted_quantity INTEGER DEFAULT 0,
    rejected_quantity INTEGER DEFAULT 0,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,

    -- Stock validation
    available_stock INTEGER DEFAULT 0,
    stock_validated_at TIMESTAMP,

    -- Item status tracking
    item_status TEXT CHECK (item_status IN (
        'pending', 'dispatched', 'delivered', 'accepted', 'rejected', 'partially_accepted'
    )) DEFAULT 'pending',

    -- Additional fields
    batch_number TEXT,
    expiry_date DATE,
    rejection_reason TEXT,
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id),
    FOREIGN KEY (storeroom_id) REFERENCES storerooms(id)
);

-- 10. Delivery Note Status History
CREATE TABLE IF NOT EXISTS delivery_note_status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_note_id INTEGER NOT NULL,
    status_type TEXT CHECK (status_type IN ('hub_status', 'franchise_status', 'legacy_status')) NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_by INTEGER NOT NULL,
    change_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id)
);

-- 11. Payment Transactions
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_number TEXT UNIQUE NOT NULL,
    transaction_type TEXT CHECK (transaction_type IN ('payment', 'refund', 'adjustment')) NOT NULL,
    reference_type TEXT CHECK (reference_type IN ('proforma_invoice', 'purchase_order', 'delivery_note')) NOT NULL,
    reference_id INTEGER NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_method TEXT,
    payment_date DATE NOT NULL,
    status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    notes TEXT,
    transaction_reference TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 11. Inventory Transfers
CREATE TABLE IF NOT EXISTS inventory_transfers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transfer_number TEXT UNIQUE NOT NULL,
    transfer_type TEXT CHECK (transfer_type IN ('hub_to_franchise', 'franchise_to_hub', 'franchise_to_franchise')) NOT NULL,
    from_tenant_id INTEGER NOT NULL,
    to_tenant_id INTEGER NOT NULL,
    from_storeroom_id INTEGER,
    to_storeroom_id INTEGER,
    status TEXT CHECK (status IN ('draft', 'submitted', 'approved', 'in_transit', 'delivered', 'cancelled')) DEFAULT 'draft',
    transfer_date DATE,
    expected_delivery_date DATE,
    notes TEXT,
    total_value DECIMAL(12,2) DEFAULT 0,
    purchase_request_id INTEGER,
    delivery_note_id INTEGER,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (to_tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (from_storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (to_storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(id),
    FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 12. Inventory Management (Database-based with Storeroom Integration)
CREATE TABLE IF NOT EXISTS inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    sku TEXT UNIQUE NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    quantity INTEGER DEFAULT 0,
    unit TEXT NOT NULL,
    reorder_level INTEGER DEFAULT 0,
    cost_price DECIMAL(10,2) DEFAULT 0,
    selling_price DECIMAL(10,2) DEFAULT 0,
    supplier TEXT,
    location TEXT,
    expiry_date DATE,
    storeroom_id INTEGER NOT NULL,
    tenant_id INTEGER NOT NULL,
    batch_number TEXT,
    barcode TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (storeroom_id) REFERENCES storerooms(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 13. Inventory Transactions (for tracking stock movements)
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_id INTEGER NOT NULL,
    transaction_type TEXT CHECK (transaction_type IN ('in', 'out', 'adjustment', 'transfer')) NOT NULL,
    quantity INTEGER NOT NULL,
    reference_type TEXT, -- 'purchase_order', 'delivery_note', 'manual_adjustment', 'transfer'
    reference_id INTEGER,
    reason TEXT NOT NULL,
    notes TEXT,
    unit_cost DECIMAL(10,2) DEFAULT 0,
    total_cost DECIMAL(12,2) DEFAULT 0,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_id) REFERENCES inventory(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 14. Inventory Transfer Items
CREATE TABLE IF NOT EXISTS inventory_transfer_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_transfer_id INTEGER NOT NULL,
    inventory_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL,
    unit TEXT NOT NULL,
    unit_value DECIMAL(10,2) DEFAULT 0,
    total_value DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_transfer_id) REFERENCES inventory_transfers(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_id) REFERENCES inventory(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_storerooms_tenant_id ON storerooms(tenant_id);
CREATE INDEX IF NOT EXISTS idx_storerooms_status ON storerooms(status);
CREATE INDEX IF NOT EXISTS idx_purchase_requests_requesting_tenant ON purchase_requests(requesting_tenant_id);
CREATE INDEX IF NOT EXISTS idx_purchase_requests_status ON purchase_requests(status);
CREATE INDEX IF NOT EXISTS idx_purchase_requests_storeroom ON purchase_requests(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_tenant ON purchase_orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_proforma_invoices_from_tenant ON proforma_invoices(from_tenant_id);
CREATE INDEX IF NOT EXISTS idx_proforma_invoices_to_tenant ON proforma_invoices(to_tenant_id);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_from_tenant ON delivery_notes(from_tenant_id);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_to_tenant ON delivery_notes(to_tenant_id);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_status ON delivery_notes(status);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_hub_status ON delivery_notes(hub_status);
CREATE INDEX IF NOT EXISTS idx_delivery_notes_franchise_status ON delivery_notes(franchise_status);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_inventory ON delivery_note_items(inventory_item_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_storeroom ON delivery_note_items(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_items_status ON delivery_note_items(item_status);
CREATE INDEX IF NOT EXISTS idx_delivery_note_status_history_dn ON delivery_note_status_history(delivery_note_id);
CREATE INDEX IF NOT EXISTS idx_delivery_note_status_history_type ON delivery_note_status_history(status_type);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_from_tenant ON payment_transactions(from_tenant_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_to_tenant ON payment_transactions(to_tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transfers_from_tenant ON inventory_transfers(from_tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transfers_to_tenant ON inventory_transfers(to_tenant_id);
-- Inventory indexes
CREATE INDEX IF NOT EXISTS idx_inventory_storeroom ON inventory(storeroom_id);
CREATE INDEX IF NOT EXISTS idx_inventory_tenant ON inventory(tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_sku ON inventory(sku);
CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category);
CREATE INDEX IF NOT EXISTS idx_inventory_active ON inventory(is_active);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_inventory ON inventory_transactions(inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_reference ON inventory_transactions(reference_type, reference_id);
