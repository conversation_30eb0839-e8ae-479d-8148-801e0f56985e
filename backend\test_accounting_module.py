#!/usr/bin/env python3
"""
Test script for AVINI Labs Accounting Module
Tests the core functionality of the accounting system
"""

import sys
import os
import sqlite3
from datetime import datetime, date

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.accounting_service import AccountingService
from services.accounts_payable_service import AccountsPayableService
from services.accounts_receivable_service import AccountsReceivableService
from services.financial_reporting_service import FinancialReportingService
from services.accounting_integration_service import AccountingIntegrationService

def test_database_connection():
    """Test database connection and table existence"""
    print("=" * 60)
    print("TESTING DATABASE CONNECTION")
    print("=" * 60)
    
    try:
        # Use the same database path as the database manager
        from database_manager import db_manager
        db_path = db_manager.db_path
        print(f"Using database: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if accounting tables exist
        accounting_table_names = [
            'chart_of_accounts', 'journal_entries', 'vendors', 'customers',
            'purchase_invoices', 'sales_invoices', 'financial_periods'
        ]

        existing_tables = []
        for table_name in accounting_table_names:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if cursor.fetchone():
                existing_tables.append(table_name)

        print(f"Found {len(existing_tables)}/{len(accounting_table_names)} accounting tables:")
        for table in existing_tables:
            print(f"  - {table}")

        # Check chart of accounts
        cursor.execute("SELECT COUNT(*) FROM chart_of_accounts")
        account_count = cursor.fetchone()[0]
        print(f"\nChart of accounts has {account_count} accounts")
        
        # Check some sample accounts
        cursor.execute("SELECT account_code, account_name FROM chart_of_accounts LIMIT 5")
        sample_accounts = cursor.fetchall()
        print("\nSample accounts:")
        for account in sample_accounts:
            print(f"  - {account[0]}: {account[1]}")
        
        conn.close()

        if len(existing_tables) >= 5:  # At least 5 core accounting tables
            print("✓ Database connection test PASSED")
            return True
        else:
            print("✗ Database connection test FAILED: Missing accounting tables")
            return False
        
    except Exception as e:
        print(f"✗ Database connection test FAILED: {str(e)}")
        return False

def test_accounting_service():
    """Test core accounting service functionality"""
    print("\n" + "=" * 60)
    print("TESTING ACCOUNTING SERVICE")
    print("=" * 60)
    
    try:
        service = AccountingService()
        
        # Test getting accounts by type
        asset_accounts = service.get_accounts_by_type('ASSET', 1)
        print(f"Found {len(asset_accounts)} asset accounts")
        
        # Test getting specific account
        cash_account = service.get_account_by_code('1110', 1)
        if cash_account:
            print(f"Cash account: {cash_account['account_name']}")
        else:
            print("Cash account not found")
        
        # Test creating a simple journal entry
        journal_data = {
            'journal_type': 'GENERAL',
            'transaction_date': date.today().isoformat(),
            'description': 'Test Journal Entry',
            'tenant_id': 1,
            'created_by': 1
        }
        
        # Get cash and revenue accounts for the entry
        cash_account = service.get_account_by_code('1110', 1)
        revenue_account = service.get_account_by_code('4110', 1)
        
        if cash_account and revenue_account:
            line_items = [
                {
                    'account_id': cash_account['id'],
                    'debit_amount': 1000,
                    'credit_amount': 0,
                    'description': 'Test cash receipt'
                },
                {
                    'account_id': revenue_account['id'],
                    'debit_amount': 0,
                    'credit_amount': 1000,
                    'description': 'Test revenue'
                }
            ]
            
            journal_id = service.create_journal_entry(journal_data, line_items)
            print(f"Created test journal entry with ID: {journal_id}")
            
            # Test posting the journal entry
            success = service.post_journal_entry(journal_id, 1)
            if success:
                print("✓ Journal entry posted successfully")
            else:
                print("✗ Failed to post journal entry")
        
        print("✓ Accounting service test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Accounting service test FAILED: {str(e)}")
        return False

def test_accounts_receivable():
    """Test accounts receivable functionality"""
    print("\n" + "=" * 60)
    print("TESTING ACCOUNTS RECEIVABLE")
    print("=" * 60)
    
    try:
        service = AccountsReceivableService()
        
        # Test creating a customer
        customer_data = {
            'customer_name': 'Test Customer',
            'customer_type': 'INDIVIDUAL',
            'payment_terms': 'NET_30',
            'credit_limit': 5000,
            'credit_days': 30,
            'tenant_id': 1
        }
        
        customer_id = service.create_customer(customer_data)
        print(f"Created test customer with ID: {customer_id}")
        
        # Test creating a sales invoice
        invoice_data = {
            'customer_id': customer_id,
            'invoice_date': date.today().isoformat(),
            'total_amount': 1500,
            'subtotal': 1200,
            'tax_amount': 300,
            'tenant_id': 1
        }
        
        line_items = [
            {
                'item_description': 'Test Service',
                'quantity': 1,
                'unit_price': 1200,
                'line_total': 1200
            }
        ]
        
        invoice_id = service.create_sales_invoice(invoice_data, line_items)
        print(f"Created test sales invoice with ID: {invoice_id}")
        
        print("✓ Accounts receivable test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Accounts receivable test FAILED: {str(e)}")
        return False

def test_financial_reporting():
    """Test financial reporting functionality"""
    print("\n" + "=" * 60)
    print("TESTING FINANCIAL REPORTING")
    print("=" * 60)
    
    try:
        service = FinancialReportingService()
        
        # Test trial balance
        trial_balance = service.generate_trial_balance(1)
        print(f"Generated trial balance with {len(trial_balance['accounts'])} accounts")
        print(f"Total debits: {trial_balance['totals']['total_debits']}")
        print(f"Total credits: {trial_balance['totals']['total_credits']}")
        print(f"Is balanced: {trial_balance['is_balanced']}")
        
        # Test balance sheet
        balance_sheet = service.generate_balance_sheet(1)
        print(f"Generated balance sheet")
        print(f"Total assets: {balance_sheet['assets']['total_assets']}")
        print(f"Total liabilities: {balance_sheet['liabilities']['total_liabilities']}")
        print(f"Total equity: {balance_sheet['equity']['total_equity']}")
        
        print("✓ Financial reporting test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Financial reporting test FAILED: {str(e)}")
        return False

def test_integration_service():
    """Test integration with existing systems"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATION SERVICE")
    print("=" * 60)
    
    try:
        service = AccountingIntegrationService()
        
        # Test setting up financial periods
        current_year = datetime.now().year

        # Check if periods already exist for this year
        from database_manager import db_manager
        existing_periods = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM financial_periods WHERE tenant_id = ? AND fiscal_year = ?",
            (1, current_year)
        )

        if existing_periods and existing_periods[0]['count'] > 0:
            print(f"Financial periods for {current_year} already exist ({existing_periods[0]['count']} periods)")
        else:
            period_ids = service.setup_financial_periods(1, current_year, 1)
            print(f"Created {len(period_ids)} financial periods for {current_year}")
        
        print("✓ Integration service test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Integration service test FAILED: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("AVINI Labs Accounting Module Test Suite")
    print("=" * 60)
    
    # Change to backend directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    tests = [
        test_database_connection,
        test_accounting_service,
        test_accounts_receivable,
        test_financial_reporting,
        test_integration_service
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} FAILED with exception: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {passed}")
    print(f"Tests failed: {failed}")
    print(f"Total tests: {passed + failed}")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED!")
        return 0
    else:
        print("❌ SOME TESTS FAILED!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
