import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Tabs, Tab, Table, Button, Form, Modal,
  Alert, Spinner, Badge, InputGroup
} from 'react-bootstrap';
import {
  FaCalculator, FaFileInvoiceDollar, FaReceipt, FaChartBar,
  FaPlus, FaDownload, FaEye, FaCog
} from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const TaxManagement = () => {
  const [activeTab, setActiveTab] = useState('gst-dashboard');
  const [taxData, setTaxData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadTaxData();
  }, [activeTab, dateRange]);

  const loadTaxData = async () => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = 1; // Get from context

      switch (activeTab) {
        case 'gst-dashboard':
          const gstData = await accountingService.getGSTDashboard(tenantId, dateRange);
          setTaxData(gstData);
          break;
        case 'gst-returns':
          const gstReturns = await accountingService.getGSTReturns(tenantId, dateRange);
          setTaxData(gstReturns);
          break;
        case 'tds-management':
          const tdsData = await accountingService.getTDSData(tenantId, dateRange);
          setTaxData(tdsData);
          break;
        case 'tcs-management':
          const tcsData = await accountingService.getTCSData(tenantId, dateRange);
          setTaxData(tcsData);
          break;
        default:
          break;
      }
    } catch (err) {
      setError(`Failed to load tax data: ${err.message}`);
      console.error('Tax data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async (reportType) => {
    try {
      const tenantId = 1;
      const response = await accountingService.generateTaxReport(reportType, tenantId, dateRange);

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${reportType}-${dateRange.endDate}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError(`Failed to generate ${reportType} report: ${err.message}`);
    }
  };

  const GSTDashboardTab = () => (
    <div>
      <Row className="mb-4">
        <Col md={8}>
          <h5>GST Dashboard</h5>
        </Col>
        <Col md={4}>
          <Row>
            <Col md={6}>
              <Form.Control
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
              />
            </Col>
            <Col md={6}>
              <Form.Control
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
              />
            </Col>
          </Row>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : taxData ? (
        <div>
          <Row className="mb-4">
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-success">₹{taxData.total_gst_collected?.toLocaleString()}</h4>
                  <p className="mb-0">GST Collected</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-primary">₹{taxData.total_gst_paid?.toLocaleString()}</h4>
                  <p className="mb-0">GST Paid</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-warning">₹{taxData.gst_payable?.toLocaleString()}</h4>
                  <p className="mb-0">GST Payable</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-info">{taxData.total_transactions}</h4>
                  <p className="mb-0">Total Transactions</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Card>
            <Card.Header>
              <Row>
                <Col>
                  <h6>Recent GST Transactions</h6>
                </Col>
                <Col className="text-end">
                  <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={() => handleGenerateReport('gst-summary')}
                  >
                    <FaDownload className="me-1" />
                    Export Summary
                  </Button>
                </Col>
              </Row>
            </Card.Header>
            <Card.Body>
              <Table responsive striped hover>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Transaction Type</th>
                    <th>Invoice/Bill #</th>
                    <th>Party Name</th>
                    <th>Taxable Amount</th>
                    <th>CGST</th>
                    <th>SGST</th>
                    <th>IGST</th>
                    <th>Total GST</th>
                  </tr>
                </thead>
                <tbody>
                  {taxData.recent_transactions?.map((transaction, index) => (
                    <tr key={index}>
                      <td>{new Date(transaction.transaction_date).toLocaleDateString()}</td>
                      <td>
                        <Badge bg={transaction.transaction_type === 'SALE' ? 'success' : 'primary'}>
                          {transaction.transaction_type}
                        </Badge>
                      </td>
                      <td>{transaction.document_number}</td>
                      <td>{transaction.party_name}</td>
                      <td>₹{transaction.taxable_amount?.toLocaleString()}</td>
                      <td>₹{transaction.cgst_amount?.toLocaleString()}</td>
                      <td>₹{transaction.sgst_amount?.toLocaleString()}</td>
                      <td>₹{transaction.igst_amount?.toLocaleString()}</td>
                      <td>₹{transaction.total_gst?.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </div>
      ) : null}
    </div>
  );

  const GSTReturnsTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <h5>GST Returns</h5>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="primary"
            className="me-2"
            onClick={() => handleGenerateReport('gstr1')}
          >
            <FaDownload className="me-1" />
            Generate GSTR-1
          </Button>
          <Button
            variant="success"
            onClick={() => handleGenerateReport('gstr3b')}
          >
            <FaDownload className="me-1" />
            Generate GSTR-3B
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : taxData ? (
        <div>
          <Row className="mb-4">
            <Col md={6}>
              <Card>
                <Card.Header>
                  <h6 className="text-primary">GSTR-1 Summary (Outward Supplies)</h6>
                </Card.Header>
                <Card.Body>
                  <Table responsive>
                    <tbody>
                      <tr>
                        <td>B2B Supplies</td>
                        <td className="text-end">₹{taxData.gstr1_data?.b2b_supplies?.toLocaleString()}</td>
                      </tr>
                      <tr>
                        <td>B2C Supplies</td>
                        <td className="text-end">₹{taxData.gstr1_data?.b2c_supplies?.toLocaleString()}</td>
                      </tr>
                      <tr>
                        <td>Exports</td>
                        <td className="text-end">₹{taxData.gstr1_data?.exports?.toLocaleString()}</td>
                      </tr>
                      <tr className="table-primary">
                        <td><strong>Total Outward Supplies</strong></td>
                        <td className="text-end"><strong>₹{taxData.gstr1_data?.total_outward?.toLocaleString()}</strong></td>
                      </tr>
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
            <Col md={6}>
              <Card>
                <Card.Header>
                  <h6 className="text-primary">GSTR-3B Summary</h6>
                </Card.Header>
                <Card.Body>
                  <Table responsive>
                    <tbody>
                      <tr>
                        <td>Output Tax Liability</td>
                        <td className="text-end">₹{taxData.gstr3b_data?.output_tax?.toLocaleString()}</td>
                      </tr>
                      <tr>
                        <td>Input Tax Credit</td>
                        <td className="text-end">₹{taxData.gstr3b_data?.input_tax_credit?.toLocaleString()}</td>
                      </tr>
                      <tr>
                        <td>Interest & Late Fee</td>
                        <td className="text-end">₹{taxData.gstr3b_data?.interest_late_fee?.toLocaleString()}</td>
                      </tr>
                      <tr className="table-warning">
                        <td><strong>Net Tax Payable</strong></td>
                        <td className="text-end"><strong>₹{taxData.gstr3b_data?.net_tax_payable?.toLocaleString()}</strong></td>
                      </tr>
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      ) : null}
    </div>
  );

  const TDSManagementTab = () => (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <h5>TDS Management</h5>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="primary"
            className="me-2"
            onClick={() => setShowModal(true)}
          >
            <FaPlus className="me-1" />
            Record TDS
          </Button>
          <Button
            variant="outline-primary"
            onClick={() => handleGenerateReport('tds-certificate')}
          >
            <FaDownload className="me-1" />
            TDS Certificate
          </Button>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : taxData ? (
        <div>
          <Row className="mb-4">
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-primary">₹{taxData.total_tds_deducted?.toLocaleString()}</h4>
                  <p className="mb-0">Total TDS Deducted</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-success">₹{taxData.total_tds_deposited?.toLocaleString()}</h4>
                  <p className="mb-0">TDS Deposited</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="text-center">
                <Card.Body>
                  <h4 className="text-warning">₹{taxData.tds_pending?.toLocaleString()}</h4>
                  <p className="mb-0">TDS Pending</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Table responsive striped hover>
            <thead>
              <tr>
                <th>Date</th>
                <th>Vendor</th>
                <th>TDS Section</th>
                <th>Payment Amount</th>
                <th>TDS Rate</th>
                <th>TDS Amount</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {taxData.tds_transactions?.map((transaction, index) => (
                <tr key={index}>
                  <td>{new Date(transaction.transaction_date).toLocaleDateString()}</td>
                  <td>{transaction.vendor_name}</td>
                  <td>{transaction.tds_section}</td>
                  <td>₹{transaction.payment_amount?.toLocaleString()}</td>
                  <td>{transaction.tds_rate}%</td>
                  <td>₹{transaction.tds_amount?.toLocaleString()}</td>
                  <td>
                    <Badge bg={transaction.status === 'DEPOSITED' ? 'success' : 'warning'}>
                      {transaction.status}
                    </Badge>
                  </td>
                  <td>
                    <Button
                      variant="outline-info"
                      size="sm"
                      onClick={() => handleGenerateReport('tds-certificate', transaction.id)}
                    >
                      <FaEye />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
      ) : null}
    </div>
  );

  return (
    <div className="tax-management">
      <Card>
        <Card.Header>
          <Row>
            <Col>
              <h5 className="mb-0 text-primary">
                <FaCalculator className="me-2" />
                Tax Management
              </h5>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body style={{background:"#f8f9fa"}}>
          {error && <Alert variant="danger">{error}</Alert>}

          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-3"
          >
            <Tab eventKey="gst-dashboard" title="GST Dashboard">
              <GSTDashboardTab />
            </Tab>
            <Tab eventKey="gst-returns" title="GST Returns">
              <GSTReturnsTab />
            </Tab>
            <Tab eventKey="tds-management" title="TDS Management">
              <TDSManagementTab />
            </Tab>
          </Tabs>
        </Card.Body>
      </Card>
    </div>
  );
};

export default TaxManagement;
