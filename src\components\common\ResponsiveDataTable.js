import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEllipsisV, faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import PropTypes from 'prop-types';

/**
 * Responsive DataTable component that adapts to mobile and desktop views
 * 
 * @param {Object} props - Component props
 * @param {Array} props.data - Data to display in the table
 * @param {Array} props.columns - Column definitions for desktop view
 * @param {Object} props.mobileCardConfig - Configuration for mobile card view
 * @param {Function} props.getRowActions - Function that returns actions for each row
 * @param {boolean} props.loading - Whether the data is loading
 * @param {string} props.emptyMessage - Message to display when there is no data
 * @param {number} props.currentPage - Current page number
 * @param {number} props.totalPages - Total number of pages
 * @param {Function} props.onPageChange - Function to call when page changes
 */
const ResponsiveDataTable = ({
  data = [],
  columns = [],
  mobileCardConfig = {},
  getRowActions = () => [],
  loading = false,
  emptyMessage = 'No data available',
  currentPage = 1,
  totalPages = 1,
  onPageChange
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [expandedRows, setExpandedRows] = useState(new Set());

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const toggleRowExpansion = (rowId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    return (
      <div className="d-flex justify-content-center mt-3">
        <Pagination>
          <Pagination.Prev
            disabled={currentPage === 1}
            onClick={() => onPageChange(currentPage - 1)}
          >
            <FontAwesomeIcon icon={faChevronLeft} />
          </Pagination.Prev>

          {startPage > 1 && (
            <>
              <Pagination.Item onClick={() => onPageChange(1)}>1</Pagination.Item>
              {startPage > 2 && <Pagination.Ellipsis />}
            </>
          )}

          {Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i).map(page => (
            <Pagination.Item
              key={page}
              active={page === currentPage}
              onClick={() => onPageChange(page)}
            >
              {page}
            </Pagination.Item>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <Pagination.Ellipsis />}
              <Pagination.Item onClick={() => onPageChange(totalPages)}>{totalPages}</Pagination.Item>
            </>
          )}

          <Pagination.Next
            disabled={currentPage === totalPages}
            onClick={() => onPageChange(currentPage + 1)}
          >
            <FontAwesomeIcon icon={faChevronRight} />
          </Pagination.Next>
        </Pagination>
      </div>
    );
  };

  const renderMobileCard = (row, index) => {
    const actions = getRowActions(row);
    const isExpanded = expandedRows.has(row.id);

    return (
      <Card key={row.id || index} className="mb-3">
        <Card.Body>
          <div className="d-flex justify-content-between align-items-start">
            <div className="flex-grow-1">
              {/* Title */}
              <h6 className="mb-1">
                {mobileCardConfig.title ? mobileCardConfig.title(row) : row.id}
              </h6>
              
              {/* Subtitle */}
              {mobileCardConfig.subtitle && (
                <p className="text-muted small mb-2">
                  {mobileCardConfig.subtitle(row)}
                </p>
              )}

              {/* Primary and Secondary fields */}
              <div className="row">
                {mobileCardConfig.primaryField && (
                  <div className="col-6">
                    <small className="text-muted d-block">
                      {columns.find(col => col.key === mobileCardConfig.primaryField)?.label || mobileCardConfig.primaryField}
                    </small>
                    <div className="fw-semibold">
                      {columns.find(col => col.key === mobileCardConfig.primaryField)?.render
                        ? columns.find(col => col.key === mobileCardConfig.primaryField).render(row[mobileCardConfig.primaryField], row)
                        : row[mobileCardConfig.primaryField]}
                    </div>
                  </div>
                )}
                
                {mobileCardConfig.secondaryField && (
                  <div className="col-6">
                    <small className="text-muted d-block">
                      {columns.find(col => col.key === mobileCardConfig.secondaryField)?.label || mobileCardConfig.secondaryField}
                    </small>
                    <div>
                      {columns.find(col => col.key === mobileCardConfig.secondaryField)?.render
                        ? columns.find(col => col.key === mobileCardConfig.secondaryField).render(row[mobileCardConfig.secondaryField], row)
                        : row[mobileCardConfig.secondaryField]}
                    </div>
                  </div>
                )}
              </div>

              {/* Status field */}
              {mobileCardConfig.statusField && (
                <div className="mt-2">
                  {columns.find(col => col.key === mobileCardConfig.statusField)?.render
                    ? columns.find(col => col.key === mobileCardConfig.statusField).render(row[mobileCardConfig.statusField], row)
                    : <Badge bg="secondary">{row[mobileCardConfig.statusField]}</Badge>}
                </div>
              )}

              {/* Expanded details */}
              {isExpanded && (
                <div className="mt-3 pt-3 border-top">
                  {columns.filter(col => 
                    col.key !== mobileCardConfig.primaryField && 
                    col.key !== mobileCardConfig.secondaryField && 
                    col.key !== mobileCardConfig.statusField
                  ).map(column => (
                    <div key={column.key} className="row mb-2">
                      <div className="col-4">
                        <small className="text-muted">{column.label}:</small>
                      </div>
                      <div className="col-8">
                        <small>
                          {column.render ? column.render(row[column.key], row) : row[column.key]}
                        </small>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="ms-3">
              {actions.length > 0 && (
                <div className="dropdown">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="dropdown-toggle"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <FontAwesomeIcon icon={faEllipsisV} />
                  </Button>
                  <ul className="dropdown-menu dropdown-menu-end">
                    {actions.map((action, actionIndex) => (
                      <li key={actionIndex}>
                        <button
                          className="dropdown-item"
                          onClick={action.onClick}
                        >
                          <FontAwesomeIcon icon={action.icon} className="me-2" />
                          {action.label}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* Expand/Collapse button */}
              <Button
                variant="link"
                size="sm"
                className="p-0 mt-2"
                onClick={() => toggleRowExpansion(row.id)}
              >
                {isExpanded ? 'Less' : 'More'}
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>
    );
  };

  const renderDesktopTable = () => (
    <div className="table-responsive">
      <Table striped hover>
        <thead>
          <tr>
            {columns.map(column => (
              <th key={column.key}>{column.label}</th>
            ))}
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => {
            const actions = getRowActions(row);
            return (
              <tr key={row.id || index}>
                {columns.map(column => (
                  <td key={column.key}>
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </td>
                ))}
                <td>
                  {actions.length > 0 && (
                    <div className="btn-group">
                      {actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          variant={action.variant || 'outline-primary'}
                          size="sm"
                          onClick={action.onClick}
                          title={action.label}
                        >
                          <FontAwesomeIcon icon={action.icon} />
                        </Button>
                      ))}
                    </div>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <div className="mt-2">Loading...</div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-5 text-muted">
        <div className="mb-2">📋</div>
        <div>{emptyMessage}</div>
      </div>
    );
  }

  return (
    <div>
      {isMobile ? (
        <div>
          {data.map((row, index) => renderMobileCard(row, index))}
        </div>
      ) : (
        renderDesktopTable()
      )}
      
      {renderPagination()}
    </div>
  );
};

ResponsiveDataTable.propTypes = {
  data: PropTypes.array.isRequired,
  columns: PropTypes.array.isRequired,
  mobileCardConfig: PropTypes.object,
  getRowActions: PropTypes.func,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string,
  currentPage: PropTypes.number,
  totalPages: PropTypes.number,
  onPageChange: PropTypes.func
};

export default ResponsiveDataTable;
