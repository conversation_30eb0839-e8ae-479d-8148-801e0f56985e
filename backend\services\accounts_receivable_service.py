"""
AVINI Labs Accounts Receivable Service
Manages customer invoices, payments, and receivable transactions
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
from decimal import Decimal

from database_manager import db_manager
from services.accounting_service import AccountingService

logger = logging.getLogger(__name__)

class AccountsReceivableService:
    """Service for managing accounts receivable operations"""
    
    def __init__(self):
        self.db = db_manager
        self.accounting_service = AccountingService()
    
    # ============================================================================
    # CUSTOMER MANAGEMENT
    # ============================================================================
    
    def create_customer(self, customer_data: Dict) -> int:
        """Create a new customer"""
        try:
            # Validate required fields
            required_fields = ['customer_name', 'tenant_id']
            for field in required_fields:
                if field not in customer_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate customer code if not provided
            if 'customer_code' not in customer_data:
                customer_data['customer_code'] = self._generate_customer_code(customer_data['tenant_id'])
            
            # Set defaults
            customer_data.setdefault('customer_type', 'INDIVIDUAL')
            customer_data.setdefault('payment_terms', 'IMMEDIATE')
            customer_data.setdefault('credit_limit', 0)
            customer_data.setdefault('credit_days', 0)
            customer_data.setdefault('is_active', True)
            customer_data.setdefault('country', 'India')
            
            customer_id = self.db.insert_record('customers', customer_data)
            logger.info(f"Created customer {customer_data['customer_code']} with ID {customer_id}")
            return customer_id
            
        except Exception as e:
            logger.error(f"Error creating customer: {str(e)}")
            raise
    
    def get_customer_by_code(self, customer_code: str, tenant_id: int) -> Optional[Dict]:
        """Get customer by customer code"""
        try:
            customers = self.db.execute_query(
                "SELECT * FROM customers WHERE customer_code = ? AND tenant_id = ?",
                (customer_code, tenant_id)
            )
            return customers[0] if customers else None
        except Exception as e:
            logger.error(f"Error getting customer by code {customer_code}: {str(e)}")
            raise
    
    def get_active_customers(self, tenant_id: int) -> List[Dict]:
        """Get all active customers for a tenant"""
        try:
            return self.db.execute_query(
                "SELECT * FROM customers WHERE tenant_id = ? AND is_active = 1 ORDER BY customer_name",
                (tenant_id,)
            )
        except Exception as e:
            logger.error(f"Error getting active customers: {str(e)}")
            raise
    
    # ============================================================================
    # SALES INVOICE MANAGEMENT
    # ============================================================================
    
    def create_sales_invoice(self, invoice_data: Dict, line_items: List[Dict]) -> int:
        """Create a new sales invoice"""
        try:
            # Validate required fields
            required_fields = ['customer_id', 'invoice_date', 'total_amount', 'tenant_id']
            for field in required_fields:
                if field not in invoice_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate invoice number if not provided
            if 'invoice_number' not in invoice_data:
                invoice_data['invoice_number'] = self._generate_invoice_number('SALES', invoice_data['tenant_id'])
            
            # Calculate due date if not provided
            if 'due_date' not in invoice_data:
                customer = self.db.execute_query("SELECT credit_days FROM customers WHERE id = ?", (invoice_data['customer_id'],))
                credit_days = customer[0]['credit_days'] if customer else 0
                due_date = datetime.strptime(invoice_data['invoice_date'], '%Y-%m-%d') + timedelta(days=credit_days)
                invoice_data['due_date'] = due_date.strftime('%Y-%m-%d')
            
            # Set defaults
            invoice_data.setdefault('status', 'DRAFT')
            invoice_data.setdefault('subtotal', 0)
            invoice_data.setdefault('tax_amount', 0)
            invoice_data.setdefault('discount_amount', 0)
            invoice_data.setdefault('paid_amount', 0)
            invoice_data.setdefault('currency', 'INR')
            invoice_data.setdefault('exchange_rate', 1.0000)
            
            # Calculate outstanding amount
            invoice_data['outstanding_amount'] = invoice_data['total_amount'] - invoice_data['paid_amount']
            
            # Insert invoice
            invoice_id = self.db.insert_record('sales_invoices', invoice_data)
            
            # Insert line items
            for i, line_item in enumerate(line_items, 1):
                line_item.update({
                    'sales_invoice_id': invoice_id,
                    'line_number': i
                })
                self.db.insert_record('sales_invoice_lines', line_item)
            
            logger.info(f"Created sales invoice {invoice_data['invoice_number']} with ID {invoice_id}")
            return invoice_id
            
        except Exception as e:
            logger.error(f"Error creating sales invoice: {str(e)}")
            raise
    
    def post_sales_invoice(self, invoice_id: int, posted_by: int) -> bool:
        """Post a sales invoice and create journal entry"""
        try:
            # Get invoice
            invoice = self.db.execute_query("SELECT * FROM sales_invoices WHERE id = ?", (invoice_id,))
            if not invoice:
                raise ValueError(f"Sales invoice with ID {invoice_id} not found")
            
            invoice = invoice[0]
            if invoice['status'] != 'DRAFT':
                raise ValueError("Only draft invoices can be posted")
            
            # Get line items
            line_items = self.db.execute_query(
                "SELECT * FROM sales_invoice_lines WHERE sales_invoice_id = ?",
                (invoice_id,)
            )
            
            # Create journal entry
            journal_data = {
                'journal_type': 'SALES',
                'transaction_date': invoice['invoice_date'],
                'description': f"Sales Invoice {invoice['invoice_number']}",
                'reference_type': 'INVOICE',
                'reference_id': invoice_id,
                'reference_number': invoice['invoice_number'],
                'tenant_id': invoice['tenant_id'],
                'created_by': posted_by,
                'is_auto_generated': True
            }
            
            # Create journal entry lines
            journal_lines = []
            
            # Debit accounts receivable
            ar_account = self.accounting_service.get_account_by_code('1210', invoice['tenant_id'])  # Trade Receivables
            if ar_account:
                journal_lines.append({
                    'account_id': ar_account['id'],
                    'debit_amount': invoice['total_amount'],
                    'credit_amount': 0,
                    'description': f"Accounts Receivable - {invoice['invoice_number']}"
                })
            
            # Credit revenue accounts for line items
            for line_item in line_items:
                if line_item.get('account_id'):
                    journal_lines.append({
                        'account_id': line_item['account_id'],
                        'debit_amount': 0,
                        'credit_amount': line_item['line_total'],
                        'description': line_item['item_description']
                    })
            
            # If no specific accounts, use default revenue account
            if len(journal_lines) == 1:  # Only AR account added
                revenue_account = self.accounting_service.get_account_by_code('4110', invoice['tenant_id'])  # Pathology Services
                if revenue_account:
                    journal_lines.append({
                        'account_id': revenue_account['id'],
                        'debit_amount': 0,
                        'credit_amount': invoice['subtotal'],
                        'description': f"Sales Revenue - {invoice['invoice_number']}"
                    })
            
            # Add tax if applicable
            if invoice['tax_amount'] > 0:
                tax_account = self.accounting_service.get_account_by_code('2310', invoice['tenant_id'])  # GST Payable
                if tax_account:
                    journal_lines.append({
                        'account_id': tax_account['id'],
                        'debit_amount': 0,
                        'credit_amount': invoice['tax_amount'],
                        'description': f"Output Tax - {invoice['invoice_number']}"
                    })
            
            # Create and post journal entry
            journal_id = self.accounting_service.create_journal_entry(journal_data, journal_lines)
            self.accounting_service.post_journal_entry(journal_id, posted_by)
            
            # Update invoice status
            update_data = {
                'status': 'SENT',
                'journal_entry_id': journal_id
            }
            self.db.update_record('sales_invoices', invoice_id, update_data)
            
            logger.info(f"Posted sales invoice {invoice['invoice_number']}")
            return True
            
        except Exception as e:
            logger.error(f"Error posting sales invoice {invoice_id}: {str(e)}")
            raise
    
    # ============================================================================
    # CUSTOMER PAYMENT MANAGEMENT
    # ============================================================================
    
    def create_customer_payment(self, payment_data: Dict, invoice_allocations: List[Dict]) -> int:
        """Create a customer payment and allocate to invoices"""
        try:
            # Validate required fields
            required_fields = ['customer_id', 'payment_date', 'payment_method', 'total_amount', 'tenant_id']
            for field in required_fields:
                if field not in payment_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Generate payment number if not provided
            if 'payment_number' not in payment_data:
                payment_data['payment_number'] = self._generate_payment_number(payment_data['tenant_id'])
            
            # Set defaults
            payment_data.setdefault('status', 'DRAFT')
            payment_data.setdefault('currency', 'INR')
            payment_data.setdefault('exchange_rate', 1.0000)
            
            # Insert payment
            payment_id = self.db.insert_record('customer_payments', payment_data)
            
            # Insert allocations
            total_allocated = 0
            for allocation in invoice_allocations:
                allocation.update({
                    'payment_id': payment_id
                })
                self.db.insert_record('customer_payment_allocations', allocation)
                total_allocated += allocation['allocated_amount']
            
            # Validate total allocation
            if abs(total_allocated - payment_data['total_amount']) > 0.01:
                raise ValueError(f"Total allocated amount {total_allocated} does not match payment amount {payment_data['total_amount']}")
            
            logger.info(f"Created customer payment {payment_data['payment_number']} with ID {payment_id}")
            return payment_id
            
        except Exception as e:
            logger.error(f"Error creating customer payment: {str(e)}")
            raise
    
    def process_customer_payment(self, payment_id: int, processed_by: int) -> bool:
        """Process a customer payment and create journal entry"""
        try:
            # Get payment
            payment = self.db.execute_query("SELECT * FROM customer_payments WHERE id = ?", (payment_id,))
            if not payment:
                raise ValueError(f"Customer payment with ID {payment_id} not found")
            
            payment = payment[0]
            if payment['status'] != 'DRAFT':
                raise ValueError("Only draft payments can be processed")
            
            # Get allocations
            allocations = self.db.execute_query(
                """SELECT cpa.*, si.invoice_number 
                   FROM customer_payment_allocations cpa
                   JOIN sales_invoices si ON cpa.sales_invoice_id = si.id
                   WHERE cpa.payment_id = ?""",
                (payment_id,)
            )
            
            # Create journal entry
            journal_data = {
                'journal_type': 'CASH_RECEIPT' if payment['payment_method'] == 'CASH' else 'BANK_RECEIPT',
                'transaction_date': payment['payment_date'],
                'description': f"Customer Payment {payment['payment_number']}",
                'reference_type': 'PAYMENT',
                'reference_id': payment_id,
                'reference_number': payment['payment_number'],
                'tenant_id': payment['tenant_id'],
                'created_by': processed_by,
                'is_auto_generated': True
            }
            
            # Create journal entry lines
            journal_lines = []
            
            # Debit cash/bank account
            if payment['payment_method'] == 'CASH':
                cash_account = self.accounting_service.get_account_by_code('1110', payment['tenant_id'])  # Cash in Hand
                if cash_account:
                    journal_lines.append({
                        'account_id': cash_account['id'],
                        'debit_amount': payment['total_amount'],
                        'credit_amount': 0,
                        'description': f"Cash receipt - {payment['payment_number']}"
                    })
            else:
                # Use bank account if specified, otherwise default bank account
                bank_account_id = payment.get('bank_account_id')
                if not bank_account_id:
                    bank_account = self.accounting_service.get_account_by_code('1121', payment['tenant_id'])  # Default bank
                    bank_account_id = bank_account['id'] if bank_account else None
                
                if bank_account_id:
                    journal_lines.append({
                        'account_id': bank_account_id,
                        'debit_amount': payment['total_amount'],
                        'credit_amount': 0,
                        'description': f"Bank receipt - {payment['payment_number']}"
                    })
            
            # Credit accounts receivable
            ar_account = self.accounting_service.get_account_by_code('1210', payment['tenant_id'])  # Trade Receivables
            if ar_account:
                journal_lines.append({
                    'account_id': ar_account['id'],
                    'debit_amount': 0,
                    'credit_amount': payment['total_amount'],
                    'description': f"Payment from customer - {payment['payment_number']}"
                })
            
            # Create and post journal entry
            journal_id = self.accounting_service.create_journal_entry(journal_data, journal_lines)
            self.accounting_service.post_journal_entry(journal_id, processed_by)
            
            # Update payment status
            self.db.update_record('customer_payments', payment_id, {
                'status': 'PROCESSED',
                'journal_entry_id': journal_id
            })
            
            # Update invoice paid amounts
            for allocation in allocations:
                invoice_id = allocation['sales_invoice_id']
                allocated_amount = allocation['allocated_amount']
                
                # Get current invoice
                invoice = self.db.execute_query("SELECT * FROM sales_invoices WHERE id = ?", (invoice_id,))
                if invoice:
                    invoice = invoice[0]
                    new_paid_amount = invoice['paid_amount'] + allocated_amount
                    new_outstanding = invoice['total_amount'] - new_paid_amount
                    
                    # Determine new status
                    if new_outstanding <= 0.01:
                        new_status = 'PAID'
                    elif new_paid_amount > 0:
                        new_status = 'PARTIALLY_PAID'
                    else:
                        new_status = invoice['status']
                    
                    self.db.update_record('sales_invoices', invoice_id, {
                        'paid_amount': new_paid_amount,
                        'outstanding_amount': new_outstanding,
                        'status': new_status
                    })
            
            logger.info(f"Processed customer payment {payment['payment_number']}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing customer payment {payment_id}: {str(e)}")
            raise
    
    # ============================================================================
    # REPORTING
    # ============================================================================
    
    def get_aged_receivables_report(self, tenant_id: int, as_of_date: str = None) -> List[Dict]:
        """Generate aged receivables report"""
        try:
            if not as_of_date:
                as_of_date = date.today().isoformat()
            
            query = """
                SELECT 
                    c.customer_code,
                    c.customer_name,
                    si.invoice_number,
                    si.invoice_date,
                    si.due_date,
                    si.total_amount,
                    si.outstanding_amount,
                    CASE 
                        WHEN si.due_date >= ? THEN 'Current'
                        WHEN si.due_date >= date(?, '-30 days') THEN '1-30 Days'
                        WHEN si.due_date >= date(?, '-60 days') THEN '31-60 Days'
                        WHEN si.due_date >= date(?, '-90 days') THEN '61-90 Days'
                        ELSE 'Over 90 Days'
                    END as aging_bucket,
                    julianday(?) - julianday(si.due_date) as days_overdue
                FROM sales_invoices si
                JOIN customers c ON si.customer_id = c.id
                WHERE si.tenant_id = ? 
                    AND si.outstanding_amount > 0
                    AND si.status IN ('SENT', 'PARTIALLY_PAID')
                ORDER BY c.customer_name, si.due_date
            """
            
            return self.db.execute_query(query, (as_of_date, as_of_date, as_of_date, as_of_date, as_of_date, tenant_id))
            
        except Exception as e:
            logger.error(f"Error generating aged receivables report: {str(e)}")
            raise
    
    def generate_customer_statement(self, customer_id: int, start_date: str, end_date: str) -> Dict:
        """Generate customer statement for a date range"""
        try:
            # Get customer details
            customer = self.db.execute_query("SELECT * FROM customers WHERE id = ?", (customer_id,))
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")

            customer = customer[0]

            # Get opening balance
            opening_balance_query = """
                SELECT COALESCE(SUM(
                    CASE WHEN si.status = 'SENT' THEN si.total_amount ELSE 0 END -
                    CASE WHEN cp.status = 'PROCESSED' THEN cp.total_amount ELSE 0 END
                ), 0) as opening_balance
                FROM sales_invoices si
                LEFT JOIN customer_payments cp ON si.customer_id = cp.customer_id
                WHERE si.customer_id = ? AND si.invoice_date < ?
            """
            opening_result = self.db.execute_query(opening_balance_query, (customer_id, start_date))
            opening_balance = opening_result[0]['opening_balance'] if opening_result else 0

            # Get transactions for the period
            transactions_query = """
                SELECT
                    'INVOICE' as type,
                    si.invoice_date as date,
                    si.invoice_number as reference,
                    si.description as description,
                    si.total_amount as debit,
                    0 as credit,
                    si.outstanding_amount
                FROM sales_invoices si
                WHERE si.customer_id = ?
                    AND si.invoice_date BETWEEN ? AND ?
                    AND si.status IN ('SENT', 'PAID', 'PARTIALLY_PAID')

                UNION ALL

                SELECT
                    'PAYMENT' as type,
                    cp.payment_date as date,
                    cp.payment_number as reference,
                    cp.notes as description,
                    0 as debit,
                    cp.total_amount as credit,
                    0 as outstanding_amount
                FROM customer_payments cp
                WHERE cp.customer_id = ?
                    AND cp.payment_date BETWEEN ? AND ?
                    AND cp.status = 'PROCESSED'

                ORDER BY date, type
            """

            transactions = self.db.execute_query(
                transactions_query,
                (customer_id, start_date, end_date, customer_id, start_date, end_date)
            )

            # Calculate running balance
            running_balance = opening_balance
            for transaction in transactions:
                running_balance += transaction['debit'] - transaction['credit']
                transaction['balance'] = running_balance

            # Get current outstanding
            current_outstanding_query = """
                SELECT COALESCE(SUM(outstanding_amount), 0) as total_outstanding
                FROM sales_invoices
                WHERE customer_id = ? AND outstanding_amount > 0
            """
            outstanding_result = self.db.execute_query(current_outstanding_query, (customer_id,))
            current_outstanding = outstanding_result[0]['total_outstanding'] if outstanding_result else 0

            return {
                'customer': customer,
                'statement_period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'opening_balance': opening_balance,
                'closing_balance': running_balance,
                'current_outstanding': current_outstanding,
                'transactions': transactions,
                'summary': {
                    'total_invoices': sum(1 for t in transactions if t['type'] == 'INVOICE'),
                    'total_payments': sum(1 for t in transactions if t['type'] == 'PAYMENT'),
                    'total_debits': sum(t['debit'] for t in transactions),
                    'total_credits': sum(t['credit'] for t in transactions)
                }
            }

        except Exception as e:
            logger.error(f"Error generating customer statement: {str(e)}")
            raise

    def check_credit_limit(self, customer_id: int, additional_amount: Decimal = 0) -> Dict:
        """Check if customer is within credit limit"""
        try:
            # Get customer details
            customer = self.db.execute_query("SELECT * FROM customers WHERE id = ?", (customer_id,))
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")

            customer = customer[0]
            credit_limit = Decimal(str(customer.get('credit_limit', 0)))

            # Get current outstanding
            outstanding_query = """
                SELECT COALESCE(SUM(outstanding_amount), 0) as total_outstanding
                FROM sales_invoices
                WHERE customer_id = ? AND outstanding_amount > 0
            """
            outstanding_result = self.db.execute_query(outstanding_query, (customer_id,))
            current_outstanding = Decimal(str(outstanding_result[0]['total_outstanding'] if outstanding_result else 0))

            # Calculate available credit
            total_exposure = current_outstanding + Decimal(str(additional_amount))
            available_credit = credit_limit - current_outstanding

            return {
                'customer_code': customer['customer_code'],
                'customer_name': customer['customer_name'],
                'credit_limit': float(credit_limit),
                'current_outstanding': float(current_outstanding),
                'additional_amount': float(additional_amount),
                'total_exposure': float(total_exposure),
                'available_credit': float(available_credit),
                'within_limit': total_exposure <= credit_limit,
                'credit_utilization_percent': float((total_exposure / credit_limit * 100) if credit_limit > 0 else 0)
            }

        except Exception as e:
            logger.error(f"Error checking credit limit: {str(e)}")
            raise

    def generate_dunning_letters(self, tenant_id: int, overdue_days: int = 30) -> List[Dict]:
        """Generate dunning letters for overdue customers"""
        try:
            query = """
                SELECT
                    c.id as customer_id,
                    c.customer_code,
                    c.customer_name,
                    c.email,
                    c.phone,
                    COUNT(si.id) as overdue_invoices,
                    SUM(si.outstanding_amount) as total_overdue,
                    MIN(si.due_date) as oldest_due_date,
                    MAX(julianday('now') - julianday(si.due_date)) as max_days_overdue
                FROM customers c
                JOIN sales_invoices si ON c.id = si.customer_id
                WHERE c.tenant_id = ?
                    AND si.outstanding_amount > 0
                    AND julianday('now') - julianday(si.due_date) >= ?
                    AND si.status IN ('SENT', 'PARTIALLY_PAID')
                GROUP BY c.id, c.customer_code, c.customer_name, c.email, c.phone
                ORDER BY max_days_overdue DESC, total_overdue DESC
            """

            overdue_customers = self.db.execute_query(query, (tenant_id, overdue_days))

            # Generate dunning letter data for each customer
            dunning_letters = []
            for customer in overdue_customers:
                # Get detailed invoice information
                invoice_query = """
                    SELECT invoice_number, invoice_date, due_date, outstanding_amount,
                           julianday('now') - julianday(due_date) as days_overdue
                    FROM sales_invoices
                    WHERE customer_id = ? AND outstanding_amount > 0
                    ORDER BY due_date
                """
                invoices = self.db.execute_query(invoice_query, (customer['customer_id'],))

                # Determine dunning level based on days overdue
                max_days = customer['max_days_overdue']
                if max_days >= 90:
                    dunning_level = 'FINAL_NOTICE'
                elif max_days >= 60:
                    dunning_level = 'SECOND_NOTICE'
                else:
                    dunning_level = 'FIRST_NOTICE'

                dunning_letters.append({
                    'customer': customer,
                    'dunning_level': dunning_level,
                    'overdue_invoices': invoices,
                    'generated_date': date.today().isoformat()
                })

            return dunning_letters

        except Exception as e:
            logger.error(f"Error generating dunning letters: {str(e)}")
            raise

    def get_sales_invoice_by_number(self, invoice_number: str, tenant_id: int) -> Optional[Dict]:
        """Get sales invoice by invoice number"""
        try:
            invoices = self.db.execute_query(
                "SELECT * FROM sales_invoices WHERE invoice_number = ? AND tenant_id = ?",
                (invoice_number, tenant_id)
            )
            return invoices[0] if invoices else None
        except Exception as e:
            logger.error(f"Error getting sales invoice by number: {str(e)}")
            raise

    def get_customer_by_code(self, customer_code: str, tenant_id: int) -> Optional[Dict]:
        """Get customer by customer code"""
        try:
            customers = self.db.execute_query(
                "SELECT * FROM customers WHERE customer_code = ? AND tenant_id = ?",
                (customer_code, tenant_id)
            )
            return customers[0] if customers else None
        except Exception as e:
            logger.error(f"Error getting customer by code: {str(e)}")
            raise

    def get_sales_invoices(self, tenant_id: int) -> List[Dict]:
        """Get all sales invoices for a tenant"""
        try:
            query = """
                SELECT si.*, c.customer_name, c.customer_code
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.tenant_id = ?
                ORDER BY si.invoice_date DESC, si.invoice_number DESC
            """
            return self.db.execute_query(query, (tenant_id,))
        except Exception as e:
            logger.error(f"Error getting sales invoices: {str(e)}")
            raise

    def get_customer_payments(self, tenant_id: int) -> List[Dict]:
        """Get all customer payments for a tenant"""
        try:
            query = """
                SELECT cp.*, c.customer_name, c.customer_code
                FROM customer_payments cp
                LEFT JOIN customers c ON cp.customer_id = c.id
                WHERE cp.tenant_id = ?
                ORDER BY cp.payment_date DESC, cp.payment_number DESC
            """
            return self.db.execute_query(query, (tenant_id,))
        except Exception as e:
            logger.error(f"Error getting customer payments: {str(e)}")
            raise

    def update_customer(self, customer_id: int, customer_data: Dict) -> bool:
        """Update an existing customer"""
        try:
            # Remove id and tenant_id from update data if present
            update_data = {k: v for k, v in customer_data.items() if k not in ['id', 'tenant_id']}

            # Update customer
            self.db.update_record('customers', customer_id, update_data)

            logger.info(f"Updated customer with ID {customer_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating customer {customer_id}: {str(e)}")
            raise

    def update_sales_invoice(self, invoice_id: int, invoice_data: Dict) -> bool:
        """Update an existing sales invoice"""
        try:
            # Remove id and tenant_id from update data if present
            update_data = {k: v for k, v in invoice_data.items() if k not in ['id', 'tenant_id']}

            # Update invoice
            self.db.update_record('sales_invoices', invoice_id, update_data)

            logger.info(f"Updated sales invoice with ID {invoice_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating sales invoice {invoice_id}: {str(e)}")
            raise

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    def _generate_customer_code(self, tenant_id: int) -> str:
        """Generate a unique customer code"""
        try:
            # Get the latest CUS customer code for this tenant
            result = self.db.execute_query(
                "SELECT customer_code FROM customers WHERE tenant_id = ? AND customer_code LIKE 'CUS%' ORDER BY customer_code DESC LIMIT 1",
                (tenant_id,)
            )

            if result:
                last_code = result[0]['customer_code']
                # Extract number from format like "CUS001"
                if last_code.startswith('CUS'):
                    next_num = int(last_code[3:]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1

            return f"CUS{next_num:03d}"

        except Exception as e:
            logger.error(f"Error generating customer code: {str(e)}")
            return f"CUS{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _generate_invoice_number(self, invoice_type: str, tenant_id: int) -> str:
        """Generate a unique invoice number"""
        try:
            prefix = 'SI' if invoice_type == 'SALES' else 'PI'
            year = datetime.now().year
            
            result = self.db.execute_query(
                "SELECT invoice_number FROM sales_invoices WHERE tenant_id = ? ORDER BY id DESC LIMIT 1",
                (tenant_id,)
            )
            
            if result:
                last_number = result[0]['invoice_number']
                if f"{prefix}-{year}" in last_number:
                    parts = last_number.split('-')
                    next_num = int(parts[-1]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            return f"{prefix}-{year}-{next_num:04d}"
            
        except Exception as e:
            logger.error(f"Error generating invoice number: {str(e)}")
            return f"{prefix}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _generate_payment_number(self, tenant_id: int) -> str:
        """Generate a unique payment number"""
        try:
            year = datetime.now().year
            
            result = self.db.execute_query(
                "SELECT payment_number FROM customer_payments WHERE tenant_id = ? ORDER BY id DESC LIMIT 1",
                (tenant_id,)
            )
            
            if result:
                last_number = result[0]['payment_number']
                if f"CP-{year}" in last_number:
                    parts = last_number.split('-')
                    next_num = int(parts[-1]) + 1
                else:
                    next_num = 1
            else:
                next_num = 1
            
            return f"CP-{year}-{next_num:04d}"
            
        except Exception as e:
            logger.error(f"Error generating payment number: {str(e)}")
            return f"CP-{datetime.now().strftime('%Y%m%d%H%M%S')}"
