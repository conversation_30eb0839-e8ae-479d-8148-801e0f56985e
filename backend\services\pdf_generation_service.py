"""
PDF Generation Service for Delivery Notes

This service generates courier-friendly PDF delivery notes following international shipping document standards.
The PDFs exclude all pricing information for security and are optimized for printing and courier handoff.

Features:
- International shipping document format
- Courier-optimized layout (no pricing information)
- Professional appearance with company branding
- Barcode generation for tracking
- QR code for digital verification
- Optimized for A4 printing
"""

import logging
import io
import base64
from datetime import datetime
from typing import Dict, List, Optional
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
from reportlab.graphics.barcode import code128
from reportlab.graphics import renderPDF
from reportlab.graphics.shapes import Drawing
import qrcode
from PIL import Image as PILImage

logger = logging.getLogger(__name__)

class PDFGenerationService:
    """Service for generating courier-friendly delivery note PDFs"""
    
    def __init__(self):
        self.page_width, self.page_height = A4
        self.margin = 20 * mm
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the PDF"""
        # Company header style
        self.styles.add(ParagraphStyle(
            name='CompanyHeader',
            parent=self.styles['Heading1'],
            fontSize=18,
            textColor=colors.darkblue,
            alignment=TA_CENTER,
            spaceAfter=6
        ))
        
        # Document title style
        self.styles.add(ParagraphStyle(
            name='DocumentTitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=colors.black,
            alignment=TA_CENTER,
            spaceAfter=12
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=12,
            textColor=colors.darkblue,
            spaceBefore=12,
            spaceAfter=6
        ))
        
        # Field label style
        self.styles.add(ParagraphStyle(
            name='FieldLabel',
            parent=self.styles['Normal'],
            fontSize=9,
            textColor=colors.grey,
            spaceBefore=2
        ))
        
        # Field value style
        self.styles.add(ParagraphStyle(
            name='FieldValue',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.black,
            spaceAfter=4
        ))
    
    def generate_delivery_note_pdf(self, delivery_note_data: Dict) -> bytes:
        """
        Generate courier-friendly delivery note PDF
        Excludes all pricing information for security
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            # Build PDF content
            story = []
            
            # Header section
            story.extend(self._build_header(delivery_note_data))
            story.append(Spacer(1, 12))
            
            # Delivery information section
            story.extend(self._build_delivery_info(delivery_note_data))
            story.append(Spacer(1, 12))
            
            # Sender and recipient information
            story.extend(self._build_address_section(delivery_note_data))
            story.append(Spacer(1, 12))
            
            # Items section (without pricing)
            story.extend(self._build_items_section(delivery_note_data))
            story.append(Spacer(1, 12))
            
            # Transport and handling instructions
            story.extend(self._build_transport_section(delivery_note_data))
            story.append(Spacer(1, 12))
            
            # Signature section
            story.extend(self._build_signature_section())
            
            # Footer
            story.extend(self._build_footer(delivery_note_data))
            
            # Build PDF
            doc.build(story)
            
            # Get PDF bytes
            pdf_bytes = buffer.getvalue()
            buffer.close()
            
            logger.info(f"Successfully generated PDF for delivery note {delivery_note_data.get('delivery_number')}")
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"Error generating PDF for delivery note: {e}")
            raise
    
    def _build_header(self, data: Dict) -> List:
        """Build PDF header with company info and document title"""
        elements = []
        
        # Company header
        company_name = "AVINI LABS"
        elements.append(Paragraph(company_name, self.styles['CompanyHeader']))
        
        # Document title
        elements.append(Paragraph("DELIVERY NOTE", self.styles['DocumentTitle']))
        
        # Delivery note number and date
        info_data = [
            ['Delivery Note #:', data.get('delivery_number', 'N/A')],
            ['Date:', data.get('created_at', datetime.now().strftime('%Y-%m-%d'))],
            ['Status:', data.get('hub_status', 'prepared').title()]
        ]
        
        info_table = Table(info_data, colWidths=[80*mm, 80*mm])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(info_table)
        
        return elements
    
    def _build_delivery_info(self, data: Dict) -> List:
        """Build delivery information section"""
        elements = []
        
        elements.append(Paragraph("DELIVERY INFORMATION", self.styles['SectionHeader']))
        
        delivery_data = [
            ['Delivery Type:', data.get('delivery_type', 'N/A').replace('_', ' ').title()],
            ['Mode of Transport:', data.get('mode_of_transport', 'Road Transport')],
            ['Expected Delivery:', data.get('expected_delivery_date', 'TBD')],
            ['Tracking Number:', data.get('tracking_number', 'Will be assigned')]
        ]
        
        delivery_table = Table(delivery_data, colWidths=[60*mm, 100*mm])
        delivery_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
        ]))
        elements.append(delivery_table)
        
        return elements
    
    def _build_address_section(self, data: Dict) -> List:
        """Build sender and recipient address section"""
        elements = []
        
        elements.append(Paragraph("SENDER & RECIPIENT INFORMATION", self.styles['SectionHeader']))
        
        # Get tenant information
        from_tenant = data.get('from_tenant', {})
        to_tenant = data.get('to_tenant', {})
        
        address_data = [
            ['FROM (SENDER)', 'TO (RECIPIENT)'],
            [
                f"{from_tenant.get('name', 'N/A')}<br/>"
                f"{from_tenant.get('address', 'Address not available')}<br/>"
                f"Contact: {from_tenant.get('contact_number', 'N/A')}<br/>"
                f"Email: {from_tenant.get('email', 'N/A')}",
                
                f"{to_tenant.get('name', 'N/A')}<br/>"
                f"{to_tenant.get('address', 'Address not available')}<br/>"
                f"Contact: {to_tenant.get('contact_number', 'N/A')}<br/>"
                f"Email: {to_tenant.get('email', 'N/A')}"
            ]
        ]
        
        address_table = Table(address_data, colWidths=[85*mm, 85*mm])
        address_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))
        elements.append(address_table)
        
        return elements
    
    def _build_items_section(self, data: Dict) -> List:
        """Build items section WITHOUT pricing information"""
        elements = []
        
        elements.append(Paragraph("ITEMS FOR DELIVERY", self.styles['SectionHeader']))
        
        # Table headers (NO PRICING COLUMNS)
        items_data = [['S.No.', 'Item Name', 'Description', 'Quantity', 'Unit', 'Notes']]
        
        # Add items
        items = data.get('items', [])
        for i, item in enumerate(items, 1):
            items_data.append([
                str(i),
                item.get('item_name', 'N/A'),
                item.get('description', '')[:50] + ('...' if len(item.get('description', '')) > 50 else ''),
                str(item.get('requested_quantity', item.get('quantity', 0))),
                item.get('unit', 'pcs'),
                item.get('notes', '')[:30] + ('...' if len(item.get('notes', '')) > 30 else '')
            ])
        
        # Create table
        items_table = Table(items_data, colWidths=[15*mm, 45*mm, 50*mm, 20*mm, 15*mm, 25*mm])
        items_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # S.No. center aligned
            ('ALIGN', (3, 0), (3, -1), 'CENTER'),  # Quantity center aligned
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))
        elements.append(items_table)
        
        # Add total items count
        total_items = len(items)
        elements.append(Spacer(1, 6))
        elements.append(Paragraph(f"<b>Total Items: {total_items}</b>", self.styles['FieldValue']))
        
        return elements
    
    def _build_transport_section(self, data: Dict) -> List:
        """Build transport and handling instructions section"""
        elements = []
        
        elements.append(Paragraph("TRANSPORT & HANDLING INSTRUCTIONS", self.styles['SectionHeader']))
        
        instructions = [
            "• Handle with care - contains laboratory supplies",
            "• Keep packages upright during transport",
            "• Protect from moisture and extreme temperatures",
            "• Verify recipient identity before delivery",
            "• Obtain signature confirmation upon delivery",
            "• Contact sender immediately if any damage is observed"
        ]
        
        for instruction in instructions:
            elements.append(Paragraph(instruction, self.styles['Normal']))
        
        # Special notes if any
        notes = data.get('notes', '')
        if notes:
            elements.append(Spacer(1, 6))
            elements.append(Paragraph("SPECIAL NOTES:", self.styles['FieldLabel']))
            elements.append(Paragraph(notes, self.styles['FieldValue']))
        
        return elements
    
    def _build_signature_section(self) -> List:
        """Build signature section for courier and recipient"""
        elements = []
        
        elements.append(Paragraph("DELIVERY CONFIRMATION", self.styles['SectionHeader']))
        
        signature_data = [
            ['COURIER SIGNATURE', 'RECIPIENT SIGNATURE'],
            ['', ''],
            ['', ''],
            ['Name: ____________________', 'Name: ____________________'],
            ['Date: ____________________', 'Date: ____________________'],
            ['Time: ____________________', 'Time: ____________________']
        ]
        
        signature_table = Table(signature_data, colWidths=[85*mm, 85*mm], rowHeights=[None, 20*mm, 20*mm, None, None, None])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))
        elements.append(signature_table)
        
        return elements
    
    def _build_footer(self, data: Dict) -> List:
        """Build PDF footer with QR code and barcode"""
        elements = []
        
        elements.append(Spacer(1, 12))
        
        # Generate QR code for delivery note
        qr_data = f"DN:{data.get('delivery_number', 'N/A')}"
        qr_code = self._generate_qr_code(qr_data)
        
        footer_data = [
            ['Generated on:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Document ID:', data.get('delivery_number', 'N/A')],
            ['System:', 'AVINI Labs Delivery Management']
        ]
        
        footer_table = Table(footer_data, colWidths=[40*mm, 130*mm])
        footer_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.grey),
        ]))
        elements.append(footer_table)
        
        return elements
    
    def _generate_qr_code(self, data: str) -> str:
        """Generate QR code for delivery note verification"""
        try:
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(data)
            qr.make(fit=True)
            
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64 for embedding
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            buffer.close()
            
            return qr_base64
            
        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            return ""
