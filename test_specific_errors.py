#!/usr/bin/env python3
"""
Test script to investigate specific procurement API errors
"""
import requests
import json
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def get_auth_token():
    """Get authentication token"""
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post('http://localhost:5002/api/auth/login', json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            return data.get('token')
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_delivery_confirmation_500_error():
    """Test the delivery confirmation endpoint that's returning 500 error"""
    print("🔍 Testing Delivery Confirmation (500 Error Investigation)")
    print("-" * 60)
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test delivery note ID 6 specifically
    delivery_id = 6
    url = f'http://localhost:5002/api/procurement/delivery-notes/{delivery_id}/confirm'
    
    confirm_data = {
        'signature': 'Test confirmation signature',
        'delivery_notes': 'Confirmed via API test - investigating 500 error'
    }
    
    try:
        print(f"📄 Testing URL: {url}")
        print(f"📄 Request data: {json.dumps(confirm_data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=confirm_data, timeout=15)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 500:
            print("❌ 500 INTERNAL SERVER ERROR confirmed")
            print("📄 Response Content:")
            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2))
            except:
                print(response.text[:500])
        elif response.status_code == 200:
            print("✅ Request successful!")
            print("📄 Response:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            print("📄 Response:")
            print(response.text[:500])
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_delivery_note_create_route():
    """Test the missing GET route for delivery note creation"""
    print("\n🔍 Testing Delivery Note Create Route (404 Investigation)")
    print("-" * 60)
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test the GET route that's returning 404
    url = 'http://localhost:5002/api/procurement/delivery-notes/create'
    
    try:
        print(f"📄 Testing URL: {url}")
        
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 404:
            print("❌ 404 NOT FOUND confirmed - Route does not exist")
            print("📄 Response Content:")
            print(response.text[:300])
        elif response.status_code == 200:
            print("✅ Route exists and accessible!")
            print("📄 Response:")
            try:
                print(json.dumps(response.json(), indent=2))
            except:
                print(response.text[:300])
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            print("📄 Response:")
            print(response.text[:300])
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def check_delivery_note_6_status():
    """Check the current status of delivery note ID 6"""
    print("\n🔍 Checking Delivery Note ID 6 Status")
    print("-" * 60)
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    url = 'http://localhost:5002/api/procurement/delivery-notes/6'
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Delivery note found!")
            print(f"📄 ID: {data.get('id')}")
            print(f"📄 Number: {data.get('delivery_number')}")
            print(f"📄 Status: {data.get('status')}")
            print(f"📄 From Tenant: {data.get('from_tenant_id')} -> To Tenant: {data.get('to_tenant_id')}")
            print(f"📄 Items: {len(data.get('items', []))}")
            
            # Check if it's in a confirmable state
            status = data.get('status')
            if status in ['dispatched', 'in_transit']:
                print(f"✅ Status '{status}' is confirmable")
            else:
                print(f"⚠️  Status '{status}' may not be confirmable")
            
            return True
        else:
            print(f"❌ Failed to get delivery note: {response.status_code}")
            print(response.text[:300])
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def check_available_routes():
    """Check what procurement routes are actually available"""
    print("\n🔍 Checking Available Procurement Routes")
    print("-" * 60)
    
    try:
        # Import Flask app to check registered routes
        from app import app
        
        procurement_routes = []
        for rule in app.url_map.iter_rules():
            if 'procurement' in rule.rule and 'delivery-notes' in rule.rule:
                methods = [m for m in rule.methods if m not in ['HEAD', 'OPTIONS']]
                procurement_routes.append({
                    'rule': rule.rule,
                    'methods': methods,
                    'endpoint': rule.endpoint
                })
        
        print("📄 Available delivery-notes routes:")
        for route in procurement_routes:
            print(f"  {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
        
        # Check specifically for create route
        create_routes = [r for r in procurement_routes if 'create' in r['rule']]
        if create_routes:
            print(f"\n📄 Create routes found: {len(create_routes)}")
            for route in create_routes:
                print(f"  {route['rule']} [{', '.join(route['methods'])}]")
        else:
            print("\n❌ No 'create' routes found for delivery-notes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking routes: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Investigating Specific Procurement API Errors")
    print("=" * 70)
    
    # Test 1: Check delivery note 6 status first
    dn6_status = check_delivery_note_6_status()
    
    # Test 2: Check available routes
    routes_check = check_available_routes()
    
    # Test 3: Test the 500 error on confirmation
    confirm_test = test_delivery_confirmation_500_error()
    
    # Test 4: Test the 404 error on create route
    create_test = test_delivery_note_create_route()
    
    print("\n" + "=" * 70)
    print("📊 Investigation Results Summary:")
    print(f"  Delivery Note 6 Status Check: {'✅ PASS' if dn6_status else '❌ FAIL'}")
    print(f"  Available Routes Check: {'✅ PASS' if routes_check else '❌ FAIL'}")
    print(f"  Delivery Confirmation Test: {'✅ PASS' if confirm_test else '❌ FAIL'}")
    print(f"  Create Route Test: {'✅ PASS' if create_test else '❌ FAIL'}")
    
    print("\n💡 Next Steps:")
    if not confirm_test:
        print("  1. Check server logs for 500 error details")
        print("  2. Verify delivery_service.py is working correctly")
        print("  3. Check database constraints and data integrity")
    
    if not create_test:
        print("  4. Implement missing GET /api/procurement/delivery-notes/create route")
        print("  5. Or update frontend to use correct endpoint")
    
    return dn6_status and routes_check

if __name__ == "__main__":
    main()
