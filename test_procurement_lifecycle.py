#!/usr/bin/env python3
"""
Comprehensive End-to-End Testing Script for Procurement Lifecycle
"""

import requests
import json

def test_procurement_lifecycle():
    base_url = 'http://localhost:5002'
    
    print('=== COMPREHENSIVE PROCUREMENT LIFECYCLE TESTING ===')
    
    # Test scenarios for different user types
    test_scenarios = [
        {
            'username': 'mayiladhuthurai', 
            'password': 'super123', 
            'role': 'Hub Admin',
            'description': 'Central hub administrator with full system access'
        },
        {
            'username': 'sirkazhi', 
            'password': 'sirkazhi123', 
            'role': 'Franchise Admin',
            'description': 'Franchise administrator with tenant-restricted access'
        }
    ]
    
    for scenario in test_scenarios:
        print(f'\n{"="*60}')
        print(f'TESTING SCENARIO: {scenario["role"]} ({scenario["username"]})')
        print(f'Description: {scenario["description"]}')
        print(f'{"="*60}')
        
        # Login
        login_response = requests.post(f'{base_url}/api/auth/login', json={
            'username': scenario['username'],
            'password': scenario['password']
        })
        
        if login_response.status_code == 200:
            token = login_response.json()['token']
            headers = {'Authorization': f'Bearer {token}'}
            
            print('✓ Authentication successful')
            
            # 1. Test Storeroom Access
            print('\n1. STOREROOM ACCESS TESTING')
            storerooms_response = requests.get(f'{base_url}/api/storerooms', headers=headers)
            if storerooms_response.status_code == 200:
                storerooms = storerooms_response.json().get('data', [])
                print(f'✓ Access to {len(storerooms)} storerooms:')
                for sr in storerooms:
                    tenant_names = {1: 'Mayiladuthurai Hub', 2: 'Sirkazhi', 3: 'Thanjavur'}
                    tenant_name = tenant_names.get(sr.get('tenant_id'), f'Tenant {sr.get("tenant_id")}')
                    print(f'  - {sr["name"]} ({tenant_name})')
                    auto_reorder = "Enabled" if sr.get("auto_reorder_enabled") else "Disabled"
                    print(f'    Auto-reorder: {auto_reorder}')
                    print(f'    Min threshold: {sr.get("min_quantity_threshold", "N/A")}')
            else:
                print(f'✗ Storeroom access failed: {storerooms_response.status_code}')
            
            # 2. Test Procurement Dashboard Access
            print('\n2. PROCUREMENT DASHBOARD ACCESS')
            dashboard_response = requests.get(f'{base_url}/api/procurement/dashboard', headers=headers)
            if dashboard_response.status_code == 200:
                dashboard_data = dashboard_response.json().get('data', {})
                print('✓ Procurement dashboard accessible')
                print(f'  - Total purchase requests: {dashboard_data.get("total_purchase_requests", 0)}')
                print(f'  - Pending requests: {dashboard_data.get("pending_purchase_requests", 0)}')
                print(f'  - Approved requests: {dashboard_data.get("approved_purchase_requests", 0)}')
            elif dashboard_response.status_code == 403:
                print('⚠️  Procurement dashboard access restricted (expected for franchise admin)')
            else:
                print(f'✗ Dashboard access failed: {dashboard_response.status_code}')
            
            # 3. Test Purchase Requests Access
            print('\n3. PURCHASE REQUESTS ACCESS')
            pr_response = requests.get(f'{base_url}/api/procurement/purchase-requests', headers=headers)
            if pr_response.status_code == 200:
                pr_data = pr_response.json().get('data', [])
                print(f'✓ Access to {len(pr_data)} purchase requests')
                
                # Show recent automated requests
                automated_requests = [pr for pr in pr_data if pr.get('request_number', '').startswith('AUTO-')]
                if automated_requests:
                    print(f'  - Found {len(automated_requests)} automated purchase requests:')
                    for pr in automated_requests[:3]:  # Show first 3
                        amount = pr.get("total_estimated_amount", 0)
                        print(f'    * {pr.get("request_number")}: {pr.get("status")} (${amount})')
                else:
                    print('  - No automated purchase requests found')
            else:
                print(f'✗ Purchase requests access failed: {pr_response.status_code}')
            
            # 4. Test Automated Procurement (Hub Admin only)
            if scenario['role'] == 'Hub Admin':
                print('\n4. AUTOMATED PROCUREMENT TESTING')
                
                # Trigger automated check
                trigger_response = requests.post(f'{base_url}/api/procurement/automated/check-inventory', headers=headers)
                if trigger_response.status_code == 200:
                    print('✓ Automated procurement check triggered successfully')
                    
                    # Check for new automated requests
                    pr_response_after = requests.get(f'{base_url}/api/procurement/purchase-requests', headers=headers)
                    if pr_response_after.status_code == 200:
                        pr_data_after = pr_response_after.json().get('data', [])
                        new_automated = [pr for pr in pr_data_after if pr.get('request_number', '').startswith('AUTO-')]
                        print(f'  - Total automated requests after trigger: {len(new_automated)}')
                        
                        # Show latest automated request details
                        if new_automated:
                            latest = new_automated[0]
                            print(f'  - Latest automated request: {latest.get("request_number")}')
                            print(f'    Status: {latest.get("status")}')
                            print(f'    Amount: ${latest.get("total_estimated_amount", 0)}')
                            print(f'    Notes: {latest.get("notes", "N/A")}')
                else:
                    print(f'✗ Automated procurement trigger failed: {trigger_response.status_code}')
            
            print(f'\n{"="*60}')
            print(f'SCENARIO COMPLETE: {scenario["role"]}')
            print(f'{"="*60}')
            
        else:
            print(f'✗ Authentication failed: {login_response.status_code}')
    
    print('\n=== PROCUREMENT LIFECYCLE TESTING COMPLETE ===')

if __name__ == "__main__":
    test_procurement_lifecycle()
