import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Button, 
  Row, 
  Col, 
  Alert, 
  Table, 
  Modal, 
  Badge,
  Spinner,
  ButtonGroup
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faWarehouse,
  faPlus,
  faEdit,
  faTrash,
  faSave,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faBoxes,
  faMapMarkerAlt
} from '@fortawesome/free-solid-svg-icons';
import storeroomAPI from '../../services/storeroomAPI';
import { tenantAPI } from '../../services/api';
import {
  TextInput,
  SelectInput,
  NumberInput,
  SuccessModal,
  ErrorModal
} from '../common';

const StoreroomManagement = () => {
  const [storerooms, setStorerooms] = useState([]);
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [editingStoreroom, setEditingStoreroom] = useState(null);
  
  const [formData, setFormData] = useState({
    storeroom_id: '',
    name: '',
    description: '',
    tenant_id: '',
    location_details: '',
    capacity: '',
    status: 'active',
    manager_name: '',
    manager_contact: '',
    // Enhanced inventory management fields
    min_quantity_threshold: '',
    max_quantity_limit: '',
    safety_stock_level: '',
    reorder_quantity: '',
    auto_reorder_enabled: false,
    reorder_point_days: '7'
  });

  useEffect(() => {
    loadStorerooms();
    loadTenants();
  }, []);

  const loadStorerooms = async () => {
    try {
      setLoading(true);
      const response = await storeroomAPI.getStorerooms({ active_only: false });
      if (response.data.success) {
        setStorerooms(response.data.data);
      } else {
        setErrorMessage('Failed to load storerooms: ' + response.data.error);
        setShowErrorModal(true);
      }
    } catch (error) {
      setErrorMessage('Failed to load storerooms: ' + error.message);
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const loadTenants = async () => {
    try {
      const response = await tenantAPI.getAccessibleTenants();
      setTenants(response.data || []);
    } catch (error) {
      console.error('Failed to load tenants:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      storeroom_id: '',
      name: '',
      description: '',
      tenant_id: '',
      location_details: '',
      capacity: '',
      status: 'active',
      manager_name: '',
      manager_contact: '',
      // Enhanced inventory management fields
      min_quantity_threshold: '',
      max_quantity_limit: '',
      safety_stock_level: '',
      reorder_quantity: '',
      auto_reorder_enabled: false,
      reorder_point_days: '7'
    });
  };

  const openAddModal = () => {
    resetForm();
    setShowAddModal(true);
  };

  const openEditModal = (storeroom) => {
    setEditingStoreroom(storeroom);
    setFormData({
      storeroom_id: storeroom.storeroom_id || '',
      name: storeroom.name || '',
      description: storeroom.description || '',
      tenant_id: storeroom.tenant_id || '',
      location_details: storeroom.location_details || '',
      capacity: storeroom.capacity || '',
      status: storeroom.status || 'active',
      manager_name: storeroom.manager_name || '',
      manager_contact: storeroom.manager_contact || '',
      // Enhanced inventory management fields
      min_quantity_threshold: storeroom.min_quantity_threshold || '',
      max_quantity_limit: storeroom.max_quantity_limit || '',
      safety_stock_level: storeroom.safety_stock_level || '',
      reorder_quantity: storeroom.reorder_quantity || '',
      auto_reorder_enabled: !!storeroom.auto_reorder_enabled,
      reorder_point_days: storeroom.reorder_point_days || '7'
    });
    setShowEditModal(true);
  };

  const handleAddStoreroom = async () => {
    if (!formData.storeroom_id || !formData.name || !formData.tenant_id) {
      setErrorMessage('Please fill in all required fields');
      setShowErrorModal(true);
      return;
    }
    const payload = {
  ...formData,
  auto_reorder_enabled: formData.auto_reorder_enabled ? 1 : 0,
};

    setLoading(true);
    try {
      const response = await storeroomAPI.createStoreroom(payload);
      if (response.data.success) {
        setSuccessMessage(`Storeroom "${formData.name}" created successfully`);
        setShowSuccessModal(true);
        setShowAddModal(false);
        resetForm();
        await loadStorerooms();
      } else {
        setErrorMessage('Failed to create storeroom: ' + response.data.error);
        setShowErrorModal(true);
      }
    } catch (error) {
      setErrorMessage('Failed to create storeroom: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStoreroom = async () => {
    if (!formData.storeroom_id || !formData.name || !formData.tenant_id) {
      setErrorMessage('Please fill in all required fields');
      setShowErrorModal(true);
      return;
    }

     const payload = {
  ...formData,
  auto_reorder_enabled: formData.auto_reorder_enabled ? 1 : 0,
};
    setLoading(true);
    try {
      const response = await storeroomAPI.updateStoreroom(editingStoreroom.id, payload);
      if (response.data.success) {
        setSuccessMessage(`Storeroom "${formData.name}" updated successfully`);
        setShowSuccessModal(true);
        setShowEditModal(false);
        setEditingStoreroom(null);
        resetForm();
        await loadStorerooms();
      } else {
        setErrorMessage('Failed to update storeroom: ' + response.data.error);
        setShowErrorModal(true);
      }
    } catch (error) {
      setErrorMessage('Failed to update storeroom: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStoreroom = async (storeroom) => {
    if (!window.confirm(`Are you sure you want to delete storeroom "${storeroom.name}"?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await storeroomAPI.deleteStoreroom(storeroom.id);
      if (response.data.success) {
        setSuccessMessage(`Storeroom "${storeroom.name}" deleted successfully`);
        setShowSuccessModal(true);
        await loadStorerooms();
      } else {
        setErrorMessage('Failed to delete storeroom: ' + response.data.error);
        setShowErrorModal(true);
      }
    } catch (error) {
      setErrorMessage('Failed to delete storeroom: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    return status === 'active' 
      ? <Badge bg="success">Active</Badge>
      : <Badge bg="secondary">Inactive</Badge>;
  };

  const getTenantOptions = () => {
    return tenants.map(tenant => ({
      value: tenant.id,
      label: `${tenant.name} (${tenant.site_code})`
    }));
  };

  const getStatusOptions = () => [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' }
  ];

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="mb-1">
            <FontAwesomeIcon icon={faWarehouse} className="me-2" />
            Storeroom Management
          </h5>
          <p className="text-muted mb-0">Manage storerooms across all franchises</p>
        </div>
        <Button 
          variant="primary" 
          size="sm"
          onClick={openAddModal}
          disabled={loading}
        >
          <FontAwesomeIcon icon={faPlus} className="me-2" />
          Add Storeroom
        </Button>
      </div>

      {/* Storerooms Table */}
      <Card>
        <Card.Header className='text-primary'>
          <h6 className="mb-0">
            <FontAwesomeIcon icon={faWarehouse} className="me-2" />
            Storerooms ({storerooms.length})
          </h6>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center p-4">
              <Spinner animation="border" size="sm" className="me-2" />
              Loading storerooms...
            </div>
          ) : storerooms.length === 0 ? (
            <div className="text-center p-4 text-muted">
              <FontAwesomeIcon icon={faInfoCircle} size="2x" className="mb-2" />
              <p>No storerooms found</p>
            </div>
          ) : (
            <Table responsive striped hover className="mb-0">
              <thead>
                <tr>
                  <th>Storeroom ID</th>
                  <th>Name</th>
                  <th>Franchise</th>
                  <th>Location</th>
                  <th>Capacity</th>
                  <th>Min Threshold</th>
                  <th>Auto Reorder</th>
                  <th>Manager</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {storerooms.map((storeroom) => (
                  <tr key={storeroom.id}>
                    <td>
                      <strong>{storeroom.storeroom_id}</strong>
                    </td>
                    <td>
                      <div>
                        <strong>{storeroom.name}</strong>
                        {storeroom.description && (
                          <div className="text-muted small">{storeroom.description}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>{storeroom.tenant_name}</strong>
                        <div className="text-muted small">({storeroom.site_code})</div>
                      </div>
                    </td>
                    <td>
                      {storeroom.location_details ? (
                        <span>
                          <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1 text-muted" />
                          {storeroom.location_details}
                        </span>
                      ) : (
                        <span className="text-muted">-</span>
                      )}
                    </td>
                    <td>
                      {storeroom.capacity ? `${storeroom.capacity} sq ft` : '-'}
                    </td>
                    <td>
                      {storeroom.min_quantity_threshold ? (
                        <span className="text-warning">
                          <FontAwesomeIcon icon={faExclamationTriangle} className="me-1" />
                          {storeroom.min_quantity_threshold}
                        </span>
                      ) : (
                        <span className="text-muted">-</span>
                      )}
                    </td>
                    <td>
                      {storeroom.auto_reorder_enabled ? (
                        <Badge bg="success">
                          <FontAwesomeIcon icon={faCheckCircle} className="me-1" />
                          Enabled
                        </Badge>
                      ) : (
                        <Badge bg="secondary">Disabled</Badge>
                      )}
                    </td>
                    <td>
                      {storeroom.manager_name ? (
                        <div>
                          <div>{storeroom.manager_name}</div>
                          {storeroom.manager_contact && (
                            <div className="text-muted small">{storeroom.manager_contact}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-muted">-</span>
                      )}
                    </td>
                    <td>{getStatusBadge(storeroom.status)}</td>
                    <td>
                      <ButtonGroup size="sm">
                        <Button
                          variant="outline-primary"
                          onClick={() => openEditModal(storeroom)}
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          onClick={() => handleDeleteStoreroom(storeroom)}
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </ButtonGroup>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Add Storeroom Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Storeroom</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <TextInput
                  name="storeroom_id"
                  label="Storeroom ID"
                  value={formData.storeroom_id}
                  onChange={(e) => setFormData(prev => ({...prev, storeroom_id: e.target.value}))}
                  placeholder="e.g., SR001, STORE-MLT-01"
                  required
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="name"
                  label="Storeroom Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                  placeholder="e.g., Main Storage, Lab Supplies"
                  required
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <SelectInput
                  name="tenant_id"
                  label="Franchise"
                  value={formData.tenant_id}
                  onChange={(e) => setFormData(prev => ({...prev, tenant_id: e.target.value}))}
                  options={getTenantOptions()}
                  placeholder="Select franchise..."
                  required
                />
              </Col>
              <Col md={6}>
                <SelectInput
                  name="status"
                  label="Status"
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({...prev, status: e.target.value}))}
                  options={getStatusOptions()}
                />
              </Col>
            </Row>
            <TextInput
              name="description"
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
              placeholder="Brief description of the storeroom"
            />
            <TextInput
              name="location_details"
              label="Location Details"
              value={formData.location_details}
              onChange={(e) => setFormData(prev => ({...prev, location_details: e.target.value}))}
              placeholder="e.g., Ground Floor, Building A, Room 101"
            />
            <Row>
              <Col md={6}>
                <NumberInput
                  name="capacity"
                  label="Capacity (sq ft)"
                  value={formData.capacity}
                  onChange={(e) => setFormData(prev => ({...prev, capacity: e.target.value}))}
                  placeholder="Storage capacity in square feet"
                  min={0}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <TextInput
                  name="manager_name"
                  label="Manager Name"
                  value={formData.manager_name}
                  onChange={(e) => setFormData(prev => ({...prev, manager_name: e.target.value}))}
                  placeholder="Responsible person name"
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="manager_contact"
                  label="Manager Contact"
                  value={formData.manager_contact}
                  onChange={(e) => setFormData(prev => ({...prev, manager_contact: e.target.value}))}
                  placeholder="Phone number or email"
                />
              </Col>
            </Row>

            {/* Enhanced Inventory Management Section */}
            <hr className="my-4" />
            <h6 className="mb-3 text-primary">
              <FontAwesomeIcon icon={faBoxes} className="me-2" />
              Inventory Management Settings
            </h6>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="min_quantity_threshold"
                  label="Minimum Quantity Threshold"
                  value={formData.min_quantity_threshold}
                  onChange={(e) => setFormData(prev => ({...prev, min_quantity_threshold: e.target.value}))}
                  placeholder="Minimum stock level"
                  min={0}
                  step="0.01"
                />
              </Col>
              <Col md={6}>
                <NumberInput
                  name="max_quantity_limit"
                  label="Maximum Quantity Limit"
                  value={formData.max_quantity_limit}
                  onChange={(e) => setFormData(prev => ({...prev, max_quantity_limit: e.target.value}))}
                  placeholder="Maximum stock capacity"
                  min={0}
                  step="0.01"
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="safety_stock_level"
                  label="Safety Stock Level"
                  value={formData.safety_stock_level}
                  onChange={(e) => setFormData(prev => ({...prev, safety_stock_level: e.target.value}))}
                  placeholder="Safety buffer stock"
                  min={0}
                  step="0.01"
                />
              </Col>
              <Col md={6}>
                <NumberInput
                  name="reorder_quantity"
                  label="Reorder Quantity"
                  value={formData.reorder_quantity}
                  onChange={(e) => setFormData(prev => ({...prev, reorder_quantity: e.target.value}))}
                  placeholder="Quantity to reorder"
                  min={0}
                  step="0.01"
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="reorder_point_days"
                  label="Reorder Point (Days)"
                  value={formData.reorder_point_days}
                  onChange={(e) => setFormData(prev => ({...prev, reorder_point_days: e.target.value}))}
                  placeholder="Days before reorder"
                  min={1}
                />
              </Col>
              <Col md={6}>
                <div className="form-group">
                  <label className="form-label">Auto Reorder</label>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="auto_reorder_enabled"
                      checked={formData.auto_reorder_enabled}
                      onChange={(e) => setFormData(prev => ({...prev, auto_reorder_enabled: e.target.checked}))}
                    />
                    <label className="form-check-label" htmlFor="auto_reorder_enabled">
                      Enable automatic reorder when minimum threshold is reached
                    </label>
                  </div>
                </div>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddStoreroom}
            disabled={loading}
          >
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Creating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Create Storeroom
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Storeroom Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Storeroom</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <TextInput
                  name="storeroom_id"
                  label="Storeroom ID"
                  value={formData.storeroom_id}
                  onChange={(e) => setFormData(prev => ({...prev, storeroom_id: e.target.value}))}
                  placeholder="e.g., SR001, STORE-MLT-01"
                  required
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="name"
                  label="Storeroom Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                  placeholder="e.g., Main Storage, Lab Supplies"
                  required
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <SelectInput
                  name="tenant_id"
                  label="Franchise"
                  value={formData.tenant_id}
                  onChange={(e) => setFormData(prev => ({...prev, tenant_id: e.target.value}))}
                  options={getTenantOptions()}
                  placeholder="Select franchise..."
                  required
                />
              </Col>
              <Col md={6}>
                <SelectInput
                  name="status"
                  label="Status"
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({...prev, status: e.target.value}))}
                  options={getStatusOptions()}
                />
              </Col>
            </Row>
            <TextInput
              name="description"
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
              placeholder="Brief description of the storeroom"
            />
            <TextInput
              name="location_details"
              label="Location Details"
              value={formData.location_details}
              onChange={(e) => setFormData(prev => ({...prev, location_details: e.target.value}))}
              placeholder="e.g., Ground Floor, Building A, Room 101"
            />
            <Row>
              <Col md={6}>
                <NumberInput
                  name="capacity"
                  label="Capacity (sq ft)"
                  value={formData.capacity}
                  onChange={(e) => setFormData(prev => ({...prev, capacity: e.target.value}))}
                  placeholder="Storage capacity in square feet"
                  min={0}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <TextInput
                  name="manager_name"
                  label="Manager Name"
                  value={formData.manager_name}
                  onChange={(e) => setFormData(prev => ({...prev, manager_name: e.target.value}))}
                  placeholder="Responsible person name"
                />
              </Col>
              <Col md={6}>
                <TextInput
                  name="manager_contact"
                  label="Manager Contact"
                  value={formData.manager_contact}
                  onChange={(e) => setFormData(prev => ({...prev, manager_contact: e.target.value}))}
                  placeholder="Phone number or email"
                />
              </Col>
            </Row>

            {/* Enhanced Inventory Management Section */}
            <hr className="my-4" />
            <h6 className="mb-3 text-primary">
              <FontAwesomeIcon icon={faBoxes} className="me-2" />
              Inventory Management Settings
            </h6>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="min_quantity_threshold"
                  label="Minimum Quantity Threshold"
                  value={formData.min_quantity_threshold}
                  onChange={(e) => setFormData(prev => ({...prev, min_quantity_threshold: e.target.value}))}
                  placeholder="Minimum stock level"
                  min={0}
                  step="0.01"
                />
              </Col>
              <Col md={6}>
                <NumberInput
                  name="max_quantity_limit"
                  label="Maximum Quantity Limit"
                  value={formData.max_quantity_limit}
                  onChange={(e) => setFormData(prev => ({...prev, max_quantity_limit: e.target.value}))}
                  placeholder="Maximum stock capacity"
                  min={0}
                  step="0.01"
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="safety_stock_level"
                  label="Safety Stock Level"
                  value={formData.safety_stock_level}
                  onChange={(e) => setFormData(prev => ({...prev, safety_stock_level: e.target.value}))}
                  placeholder="Safety buffer stock"
                  min={0}
                  step="0.01"
                />
              </Col>
              <Col md={6}>
                <NumberInput
                  name="reorder_quantity"
                  label="Reorder Quantity"
                  value={formData.reorder_quantity}
                  onChange={(e) => setFormData(prev => ({...prev, reorder_quantity: e.target.value}))}
                  placeholder="Quantity to reorder"
                  min={0}
                  step="0.01"
                />
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <NumberInput
                  name="reorder_point_days"
                  label="Reorder Point (Days)"
                  value={formData.reorder_point_days}
                  onChange={(e) => setFormData(prev => ({...prev, reorder_point_days: e.target.value}))}
                  placeholder="Days before reorder"
                  min={1}
                />
              </Col>
              <Col md={6}>
                <div className="form-group">
                  <label className="form-label">Auto Reorder</label>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="auto_reorder_enabled_edit"
                      checked={formData.auto_reorder_enabled}
                      onChange={(e) => setFormData(prev => ({...prev, auto_reorder_enabled: e.target.checked}))}
                    />
                    <label className="form-check-label" htmlFor="auto_reorder_enabled_edit">
                      Enable automatic reorder when minimum threshold is reached
                    </label>
                  </div>
                </div>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleUpdateStoreroom}
            disabled={loading}
          >
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Updating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Update Storeroom
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
      />
    </div>
  );
};

export default StoreroomManagement;
