[{"id": 1, "invoice_number": "INV-********-0001", "routing_id": 16, "sample_id": 58, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 19, "created_at": "2025-06-15T09:55:34.434069", "updated_at": "2025-06-15T09:59:51.547068", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "sent", "subtotal": 78.99, "tax_rate": 0.18, "tax_amount": 14.22, "total_amount": 93.21, "currency": "INR", "notes": "", "line_items": [{"description": "dfdf", "quantity": 1, "unit_price": "33.99", "total": 33.99}, {"description": "g", "quantity": 1, "unit_price": "45", "total": 45.0}]}, {"id": 2, "invoice_number": "INV-********-0002", "routing_id": 13, "sample_id": 74, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 2, "created_at": "2025-06-15T09:30:00", "updated_at": "2025-06-15T09:30:00", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "sent", "subtotal": 1500.0, "tax_rate": 0.18, "tax_amount": 270.0, "total_amount": 1770.0, "currency": "INR", "notes": "Sample processing and analysis charges", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 800.0, "total": 800.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Report Generation", "quantity": 1, "unit_price": 200.0, "total": 200.0}]}, {"id": 3, "invoice_number": "INV-********-0003", "routing_id": 14, "sample_id": 63, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 2, "created_at": "2025-06-15T10:00:00", "updated_at": "2025-06-15T10:15:00", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "paid", "subtotal": 2000.0, "tax_rate": 0.18, "tax_amount": 360.0, "total_amount": 2360.0, "currency": "INR", "notes": "Comprehensive testing package - Payment received", "line_items": [{"description": "Advanced Testing Package", "quantity": 1, "unit_price": 1200.0, "total": 1200.0}, {"description": "Express Processing", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Digital Report Delivery", "quantity": 1, "unit_price": 300.0, "total": 300.0}]}, {"id": 4, "invoice_number": "INV-********-0004", "routing_id": 17, "sample_id": 45, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 19, "created_at": "2025-06-15T10:16:10.667631", "updated_at": "2025-06-15T11:32:14.946805", "invoice_date": "2025-06-15", "due_date": "2025-07-15", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-06-15", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-06-15T11:32:14.946805", "ownership_transferred_by": 2}, {"id": 5, "invoice_number": "INV-********-0001", "routing_id": 18, "sample_id": 52, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-06-25T17:19:27.684017", "updated_at": "2025-06-25T17:21:28.353177", "invoice_date": "2025-06-25", "due_date": "2025-07-25", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-06-25", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-06-25T17:21:28.353177", "ownership_transferred_by": 5}, {"id": 6, "invoice_number": "INV-********-0001", "routing_id": 19, "sample_id": 19, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-07-21T14:22:30.880634", "updated_at": "2025-07-21T14:26:39.218151", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-07-21", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-07-21T14:26:39.218120", "ownership_transferred_by": 5}, {"id": 7, "invoice_number": "INV-********-0002", "routing_id": 20, "sample_id": 46, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-07-21T17:30:29.270596", "updated_at": "2025-07-21T17:30:29.270601", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": false, "original_owner": 1}, {"id": 8, "invoice_number": "INV-********-0003", "routing_id": 21, "sample_id": 66, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-07-21T17:32:09.271172", "updated_at": "2025-07-21T17:32:09.271183", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": false, "original_owner": 1}, {"id": 9, "invoice_number": "INV-********-0004", "routing_id": 22, "sample_id": 75, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-07-21T18:15:52.541058", "updated_at": "2025-07-21T18:22:37.345975", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "status": "paid", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-07-21", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-07-21T18:22:37.345968", "ownership_transferred_by": 5}, {"id": 10, "invoice_number": "INV-********-0001", "routing_id": 23, "sample_id": 64, "from_tenant_id": 1, "to_tenant_id": 3, "created_by": 4, "created_at": "2025-08-04T18:29:05.372454", "updated_at": "2025-08-04T18:29:05.372475", "invoice_date": "2025-08-04", "due_date": "2025-09-03", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": false, "original_owner": 1}, {"id": 11, "invoice_number": "INV-********-0002", "routing_id": 24, "sample_id": 42, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-08-04T18:29:59.081880", "updated_at": "2025-08-04T18:31:02.417863", "invoice_date": "2025-08-04", "due_date": "2025-09-03", "status": "draft", "subtotal": 800.0, "tax_rate": 0.18, "tax_amount": 144.0, "total_amount": 944.0, "currency": "INR", "notes": "Automatically generated invoice for sample routing\n\nOwnership transferred to destination facility on 2025-08-04", "line_items": [{"description": "Sample Processing Fee", "quantity": 1, "unit_price": 500.0, "total": 500.0}, {"description": "Laboratory Analysis", "quantity": 1, "unit_price": 300.0, "total": 300.0}], "ownership_transferred": true, "original_owner": 1, "ownership_transferred_at": "2025-08-04T18:31:02.417846", "ownership_transferred_by": 5}, {"id": 12, "invoice_number": "INV-********-0001", "routing_id": 25, "sample_id": 64, "from_tenant_id": 1, "to_tenant_id": 2, "created_by": 4, "created_at": "2025-09-04T16:01:39.211325", "updated_at": "2025-09-04T16:01:39.211336", "invoice_date": "2025-09-04", "due_date": "2025-10-04", "status": "draft", "subtotal": 4.0, "tax_rate": 0.18, "tax_amount": 0.72, "total_amount": 4.72, "currency": "INR", "notes": "test", "line_items": [{"description": "test", "quantity": 1, "unit_price": "2", "total": 2.0}, {"description": "test2", "quantity": 1, "unit_price": "2", "total": 2.0}]}]