import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Tabs,
  Tab,
  <PERSON>,
  <PERSON><PERSON>,
  Badge,
  Al<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Modal,
  Form,
  InputGroup
} from 'react-bootstrap';
import {
  FaPlus,
  FaEye,
  FaEdit,
  FaTrash,
  FaFileInvoiceDollar,
  FaMoneyBillWave,
  FaUsers,
  FaChartBar
} from 'react-icons/fa';
import { accountingService } from '../../services/accountingService';

const AccountsPayable = () => {
  const [activeTab, setActiveTab] = useState('vendors');
  const [vendors, setVendors] = useState([]);
  const [purchaseInvoices, setPurchaseInvoices] = useState([]);
  const [agedPayables, setAgedPayables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showVendorModal, setShowVendorModal] = useState(false);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [vendorForm, setVendorForm] = useState({
    vendor_name: '',
    vendor_code: '',
    contact_person: '',
    email: '',
    phone: '',
    address_line1: '',
    payment_terms: 'NET_30',
    tax_id: '',
    is_active: true
  });
  const [invoiceForm, setInvoiceForm] = useState({
    vendor_id: '',
    invoice_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    total_amount: '',
    tax_amount: '',
    description: '',
    po_number: ''
  });

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const tenantId = user.tenant_id || 1;

  useEffect(() => {
    if (activeTab === 'vendors') {
      loadVendors();
    } else if (activeTab === 'invoices') {
      loadPurchaseInvoices();
    } else if (activeTab === 'reports') {
      loadAgedPayables();
    }
  }, [activeTab]);

  const loadVendors = async () => {
    try {
      setLoading(true);
      const response = await accountingService.getVendors(tenantId);
      setVendors(response.data || []);
    } catch (err) {
      setError('Failed to load vendors');
      console.error('Error loading vendors:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPurchaseInvoices = async () => {
    try {
      setLoading(true);
      const response = await accountingService.getPurchaseInvoices(tenantId, {
        page: 1,
        per_page: 50
      });
      setPurchaseInvoices(response.data || []);
    } catch (err) {
      setError('Failed to load purchase invoices');
      console.error('Error loading purchase invoices:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAgedPayables = async () => {
    try {
      setLoading(true);
      const response = await accountingService.getAgedPayablesReport(tenantId);
      setAgedPayables(response.data || []);
    } catch (err) {
      setError('Failed to load aged payables report');
      console.error('Error loading aged payables:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddVendor = () => {
    setShowVendorModal(true);
  };

  const handleAddInvoice = () => {
    setShowInvoiceModal(true);
  };

  const handleCloseVendorModal = () => {
    setShowVendorModal(false);
    setVendorForm({
      vendor_name: '',
      vendor_code: '',
      contact_person: '',
      email: '',
      phone: '',
      address_line1: '',
      payment_terms: 'NET_30',
      tax_id: '',
      is_active: true
    });
  };

  const handleCloseInvoiceModal = () => {
    setShowInvoiceModal(false);
    setInvoiceForm({
      vendor_id: '',
      invoice_number: '',
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: '',
      total_amount: '',
      tax_amount: '',
      description: '',
      po_number: ''
    });
  };

  const handleVendorFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setVendorForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleInvoiceFormChange = (e) => {
    const { name, value } = e.target;
    setInvoiceForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateVendor = async () => {
    try {
      // Validate required fields
      if (!vendorForm.vendor_name.trim()) {
        setError('Vendor name is required');
        return;
      }

      const vendorData = {
        ...vendorForm,
        tenant_id: tenantId,
        created_by: user.id
      };

      await accountingService.createVendor(vendorData);
      setShowVendorModal(false);
      handleCloseVendorModal();
      loadVendors();
      setError(null); // Clear any previous errors
    } catch (err) {
      console.error('Error creating vendor:', err);

      // Provide more specific error messages
      if (err.response?.data?.error?.includes('UNIQUE constraint failed: vendors.vendor_code')) {
        setError('Vendor code already exists. Please use a different code.');
      } else if (err.response?.data?.error?.includes('vendor_name')) {
        setError('Vendor name is required.');
      } else if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
      } else {
        setError(err.response?.data?.error || 'Failed to create vendor. Please try again.');
      }
    }
  };

  const handleCreateInvoice = async () => {
    try {
      // Prepare data in the format expected by backend
      const invoiceData = {
        invoice_data: {
          vendor_id: parseInt(invoiceForm.vendor_id),
          invoice_number: invoiceForm.invoice_number,
          invoice_date: invoiceForm.invoice_date,
          due_date: invoiceForm.due_date || null,
          total_amount: parseFloat(invoiceForm.total_amount),
          tax_amount: parseFloat(invoiceForm.tax_amount) || 0,
          description: invoiceForm.description || '',
          po_number: invoiceForm.po_number || null,
          tenant_id: tenantId,
          created_by: user.id
        },
        line_items: [
          {
            description: invoiceForm.description || 'Purchase Invoice',
            quantity: 1,
            unit_price: parseFloat(invoiceForm.total_amount) - (parseFloat(invoiceForm.tax_amount) || 0),
            total_amount: parseFloat(invoiceForm.total_amount) - (parseFloat(invoiceForm.tax_amount) || 0)
          }
        ]
      };

      await accountingService.createPurchaseInvoice(invoiceData);
      setShowInvoiceModal(false);
      handleCloseInvoiceModal();
      loadPurchaseInvoices();
    } catch (err) {
      setError('Failed to create purchase invoice');
      console.error('Error creating invoice:', err);
    }
  };

  const handleApproveInvoice = async (invoiceId) => {
    try {
      await accountingService.approvePurchaseInvoice(invoiceId, user.id);
      loadPurchaseInvoices(); // Reload to show updated status
    } catch (err) {
      setError('Failed to approve purchase invoice');
      console.error('Error approving invoice:', err);
    }
  };

  const getPaymentTermsLabel = (terms) => {
    const labels = {
      'NET_30': 'Net 30 Days',
      'NET_15': 'Net 15 Days',
      'NET_60': 'Net 60 Days',
      'COD': 'Cash on Delivery',
      'ADVANCE': 'Advance Payment'
    };
    return labels[terms] || terms;
  };

  const renderVendorsTab = () => (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6 className="mb-0">Vendor Management</h6>
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowVendorModal(true)}
        >
          <FaPlus className="me-1" /> Add Vendor
        </Button>
      </div>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Vendor Code</th>
              <th>Vendor Name</th>
              <th>Contact Person</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Payment Terms</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {vendors.length === 0 ? (
              <tr>
                <td colSpan="8" className="text-center py-4">
                  No vendors found. Click "Add Vendor" to create your first vendor.
                </td>
              </tr>
            ) : (
              vendors.map((vendor) => (
                <tr key={vendor.id}>
                  <td><strong>{vendor.vendor_code}</strong></td>
                  <td>{vendor.vendor_name}</td>
                  <td>{vendor.contact_person}</td>
                  <td>{vendor.email}</td>
                  <td>{vendor.phone}</td>
                  <td>{getPaymentTermsLabel(vendor.payment_terms)}</td>
                  <td>
                    <Badge bg={vendor.is_active ? 'success' : 'secondary'}>
                      {vendor.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </td>
                  <td>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      className="me-1"
                      onClick={() => {
                        setSelectedVendor(vendor);
                        setShowVendorModal(true);
                      }}
                    >
                      <FaEye />
                    </Button>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      onClick={() => {
                        setSelectedVendor(vendor);
                        setVendorForm(vendor);
                        setShowVendorModal(true);
                      }}
                    >
                      <FaEdit />
                    </Button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </Table>
      )}
    </div>
  );

  const renderInvoicesTab = () => (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6 className="mb-0 text-primary">Purchase Invoices</h6>
        <Button variant="success" size="sm" onClick={handleAddInvoice}>
          <FaFileInvoiceDollar className="me-1" /> Create Invoice
        </Button>
      </div>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : purchaseInvoices.length === 0 ? (
        <Alert variant="info">
          <FaFileInvoiceDollar className="me-2" />
          <strong>Purchase Invoice Integration</strong>
          <p className="mb-0 mt-2">
            Purchase invoices are automatically created when procurement delivery notes are received.
            This ensures seamless integration between procurement and accounting systems.
          </p>
        </Alert>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Invoice #</th>
              <th>Vendor</th>
              <th>Invoice Date</th>
              <th>Due Date</th>
              <th>Total Amount</th>
              <th>Outstanding</th>
              <th>Status</th>
              <th>Reference</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {purchaseInvoices.map((invoice) => (
              <tr key={invoice.id}>
                <td><strong>{invoice.invoice_number}</strong></td>
                <td>{invoice.vendor_name || 'Unknown Vendor'}</td>
                <td>{new Date(invoice.invoice_date).toLocaleDateString()}</td>
                <td>{new Date(invoice.due_date).toLocaleDateString()}</td>
                <td>₹{invoice.total_amount?.toLocaleString()}</td>
                <td>₹{invoice.outstanding_amount?.toLocaleString()}</td>
                <td>
                  <Badge bg={
                    invoice.status === 'APPROVED' ? 'success' :
                    invoice.status === 'PAID' ? 'primary' :
                    invoice.status === 'PARTIALLY_PAID' ? 'warning' :
                    'secondary'
                  }>
                    {invoice.status}
                  </Badge>
                </td>
                <td>
                  {invoice.reference_type === 'DELIVERY_NOTE' && (
                    <Badge bg="info">
                      <FaFileInvoiceDollar className="me-1" />
                      Auto-Generated
                    </Badge>
                  )}
                  {invoice.po_number && (
                    <small className="text-muted d-block">PO: {invoice.po_number}</small>
                  )}
                </td>
                <td>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-1"
                    onClick={() => {
                      // View invoice details
                      console.log('View invoice:', invoice);
                    }}
                  >
                    <FaEye />
                  </Button>
                  {invoice.status === 'DRAFT' && (
                    <Button
                      variant="outline-success"
                      size="sm"
                      onClick={() => handleApproveInvoice(invoice.id)}
                    >
                      Approve
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  const renderReportsTab = () => (
    <div>
      <h6 className="mb-3">Aged Payables Report</h6>

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" />
        </div>
      ) : agedPayables.length === 0 ? (
        <Alert variant="info">
          <FaChartBar className="me-2" />
          No outstanding payables found.
        </Alert>
      ) : (
        <Table responsive striped hover>
          <thead>
            <tr>
              <th>Vendor</th>
              <th>Invoice #</th>
              <th>Invoice Date</th>
              <th>Due Date</th>
              <th>Total Amount</th>
              <th>Outstanding</th>
              <th>Aging Bucket</th>
              <th>Days Overdue</th>
            </tr>
          </thead>
          <tbody>
            {agedPayables.map((payable, index) => (
              <tr key={index}>
                <td>{payable.vendor_name}</td>
                <td>{payable.invoice_number}</td>
                <td>{new Date(payable.invoice_date).toLocaleDateString()}</td>
                <td>{new Date(payable.due_date).toLocaleDateString()}</td>
                <td>₹{payable.total_amount?.toLocaleString()}</td>
                <td>₹{payable.outstanding_amount?.toLocaleString()}</td>
                <td>
                  <Badge bg={
                    payable.aging_bucket === 'Current' ? 'success' :
                    payable.aging_bucket === '1-30 Days' ? 'warning' :
                    payable.aging_bucket === '31-60 Days' ? 'danger' :
                    'dark'
                  }>
                    {payable.aging_bucket}
                  </Badge>
                </td>
                <td>
                  {payable.days_overdue > 0 ? (
                    <Badge bg="danger">{Math.floor(payable.days_overdue)} days</Badge>
                  ) : (
                    <Badge bg="success">Current</Badge>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  return (
    <Card>
      <Card.Header className="text-primary">
        <h5 className="mb-0">
          <FaUsers className="me-2" />
          Accounts Payable
        </h5>
      </Card.Header>
      <Card.Body style={{background:"#f8f9fa"}}>
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
          <Tab eventKey="vendors" title={
            <span className="text-primary"><FaUsers className="me-1" />Vendors</span>
          }>
            {renderVendorsTab()}
          </Tab>

          <Tab eventKey="invoices" title={
            <span className="text-primary"><FaFileInvoiceDollar className="me-1" />Purchase Invoices</span>
          }>
            {renderInvoicesTab()}
          </Tab>

          <Tab eventKey="reports" title={
            <span className="text-primary"><FaChartBar className="me-1" />Aged Payables</span>
          }>
            {renderReportsTab()}
          </Tab>
        </Tabs>
      </Card.Body>

      {/* Vendor Modal */}
      <Modal show={showVendorModal} onHide={() => setShowVendorModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FaPlus className="me-2" />
            Add New Vendor
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Vendor Name *</Form.Label>
                  <Form.Control
                    type="text"
                    value={vendorForm.vendor_name}
                    onChange={handleVendorFormChange}
                    name="vendor_name"
                    placeholder="Enter vendor name"
                    required
                    isInvalid={!vendorForm.vendor_name.trim() && vendorForm.vendor_name !== ''}
                  />
                  <Form.Control.Feedback type="invalid">
                    Vendor name is required.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Vendor Code</Form.Label>
                  <Form.Control
                    type="text"
                    value={vendorForm.vendor_code}
                    onChange={handleVendorFormChange}
                    name="vendor_code"
                    placeholder="Auto-generated if empty"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Person</Form.Label>
                  <Form.Control
                    type="text"
                    value={vendorForm.contact_person}
                    onChange={handleVendorFormChange}
                    name="contact_person"
                    placeholder="Enter contact person name"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={vendorForm.email}
                    onChange={handleVendorFormChange}
                    name="email"
                    placeholder="Enter email address"
                    isInvalid={vendorForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(vendorForm.email)}
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter a valid email address.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="text"
                    value={vendorForm.phone}
                    onChange={handleVendorFormChange}
                    name="phone"
                    placeholder="Enter phone number"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Payment Terms</Form.Label>
                  <Form.Select
                    value={vendorForm.payment_terms}
                    onChange={handleVendorFormChange}
                    name="payment_terms"
                  >
                    <option value="NET_30">Net 30 Days</option>
                    <option value="NET_15">Net 15 Days</option>
                    <option value="NET_60">Net 60 Days</option>
                    <option value="COD">Cash on Delivery</option>
                    <option value="ADVANCE">Advance Payment</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Address</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={vendorForm.address_line1}
                onChange={handleVendorFormChange}
                name="address_line1"
                placeholder="Enter vendor address"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Tax ID / GST Number</Form.Label>
              <Form.Control
                type="text"
                value={vendorForm.tax_id}
                onChange={handleVendorFormChange}
                name="tax_id"
                placeholder="Enter tax ID or GST number"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="Active Vendor"
                checked={vendorForm.is_active}
                onChange={handleVendorFormChange}
                name="is_active"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseVendorModal}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleCreateVendor}>
            <FaPlus className="me-1" />
            Create Vendor
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Create Invoice Modal */}
      <Modal show={showInvoiceModal} onHide={handleCloseInvoiceModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Create Purchase Invoice</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Vendor *</Form.Label>
                  <Form.Select
                    name="vendor_id"
                    value={invoiceForm.vendor_id}
                    onChange={handleInvoiceFormChange}
                    required
                  >
                    <option value="">Select Vendor</option>
                    {vendors.map(vendor => (
                      <option key={vendor.id} value={vendor.id}>
                        {vendor.vendor_name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Invoice Number *</Form.Label>
                  <Form.Control
                    type="text"
                    name="invoice_number"
                    value={invoiceForm.invoice_number}
                    onChange={handleInvoiceFormChange}
                    required
                    placeholder="Enter invoice number"
                  />
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Invoice Date *</Form.Label>
                  <Form.Control
                    type="date"
                    name="invoice_date"
                    value={invoiceForm.invoice_date}
                    onChange={handleInvoiceFormChange}
                    required
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Due Date</Form.Label>
                  <Form.Control
                    type="date"
                    name="due_date"
                    value={invoiceForm.due_date}
                    onChange={handleInvoiceFormChange}
                  />
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Total Amount *</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.01"
                    name="total_amount"
                    value={invoiceForm.total_amount}
                    onChange={handleInvoiceFormChange}
                    required
                    placeholder="0.00"
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Tax Amount</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.01"
                    name="tax_amount"
                    value={invoiceForm.tax_amount}
                    onChange={handleInvoiceFormChange}
                    placeholder="0.00"
                  />
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>PO Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="po_number"
                    value={invoiceForm.po_number}
                    onChange={handleInvoiceFormChange}
                    placeholder="Purchase order number"
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    type="text"
                    name="description"
                    value={invoiceForm.description}
                    onChange={handleInvoiceFormChange}
                    placeholder="Invoice description"
                  />
                </Form.Group>
              </div>
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseInvoiceModal}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleCreateInvoice}>
            <FaFileInvoiceDollar className="me-1" />
            Create Invoice
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default AccountsPayable;
