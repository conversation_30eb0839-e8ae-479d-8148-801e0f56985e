"""
AVINI Labs Financial Reporting Service
Generates financial reports including trial balance, profit & loss, balance sheet
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional
from decimal import Decimal
import json

from database_manager import db_manager

logger = logging.getLogger(__name__)

class FinancialReportingService:
    """Service for generating financial reports"""
    
    def __init__(self):
        self.db = db_manager
    
    # ============================================================================
    # TRIAL BALANCE
    # ============================================================================
    
    def generate_trial_balance(self, tenant_id: int, as_of_date: str = None) -> Dict:
        """Generate trial balance report"""
        try:
            if not as_of_date:
                as_of_date = date.today().isoformat()
            
            # Get all accounts with their balances
            query = """
                SELECT 
                    coa.id,
                    coa.account_code,
                    coa.account_name,
                    coa.account_type,
                    coa.account_subtype,
                    coa.debit_balance,
                    coa.credit_balance,
                    coa.current_balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.is_active = 1
                    AND (coa.debit_balance != 0 OR coa.credit_balance != 0)
                ORDER BY coa.account_code
            """
            
            accounts = self.db.execute_query(query, (tenant_id,))
            
            # Calculate totals
            total_debits = sum(Decimal(str(acc['debit_balance'])) for acc in accounts)
            total_credits = sum(Decimal(str(acc['credit_balance'])) for acc in accounts)
            
            # Group by account type
            grouped_accounts = {}
            for account in accounts:
                account_type = account['account_type']
                if account_type not in grouped_accounts:
                    grouped_accounts[account_type] = []
                grouped_accounts[account_type].append(account)
            
            return {
                'report_type': 'TRIAL_BALANCE',
                'as_of_date': as_of_date,
                'tenant_id': tenant_id,
                'accounts': accounts,
                'grouped_accounts': grouped_accounts,
                'totals': {
                    'total_debits': float(total_debits),
                    'total_credits': float(total_credits),
                    'difference': float(total_debits - total_credits)
                },
                'is_balanced': abs(total_debits - total_credits) < 0.01
            }
            
        except Exception as e:
            logger.error(f"Error generating trial balance: {str(e)}")
            raise
    
    # ============================================================================
    # PROFIT & LOSS STATEMENT
    # ============================================================================
    
    def generate_profit_loss(self, tenant_id: int, start_date: str, end_date: str) -> Dict:
        """Generate profit & loss statement"""
        try:
            # Get revenue accounts
            revenue_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.credit_balance - coa.debit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'REVENUE'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            revenue_accounts = self.db.execute_query(revenue_query, (tenant_id,))
            total_revenue = sum(Decimal(str(acc['balance'])) for acc in revenue_accounts)
            
            # Get expense accounts
            expense_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.debit_balance - coa.credit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'EXPENSE'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            expense_accounts = self.db.execute_query(expense_query, (tenant_id,))
            total_expenses = sum(Decimal(str(acc['balance'])) for acc in expense_accounts)
            
            # Get cost of goods sold accounts
            cogs_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.debit_balance - coa.credit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'COST_OF_GOODS_SOLD'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            cogs_accounts = self.db.execute_query(cogs_query, (tenant_id,))
            total_cogs = sum(Decimal(str(acc['balance'])) for acc in cogs_accounts)
            
            # Calculate profit/loss
            gross_profit = total_revenue - total_cogs
            net_profit = gross_profit - total_expenses
            
            return {
                'report_type': 'PROFIT_LOSS',
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'tenant_id': tenant_id,
                'revenue': {
                    'accounts': revenue_accounts,
                    'total': float(total_revenue)
                },
                'cost_of_goods_sold': {
                    'accounts': cogs_accounts,
                    'total': float(total_cogs)
                },
                'gross_profit': float(gross_profit),
                'expenses': {
                    'accounts': expense_accounts,
                    'total': float(total_expenses)
                },
                'net_profit': float(net_profit)
            }
            
        except Exception as e:
            logger.error(f"Error generating profit & loss: {str(e)}")
            raise
    
    # ============================================================================
    # BALANCE SHEET
    # ============================================================================
    
    def generate_balance_sheet(self, tenant_id: int, as_of_date: str = None) -> Dict:
        """Generate balance sheet"""
        try:
            if not as_of_date:
                as_of_date = date.today().isoformat()
            
            # Get asset accounts
            assets_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.debit_balance - coa.credit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'ASSET'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            asset_accounts = self.db.execute_query(assets_query, (tenant_id,))
            
            # Group assets by subtype
            current_assets = [acc for acc in asset_accounts if acc['account_subtype'] == 'CURRENT_ASSET']
            fixed_assets = [acc for acc in asset_accounts if acc['account_subtype'] == 'FIXED_ASSET']
            other_assets = [acc for acc in asset_accounts if acc['account_subtype'] not in ['CURRENT_ASSET', 'FIXED_ASSET']]
            
            total_current_assets = sum(Decimal(str(acc['balance'])) for acc in current_assets)
            total_fixed_assets = sum(Decimal(str(acc['balance'])) for acc in fixed_assets)
            total_other_assets = sum(Decimal(str(acc['balance'])) for acc in other_assets)
            total_assets = total_current_assets + total_fixed_assets + total_other_assets
            
            # Get liability accounts
            liabilities_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.credit_balance - coa.debit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'LIABILITY'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            liability_accounts = self.db.execute_query(liabilities_query, (tenant_id,))
            
            # Group liabilities by subtype
            current_liabilities = [acc for acc in liability_accounts if acc['account_subtype'] == 'CURRENT_LIABILITY']
            long_term_liabilities = [acc for acc in liability_accounts if acc['account_subtype'] == 'LONG_TERM_LIABILITY']
            
            total_current_liabilities = sum(Decimal(str(acc['balance'])) for acc in current_liabilities)
            total_long_term_liabilities = sum(Decimal(str(acc['balance'])) for acc in long_term_liabilities)
            total_liabilities = total_current_liabilities + total_long_term_liabilities
            
            # Get equity accounts
            equity_query = """
                SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.account_subtype,
                    coa.credit_balance - coa.debit_balance as balance
                FROM chart_of_accounts coa
                WHERE coa.tenant_id = ? 
                    AND coa.account_type = 'EQUITY'
                    AND coa.is_active = 1
                ORDER BY coa.account_code
            """
            
            equity_accounts = self.db.execute_query(equity_query, (tenant_id,))
            total_equity = sum(Decimal(str(acc['balance'])) for acc in equity_accounts)
            
            # Calculate total liabilities and equity
            total_liabilities_equity = total_liabilities + total_equity
            
            return {
                'report_type': 'BALANCE_SHEET',
                'as_of_date': as_of_date,
                'tenant_id': tenant_id,
                'assets': {
                    'current_assets': {
                        'accounts': current_assets,
                        'total': float(total_current_assets)
                    },
                    'fixed_assets': {
                        'accounts': fixed_assets,
                        'total': float(total_fixed_assets)
                    },
                    'other_assets': {
                        'accounts': other_assets,
                        'total': float(total_other_assets)
                    },
                    'total_assets': float(total_assets)
                },
                'liabilities': {
                    'current_liabilities': {
                        'accounts': current_liabilities,
                        'total': float(total_current_liabilities)
                    },
                    'long_term_liabilities': {
                        'accounts': long_term_liabilities,
                        'total': float(total_long_term_liabilities)
                    },
                    'total_liabilities': float(total_liabilities)
                },
                'equity': {
                    'accounts': equity_accounts,
                    'total_equity': float(total_equity)
                },
                'total_liabilities_equity': float(total_liabilities_equity),
                'is_balanced': abs(total_assets - total_liabilities_equity) < 0.01
            }
            
        except Exception as e:
            logger.error(f"Error generating balance sheet: {str(e)}")
            raise
    
    # ============================================================================
    # GENERAL LEDGER
    # ============================================================================
    
    def generate_general_ledger(self, tenant_id: int, account_id: int = None, start_date: str = None, end_date: str = None) -> Dict:
        """Generate general ledger report"""
        try:
            # Build query conditions
            where_conditions = ['je.tenant_id = ?']
            params = [tenant_id]
            
            if account_id:
                where_conditions.append('jel.account_id = ?')
                params.append(account_id)
            
            if start_date:
                where_conditions.append('je.transaction_date >= ?')
                params.append(start_date)
            
            if end_date:
                where_conditions.append('je.transaction_date <= ?')
                params.append(end_date)
            
            where_clause = ' AND '.join(where_conditions)
            
            # Get journal entry lines with account details
            query = f"""
                SELECT 
                    je.journal_number,
                    je.transaction_date,
                    je.description as journal_description,
                    je.status,
                    coa.account_code,
                    coa.account_name,
                    jel.description as line_description,
                    jel.debit_amount,
                    jel.credit_amount
                FROM journal_entries je
                JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE {where_clause}
                    AND je.status = 'POSTED'
                ORDER BY je.transaction_date, je.journal_number, jel.line_number
            """
            
            entries = self.db.execute_query(query, params)
            
            # Calculate running balance if single account
            if account_id:
                running_balance = Decimal('0')
                for entry in entries:
                    debit = Decimal(str(entry['debit_amount']))
                    credit = Decimal(str(entry['credit_amount']))
                    running_balance += debit - credit
                    entry['running_balance'] = float(running_balance)
            
            return {
                'report_type': 'GENERAL_LEDGER',
                'tenant_id': tenant_id,
                'account_id': account_id,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'entries': entries,
                'count': len(entries)
            }
            
        except Exception as e:
            logger.error(f"Error generating general ledger: {str(e)}")
            raise
    
    # ============================================================================
    # SAVE REPORT
    # ============================================================================
    
    def save_report(self, report_data: Dict, report_type: str, tenant_id: int, generated_by: int) -> int:
        """Save a generated report"""
        try:
            # Generate report code
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            report_code = f"{report_type}_{timestamp}"
            
            # Get current period
            current_period = self.db.execute_query(
                "SELECT id FROM financial_periods WHERE tenant_id = ? AND is_current = 1",
                (tenant_id,)
            )
            period_id = current_period[0]['id'] if current_period else None
            
            # Save report
            report_record = {
                'report_code': report_code,
                'report_name': report_type.replace('_', ' ').title(),
                'report_type': report_type,
                'period_id': period_id,
                'generated_date': date.today().isoformat(),
                'report_data': json.dumps(report_data),
                'status': 'COMPLETED',
                'tenant_id': tenant_id,
                'generated_by': generated_by
            }
            
            report_id = self.db.insert_record('financial_reports', report_record)
            logger.info(f"Saved financial report {report_code} with ID {report_id}")
            return report_id
            
        except Exception as e:
            logger.error(f"Error saving report: {str(e)}")
            raise

    # ============================================================================
    # ENHANCED FINANCIAL REPORTS
    # ============================================================================

    def generate_cash_flow_statement(self, tenant_id: int, start_date: str, end_date: str, method: str = 'INDIRECT') -> Dict:
        """Generate cash flow statement using indirect or direct method"""
        try:
            if method == 'INDIRECT':
                return self._generate_indirect_cash_flow(tenant_id, start_date, end_date)
            else:
                return self._generate_direct_cash_flow(tenant_id, start_date, end_date)
        except Exception as e:
            logger.error(f"Error generating cash flow statement: {str(e)}")
            raise

    def _generate_indirect_cash_flow(self, tenant_id: int, start_date: str, end_date: str) -> Dict:
        """Generate cash flow statement using indirect method"""
        try:
            # Get net income from P&L
            pl_data = self.generate_profit_loss(tenant_id, start_date, end_date)

            # Extract net income from P&L data structure
            net_income = 0
            if 'net_income' in pl_data:
                net_income = pl_data['net_income']
            elif 'summary' in pl_data and 'net_income' in pl_data['summary']:
                net_income = pl_data['summary']['net_income']
            elif 'totals' in pl_data and 'net_income' in pl_data['totals']:
                net_income = pl_data['totals']['net_income']
            else:
                # Calculate net income from revenue and expenses
                total_revenue = pl_data.get('total_revenue', 0)
                total_expenses = pl_data.get('total_expenses', 0)
                net_income = total_revenue - total_expenses

            # Operating Activities - Start with net income and adjust
            operating_activities = {
                'net_income': net_income,
                'adjustments': {}
            }

            # Add back depreciation (non-cash expense)
            depreciation_query = """
                SELECT COALESCE(SUM(jel.debit_amount - jel.credit_amount), 0) as depreciation
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.transaction_date BETWEEN ? AND ?
                    AND je.status = 'POSTED'
                    AND coa.account_name LIKE '%Depreciation%'
            """
            depreciation_result = self.db.execute_query(depreciation_query, (tenant_id, start_date, end_date))
            depreciation = depreciation_result[0]['depreciation'] if depreciation_result else 0
            operating_activities['adjustments']['depreciation'] = depreciation

            # Changes in working capital
            # Accounts Receivable change
            ar_change_query = """
                SELECT
                    COALESCE(SUM(CASE WHEN je.transaction_date < ? THEN (jel.debit_amount - jel.credit_amount) ELSE 0 END), 0) as opening_ar,
                    COALESCE(SUM(CASE WHEN je.transaction_date <= ? THEN (jel.debit_amount - jel.credit_amount) ELSE 0 END), 0) as closing_ar
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.status = 'POSTED'
                    AND coa.account_type = 'ASSET'
                    AND coa.account_subtype = 'CURRENT_ASSET'
                    AND coa.account_name LIKE '%Receivable%'
            """
            ar_result = self.db.execute_query(ar_change_query, (start_date, end_date, tenant_id))
            ar_change = (ar_result[0]['opening_ar'] - ar_result[0]['closing_ar']) if ar_result else 0
            operating_activities['adjustments']['accounts_receivable_change'] = ar_change

            # Accounts Payable change
            ap_change_query = """
                SELECT
                    COALESCE(SUM(CASE WHEN je.transaction_date < ? THEN (jel.credit_amount - jel.debit_amount) ELSE 0 END), 0) as opening_ap,
                    COALESCE(SUM(CASE WHEN je.transaction_date <= ? THEN (jel.credit_amount - jel.debit_amount) ELSE 0 END), 0) as closing_ap
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.status = 'POSTED'
                    AND coa.account_type = 'LIABILITY'
                    AND coa.account_name LIKE '%Payable%'
            """
            ap_result = self.db.execute_query(ap_change_query, (start_date, end_date, tenant_id))
            ap_change = (ap_result[0]['closing_ap'] - ap_result[0]['opening_ap']) if ap_result else 0
            operating_activities['adjustments']['accounts_payable_change'] = ap_change

            # Calculate net cash from operating activities
            operating_cash_flow = (net_income + depreciation + ar_change + ap_change)
            operating_activities['net_cash_from_operations'] = operating_cash_flow

            # Investing Activities
            investing_activities = {}

            # Equipment purchases/sales
            equipment_query = """
                SELECT COALESCE(SUM(jel.debit_amount - jel.credit_amount), 0) as equipment_changes
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.transaction_date BETWEEN ? AND ?
                    AND je.status = 'POSTED'
                    AND coa.account_type = 'ASSET'
                    AND coa.account_subtype = 'FIXED_ASSET'
                    AND coa.account_name NOT LIKE '%Depreciation%'
            """
            equipment_result = self.db.execute_query(equipment_query, (tenant_id, start_date, end_date))
            equipment_changes = equipment_result[0]['equipment_changes'] if equipment_result else 0
            investing_activities['equipment_purchases'] = -equipment_changes  # Negative for purchases
            investing_activities['net_cash_from_investing'] = -equipment_changes

            # Financing Activities
            financing_activities = {}

            # Loan proceeds/payments
            loan_query = """
                SELECT COALESCE(SUM(jel.credit_amount - jel.debit_amount), 0) as loan_changes
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.transaction_date BETWEEN ? AND ?
                    AND je.status = 'POSTED'
                    AND coa.account_type = 'LIABILITY'
                    AND coa.account_name LIKE '%Loan%'
            """
            loan_result = self.db.execute_query(loan_query, (tenant_id, start_date, end_date))
            loan_changes = loan_result[0]['loan_changes'] if loan_result else 0
            financing_activities['loan_proceeds_payments'] = loan_changes
            financing_activities['net_cash_from_financing'] = loan_changes

            # Net change in cash
            net_cash_change = (operating_cash_flow +
                             investing_activities['net_cash_from_investing'] +
                             financing_activities['net_cash_from_financing'])

            # Get cash balances
            cash_query = """
                SELECT
                    COALESCE(SUM(CASE WHEN je.transaction_date < ? THEN (jel.debit_amount - jel.credit_amount) ELSE 0 END), 0) as opening_cash,
                    COALESCE(SUM(CASE WHEN je.transaction_date <= ? THEN (jel.debit_amount - jel.credit_amount) ELSE 0 END), 0) as closing_cash
                FROM journal_entry_lines jel
                JOIN journal_entries je ON jel.journal_entry_id = je.id
                JOIN chart_of_accounts coa ON jel.account_id = coa.id
                WHERE je.tenant_id = ?
                    AND je.status = 'POSTED'
                    AND coa.account_name LIKE '%Cash%'
                    OR coa.account_name LIKE '%Bank%'
            """
            cash_result = self.db.execute_query(cash_query, (start_date, end_date, tenant_id))
            opening_cash = cash_result[0]['opening_cash'] if cash_result else 0
            closing_cash = cash_result[0]['closing_cash'] if cash_result else 0

            return {
                'report_type': 'CASH_FLOW_STATEMENT',
                'method': 'INDIRECT',
                'period': {'start_date': start_date, 'end_date': end_date},
                'operating_activities': operating_activities,
                'investing_activities': investing_activities,
                'financing_activities': financing_activities,
                'cash_summary': {
                    'opening_cash': opening_cash,
                    'net_change': net_cash_change,
                    'closing_cash': closing_cash
                },
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating indirect cash flow: {str(e)}")
            raise

    def generate_comparative_reports(self, tenant_id: int, current_start: str, current_end: str,
                                   prior_start: str, prior_end: str, report_type: str) -> Dict:
        """Generate comparative financial reports"""
        try:
            if report_type == 'PROFIT_LOSS':
                current_data = self.generate_profit_loss(tenant_id, current_start, current_end)
                prior_data = self.generate_profit_loss(tenant_id, prior_start, prior_end)
            elif report_type == 'BALANCE_SHEET':
                current_data = self.generate_balance_sheet(tenant_id, current_end)
                prior_data = self.generate_balance_sheet(tenant_id, prior_end)
            elif report_type == 'TRIAL_BALANCE':
                current_data = self.generate_trial_balance(tenant_id, current_end)
                prior_data = self.generate_trial_balance(tenant_id, prior_end)
            else:
                raise ValueError(f"Unsupported report type for comparison: {report_type}")

            # Calculate variances
            comparative_data = {
                'report_type': f'COMPARATIVE_{report_type}',
                'current_period': {
                    'start_date': current_start,
                    'end_date': current_end,
                    'data': current_data
                },
                'prior_period': {
                    'start_date': prior_start,
                    'end_date': prior_end,
                    'data': prior_data
                },
                'variances': self._calculate_variances(current_data, prior_data, report_type),
                'generated_at': datetime.now().isoformat()
            }

            return comparative_data

        except Exception as e:
            logger.error(f"Error generating comparative reports: {str(e)}")
            raise

    def _calculate_variances(self, current_data: Dict, prior_data: Dict, report_type: str) -> Dict:
        """Calculate variances between current and prior period data"""
        try:
            variances = {}

            if report_type == 'PROFIT_LOSS':
                # Calculate P&L variances
                variances['revenue_variance'] = current_data['total_revenue'] - prior_data['total_revenue']
                variances['expense_variance'] = current_data['total_expenses'] - prior_data['total_expenses']
                variances['net_income_variance'] = current_data['net_income'] - prior_data['net_income']

                # Calculate percentage changes
                if prior_data['total_revenue'] != 0:
                    variances['revenue_variance_percent'] = (variances['revenue_variance'] / prior_data['total_revenue']) * 100
                if prior_data['total_expenses'] != 0:
                    variances['expense_variance_percent'] = (variances['expense_variance'] / prior_data['total_expenses']) * 100
                if prior_data['net_income'] != 0:
                    variances['net_income_variance_percent'] = (variances['net_income_variance'] / prior_data['net_income']) * 100

            elif report_type == 'BALANCE_SHEET':
                # Calculate Balance Sheet variances
                variances['total_assets_variance'] = current_data['total_assets'] - prior_data['total_assets']
                variances['total_liabilities_variance'] = current_data['total_liabilities'] - prior_data['total_liabilities']
                variances['total_equity_variance'] = current_data['total_equity'] - prior_data['total_equity']

                # Calculate percentage changes
                if prior_data['total_assets'] != 0:
                    variances['assets_variance_percent'] = (variances['total_assets_variance'] / prior_data['total_assets']) * 100
                if prior_data['total_liabilities'] != 0:
                    variances['liabilities_variance_percent'] = (variances['total_liabilities_variance'] / prior_data['total_liabilities']) * 100
                if prior_data['total_equity'] != 0:
                    variances['equity_variance_percent'] = (variances['total_equity_variance'] / prior_data['total_equity']) * 100

            return variances

        except Exception as e:
            logger.error(f"Error calculating variances: {str(e)}")
            raise
