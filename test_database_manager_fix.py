#!/usr/bin/env python3
"""
Test script to verify the database manager fix
"""
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_database_manager_methods():
    """Test database manager execute_update vs execute_query"""
    try:
        print("🔍 Testing Database Manager Methods")
        print("-" * 50)
        
        from database_manager import db_manager
        
        # Test execute_query with SELECT (should work)
        print("1. Testing execute_query with SELECT...")
        select_query = "SELECT id, status FROM delivery_notes WHERE id = 5"
        select_result = db_manager.execute_query(select_query)
        print(f"✅ SELECT result: {select_result}")
        
        # Test execute_update with UPDATE (should work)
        print("\n2. Testing execute_update with UPDATE...")
        update_query = """
            UPDATE delivery_notes 
            SET notes = 'Test update from script'
            WHERE id = 5
        """
        try:
            update_result = db_manager.execute_update(update_query)
            print(f"✅ UPDATE result (rows affected): {update_result}")
        except Exception as e:
            print(f"❌ UPDATE failed: {e}")
        
        # Test execute_query with UPDATE (should fail)
        print("\n3. Testing execute_query with UPDATE (should fail)...")
        try:
            bad_result = db_manager.execute_query(update_query)
            print(f"⚠️  Unexpected success: {bad_result}")
        except Exception as e:
            print(f"✅ Expected failure: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_delivery_service_import():
    """Test if delivery service can be imported with the fixes"""
    try:
        print("\n🔍 Testing Delivery Service Import")
        print("-" * 50)
        
        from services.delivery_service import DeliveryService
        service = DeliveryService()
        print("✅ DeliveryService imported successfully")
        
        # Check if the method exists
        if hasattr(service, 'receive_delivery_note'):
            print("✅ receive_delivery_note method exists")
        else:
            print("❌ receive_delivery_note method missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Database Manager Fix")
    print("=" * 60)
    
    tests = [
        ("Database Manager Methods", test_database_manager_methods),
        ("Delivery Service Import", test_delivery_service_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n✅ All tests passed! The fix should work.")
    else:
        print("\n❌ Some tests failed. Check the issues above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
