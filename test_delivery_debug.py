#!/usr/bin/env python3
"""
Debug script to test delivery notes functionality
"""
import sys
import os
import sqlite3
import json
from datetime import datetime

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_database_connection():
    """Test database connection and check delivery_notes table"""
    try:
        db_path = os.path.join('backend', 'data', 'avini_labs.db')
        print(f"Testing database connection to: {db_path}")
        
        if not os.path.exists(db_path):
            print(f"❌ Database file not found: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check if delivery_notes table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='delivery_notes'
        """)
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ delivery_notes table does not exist")
            return False
            
        print("✅ delivery_notes table exists")
        
        # Check table structure
        cursor.execute("PRAGMA table_info(delivery_notes)")
        columns = cursor.fetchall()
        print(f"📋 Table structure ({len(columns)} columns):")
        for col in columns:
            print(f"  - {col['name']}: {col['type']}")
        
        # Count delivery notes
        cursor.execute("SELECT COUNT(*) as count FROM delivery_notes")
        count = cursor.fetchone()['count']
        print(f"📊 Total delivery notes: {count}")
        
        # Show sample data if any exists
        if count > 0:
            cursor.execute("SELECT * FROM delivery_notes LIMIT 3")
            samples = cursor.fetchall()
            print("📄 Sample delivery notes:")
            for i, sample in enumerate(samples, 1):
                print(f"  {i}. ID: {sample['id']}, Number: {sample['delivery_number'] if sample['delivery_number'] else 'N/A'}, Status: {sample['status'] if sample['status'] else 'N/A'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_api_imports():
    """Test if we can import the procurement API modules"""
    try:
        print("\n🔍 Testing API imports...")
        
        # Test database manager import
        from backend.database_manager import db_manager
        print("✅ database_manager imported successfully")
        
        # Test procurement routes import
        from backend.routes.procurement_routes_db import procurement_bp
        print("✅ procurement_routes_db imported successfully")
        
        # Test database connection through db_manager
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM delivery_notes")
        count = result[0]['count'] if result else 0
        print(f"✅ db_manager query successful: {count} delivery notes found")
        
        return True
        
    except Exception as e:
        print(f"❌ API import failed: {e}")
        return False

def test_delivery_notes_query():
    """Test the actual delivery notes query used by the API"""
    try:
        print("\n🔍 Testing delivery notes query...")
        
        from backend.database_manager import db_manager
        
        # Test the exact query used in the API
        query = """
            SELECT dn.*,
                   ft.name as from_tenant_name, ft.site_code as from_site_code,
                   tt.name as to_tenant_name, tt.site_code as to_site_code
            FROM delivery_notes dn
            LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
            LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
            WHERE 1=1
            ORDER BY dn.created_at DESC
        """
        
        results = db_manager.execute_query(query)
        print(f"✅ Query executed successfully: {len(results)} results")
        
        if results:
            print("📄 Sample results:")
            for i, result in enumerate(results[:3], 1):
                print(f"  {i}. ID: {result.get('id')}, Number: {result.get('delivery_number')}")
                print(f"     From: {result.get('from_tenant_name', 'N/A')} -> To: {result.get('to_tenant_name', 'N/A')}")
                print(f"     Status: {result.get('status', 'N/A')}, Created: {result.get('created_at', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Delivery notes query failed: {e}")
        return False

def check_tenants_table():
    """Check if tenants table has data"""
    try:
        print("\n🔍 Checking tenants table...")
        
        from backend.database_manager import db_manager
        
        tenants = db_manager.execute_query("SELECT id, name, site_code FROM tenants LIMIT 5")
        print(f"✅ Found {len(tenants)} tenants")
        
        for tenant in tenants:
            print(f"  - ID: {tenant['id']}, Name: {tenant['name']}, Site: {tenant.get('site_code', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tenants check failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Delivery Notes Debug Test")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("API Imports", test_api_imports),
        ("Tenants Table", check_tenants_table),
        ("Delivery Notes Query", test_delivery_notes_query),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n🎉 All tests passed! The delivery notes system should be working.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    main()
