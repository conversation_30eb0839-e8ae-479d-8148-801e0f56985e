import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Badge, Alert } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faShoppingCart,
  faClipboardList,
  faFileInvoiceDollar,
  faTruck,
  faPlus,
  faEye,
  faChartLine,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import procurementAPI from '../../services/procurementAPI';

const ProcurementDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getDashboard();
      setDashboardData(response.data);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'submitted': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'fulfilled': return 'primary';
      case 'paid': return 'success';
      case 'delivered': return 'success';
      case 'in_transit': return 'info';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faShoppingCart} className="me-2 text-primary" />
            Procurement Dashboard
          </h2>
          <p className="text-white mb-0 text-primary">Manage purchase requests, orders, and deliveries</p>
        </div>
        {hasModuleAccess('PURCHASE_REQUESTS') && (
          <Link to="/procurement/requests/create" className="btn btn-primary">
            <FontAwesomeIcon icon={faPlus} className="me-2" />
            New Purchase Request
          </Link>
        )}
      </div>

      {/* Metrics Cards */}
      <Row className="mb-4">
        <Col lg={3} md={6} className="mb-3">
          <Card className="h-100 border-0 shadow-sm">
            <Card.Body>
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 rounded-circle p-3">
                    <FontAwesomeIcon icon={faClipboardList} className="text-primary fa-lg" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-white small">Purchase Requests</div>
                  <div className="h4 mb-0 text-white">{dashboardData?.purchase_requests?.total || 0}</div>
                  <div className="small">
                    <span className="text-warning">{dashboardData?.purchase_requests?.pending || 0} pending</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={3} md={6} className="mb-3">
          <Card className="h-100 border-0 shadow-sm">
            <Card.Body>
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 rounded-circle p-3">
                    <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-success fa-lg" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-white small">Proforma Invoices</div>
                  <div className="h4 mb-0 text-white">{dashboardData?.proforma_invoices?.total || 0}</div>
                  <div className="small">
                    <span className="text-warning">{dashboardData?.proforma_invoices?.pending_payment || 0} pending payment</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={3} md={6} className="mb-3">
          <Card className="h-100 border-0 shadow-sm">
            <Card.Body>
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 rounded-circle p-3">
                    <FontAwesomeIcon icon={faTruck} className="text-info fa-lg" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-white small">Deliveries</div>
                  <div className="h4 mb-0 text-white">{dashboardData?.deliveries?.total || 0}</div>
                  <div className="small">
                    <span className="text-info">{dashboardData?.deliveries?.in_transit || 0} in transit</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={3} md={6} className="mb-3">
          <Card className="h-100 border-0 shadow-sm">
            <Card.Body>
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 rounded-circle p-3">
                    <FontAwesomeIcon icon={faChartLine} className="text-warning fa-lg" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-white small">Purchase Orders</div>
                  <div className="h4 mb-0 text-white">{dashboardData?.purchase_orders?.total || 0}</div>
                  <div className="small">
                    <span className="text-warning">{dashboardData?.purchase_orders?.pending || 0} pending</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row className="mb-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white border-0">
              <h5 className="mb-0 text-primary">Quick Actions</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                {hasModuleAccess('PURCHASE_REQUESTS') && (
                  <Col lg={3} md={6} className="mb-3">
                    <Link to="/procurement/requests" className="text-decoration-none">
                      <div className="d-flex align-items-center p-3 bg-light rounded hover-shadow">
                        <FontAwesomeIcon icon={faClipboardList} className="text-primary me-3" />
                        <div>
                          <div className="fw-semibold">Purchase Requests</div>
                          <div className="small text-white">View and manage requests</div>
                        </div>
                      </div>
                    </Link>
                  </Col>
                )}

                {hasModuleAccess('PROFORMA_INVOICES') && (
                  <Col lg={3} md={6} className="mb-3">
                    <Link to="/procurement/proforma" className="text-decoration-none">
                      <div className="d-flex align-items-center p-3 bg-light rounded hover-shadow">
                        <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-success me-3" />
                        <div>
                          <div className="fw-semibold">Proforma Invoices</div>
                          <div className="small text-white">Payment processing</div>
                        </div>
                      </div>
                    </Link>
                  </Col>
                )}

                {hasModuleAccess('DELIVERY_MANAGEMENT') && (
                  <Col lg={3} md={6} className="mb-3">
                    <Link to="/procurement/deliveries" className="text-decoration-none">
                      <div className="d-flex align-items-center p-3 bg-light rounded hover-shadow">
                        <FontAwesomeIcon icon={faTruck} className="text-info me-3" />
                        <div>
                          <div className="fw-semibold">Deliveries</div>
                          <div className="small text-white">Track shipments</div>
                        </div>
                      </div>
                    </Link>
                  </Col>
                )}

                {hasModuleAccess('PURCHASE_ORDERS') && (
                  <Col lg={3} md={6} className="mb-3">
                    <Link to="/procurement/orders" className="text-decoration-none">
                      <div className="d-flex align-items-center p-3 bg-light rounded hover-shadow">
                        <FontAwesomeIcon icon={faChartLine} className="text-warning me-3" />
                        <div>
                          <div className="fw-semibold">Purchase Orders</div>
                          <div className="small text-white">Supplier orders</div>
                        </div>
                      </div>
                    </Link>
                  </Col>
                )}
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Activities */}
      <Row>
        <Col lg={6} className="mb-4">
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0 d-flex justify-content-between align-items-center">
              <h6 className="mb-0 text-primary">Recent Purchase Requests</h6>
              <Link to="/procurement/requests" className="btn btn-sm btn-outline-primary">
                <FontAwesomeIcon icon={faEye} className="me-1" />
                View All
              </Link>
            </Card.Header>
            <Card.Body>
              {dashboardData?.recent_activities?.requests?.length > 0 ? (
                <div className="list-group list-group-flush">
                  {dashboardData.recent_activities.requests.map((request) => (
                    <div key={request.id} className="list-group-item border-0 px-0">
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <div className="fw-semibold">{request.request_number}</div>
                          <div className="small text-white">
                            {request.items?.length || 0} items • {request.priority} priority
                          </div>
                        </div>
                        <Badge bg={getStatusBadgeVariant(request.status)}>
                          {request.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-white py-4">
                  <FontAwesomeIcon icon={faClipboardList} className="fa-2x mb-2 opacity-50" />
                  <div>No recent purchase requests</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={6} className="mb-4">
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0 d-flex justify-content-between align-items-center">
              <h6 className="mb-0 text-primary">Recent Deliveries</h6>
              <Link to="/procurement/deliveries" className="btn btn-sm btn-outline-primary">
                <FontAwesomeIcon icon={faEye} className="me-1" />
                View All
              </Link>
            </Card.Header>
            <Card.Body>
              {dashboardData?.recent_activities?.deliveries?.length > 0 ? (
                <div className="list-group list-group-flush">
                  {dashboardData.recent_activities.deliveries.map((delivery) => (
                    <div key={delivery.id} className="list-group-item border-0 px-0">
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <div className="fw-semibold">{delivery.delivery_number}</div>
                          <div className="small text-white">
                            {delivery.delivery_type} • {delivery.expected_delivery_date}
                          </div>
                        </div>
                        <Badge bg={getStatusBadgeVariant(delivery.status)}>
                          {delivery.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-white py-4">
                  <FontAwesomeIcon icon={faTruck} className="fa-2x mb-2 opacity-50" />
                  <div>No recent deliveries</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ProcurementDashboard;
