# Procurement-Inventory Module Integration Documentation

## Overview
This document outlines the connections, integrations, and data flows between the Procurement and Inventory modules in the AVINIRS system.

## Module Architecture

### Procurement Module
- **Location**: `src/pages/procurement/`, `backend/routes/procurement_routes_db.py`
- **Primary Functions**: Purchase requests, purchase orders, delivery notes, proforma invoices
- **Database Tables**: `purchase_requests`, `purchase_orders`, `delivery_notes`, `proforma_invoices`, `delivery_note_items`

### Inventory Module  
- **Location**: `src/pages/inventory/`, `backend/routes/inventory_routes_db.py`
- **Primary Functions**: Stock management, inventory tracking, storeroom organization
- **Database Tables**: `inventory`, `inventory_transactions`, `storerooms`

## Integration Points

### 1. Automatic Inventory Updates from Deliveries
**File**: `backend/services/delivery_service.py`

When delivery notes are received/confirmed, the system automatically updates inventory:

```python
def receive_delivery_note(self, dn_id, receipt_data, user_id):
    # Updates inventory quantities when deliveries are confirmed
    # Creates inventory transactions for audit trail
    # Links delivery items to inventory records
```

**Data Flow**:
1. Delivery note confirmed → `delivery_notes.status = 'delivered'`
2. For each delivery item → Check if inventory item exists
3. If exists → Update `inventory.quantity` + Create `inventory_transactions` record
4. If not exists → Create new `inventory` record + Create transaction

### 2. Storeroom-Based Organization
**Integration**: Both modules use the `storerooms` table for organization

- **Procurement**: Purchase requests specify target storeroom (`purchase_requests.storeroom_id`)
- **Inventory**: All inventory items belong to a storeroom (`inventory.storeroom_id`)
- **Delivery**: Received items are added to the storeroom specified in the purchase request

### 3. Shared Database Tables

#### Core Shared Tables:
- `storerooms` - Physical storage locations
- `tenants` - Organizational units (hub/franchises)
- `users` - User management and permissions

#### Integration Tables:
- `inventory_transactions` - Tracks all stock movements including procurement receipts
- `delivery_note_items` - Links delivery items to inventory updates

### 4. API Integrations

#### Procurement APIs that affect Inventory:
- `POST /api/procurement/delivery-notes/<id>/confirm` → Updates inventory
- `POST /api/procurement/delivery-notes/<id>/receive` → Updates inventory
- `POST /api/procurement/purchase-orders/<id>/create-delivery` → Prepares for inventory update

#### Inventory APIs used by Procurement:
- `GET /api/inventory-db` → Check stock levels for purchase decisions
- `POST /api/inventory-db/<id>/transaction` → Manual stock adjustments

### 5. Automated Procurement System
**File**: `backend/services/automated_procurement_service.py`

The system monitors inventory levels and automatically creates purchase requests:

```python
# Monitors inventory.quantity vs storerooms.min_quantity_threshold
# Creates automated purchase requests when stock is low
# Links to procurement workflow for approval and ordering
```

## Data Flow Diagrams

### Purchase-to-Inventory Flow:
```
Purchase Request → Purchase Order → Delivery Note → Inventory Update
     ↓                  ↓              ↓              ↓
storeroom_id      storeroom_id    delivery_items   inventory.quantity++
                                                   inventory_transactions
```

### Inventory Monitoring Flow:
```
Inventory Levels → Automated Check → Purchase Request → Procurement Workflow
     ↓                    ↓               ↓                ↓
quantity ≤ threshold  → Create PR  → Approval Process → Stock Replenishment
```

## Key Integration Features

### 1. Tenant-Based Access Control
Both modules respect tenant boundaries:
- Hub users can see all data
- Franchise users see only their tenant's data
- Storerooms are tenant-specific

### 2. Transaction Audit Trail
All inventory changes from procurement are logged:
- `inventory_transactions.reference_type = 'delivery_receipt'`
- `inventory_transactions.reference_id = delivery_note_id`
- Complete audit trail from purchase to stock

### 3. Real-Time Stock Updates
Delivery confirmations immediately update inventory:
- No manual intervention required
- Automatic quantity adjustments
- Transaction logging for accountability

## Configuration and Settings

### Storeroom Configuration:
- `auto_reorder_enabled` - Enables automated procurement
- `min_quantity_threshold` - Triggers automatic purchase requests
- `max_quantity_threshold` - Limits automatic order quantities

### Integration Settings:
- Tenant-based access control
- Module permissions (PROCUREMENT, INVENTORY)
- Role-based operations (admin, hub_admin, franchise_user)

## Error Handling and Recovery

### Delivery Receipt Failures:
- Delivery can be confirmed even if inventory update fails
- Accounting entries are created separately
- Manual inventory adjustment available as fallback

### Data Consistency:
- Database transactions ensure atomicity
- Rollback mechanisms for failed operations
- Audit trail for troubleshooting

## Future Enhancement Opportunities

1. **Real-time Inventory Alerts**: Push notifications for low stock
2. **Predictive Procurement**: ML-based demand forecasting
3. **Supplier Integration**: Direct API connections with suppliers
4. **Mobile Inventory Management**: Barcode scanning for receipts
5. **Advanced Analytics**: Procurement vs inventory performance metrics

## Troubleshooting Common Issues

### Issue: Inventory not updating after delivery confirmation
**Solution**: Check `delivery_service.py` logs, verify storeroom_id mapping

### Issue: Automated procurement not triggering
**Solution**: Verify storeroom `auto_reorder_enabled` and threshold settings

### Issue: Access denied errors
**Solution**: Check user tenant_id and module permissions

## API Endpoint Summary

### Procurement Endpoints:
- `GET /api/procurement/delivery-notes` - List delivery notes
- `POST /api/procurement/delivery-notes` - Create delivery note
- `POST /api/procurement/delivery-notes/<id>/confirm` - Confirm delivery (updates inventory)
- `GET /api/procurement/proforma-invoices` - List proforma invoices

### Inventory Endpoints:
- `GET /api/inventory-db` - List inventory items
- `POST /api/inventory-db` - Create inventory item
- `POST /api/inventory-db/<id>/transaction` - Add stock transaction
- `GET /api/inventory-db/transactions` - View transaction history

This integration ensures seamless flow from procurement decisions to inventory management, maintaining data consistency and providing complete audit trails throughout the process.
