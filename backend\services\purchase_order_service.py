"""
Purchase Order Service
Handles complete PO generation from approved purchase requests
"""

from database_manager import db_manager
from datetime import datetime, timedelta
import logging

class PurchaseOrderService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_po_from_purchase_request(self, purchase_request_id, supplier_id=None, user_id=None):
        """
        Generate a Purchase Order from an approved Purchase Request
        """
        try:
            # Get the purchase request with items
            pr_query = """
                SELECT pr.*, 
                       rt.name as requesting_tenant_name,
                       ht.name as hub_tenant_name,
                       s.name as storeroom_name
                FROM purchase_requests pr
                LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
                LEFT JOIN tenants ht ON pr.hub_tenant_id = ht.id
                LEFT JOIN storerooms s ON pr.storeroom_id = s.id
                WHERE pr.id = ? AND pr.status = 'approved'
            """
            
            pr_results = db_manager.execute_query(pr_query, (purchase_request_id,))
            if not pr_results:
                raise Exception("Purchase request not found or not approved")
            
            purchase_request = pr_results[0]
            
            # Get purchase request items
            items_query = """
                SELECT * FROM purchase_request_items 
                WHERE purchase_request_id = ?
                ORDER BY id
            """
            pr_items = db_manager.execute_query(items_query, (purchase_request_id,))
            
            if not pr_items:
                raise Exception("No items found in purchase request")
            
            # Generate PO number
            po_number = self._generate_po_number(purchase_request['hub_tenant_id'])
            
            # Calculate totals
            subtotal = sum(item['total_estimated_amount'] or 0 for item in pr_items)
            tax_rate = 18.0  # Default GST rate
            tax_amount = subtotal * (tax_rate / 100)
            total_amount = subtotal + tax_amount
            
            # Create Purchase Order
            po_data = {
                'po_number': po_number,
                'supplier_id': supplier_id,
                'tenant_id': purchase_request['hub_tenant_id'],
                'status': 'draft',
                'order_date': datetime.now().date().isoformat(),
                'expected_delivery_date': (datetime.now() + timedelta(days=7)).date().isoformat(),
                'subtotal': subtotal,
                'tax_rate': tax_rate,
                'tax_amount': tax_amount,
                'total_amount': total_amount,
                'payment_terms': 'Net 30',
                'delivery_address': f"Delivery to {purchase_request['requesting_tenant_name']} - {purchase_request['storeroom_name']}",
                'notes': f"Generated from Purchase Request: {purchase_request['request_number']}",
                'purchase_request_id': purchase_request_id,
                'created_by': user_id or 1
            }
            
            # Insert Purchase Order
            po_insert_query = """
                INSERT INTO purchase_orders (
                    po_number, supplier_id, tenant_id, status, order_date, expected_delivery_date,
                    subtotal, tax_rate, tax_amount, total_amount, payment_terms, delivery_address,
                    notes, purchase_request_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            po_params = (
                po_data['po_number'], po_data['supplier_id'], po_data['tenant_id'],
                po_data['status'], po_data['order_date'], po_data['expected_delivery_date'],
                po_data['subtotal'], po_data['tax_rate'], po_data['tax_amount'],
                po_data['total_amount'], po_data['payment_terms'], po_data['delivery_address'],
                po_data['notes'], po_data['purchase_request_id'], po_data['created_by']
            )
            
            po_id = db_manager.execute_query(po_insert_query, po_params)
            
            # Insert Purchase Order Items
            for pr_item in pr_items:
                po_item_query = """
                    INSERT INTO purchase_order_items (
                        purchase_order_id, item_name, description, quantity, unit,
                        unit_price, total_amount, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                po_item_params = (
                    po_id,
                    pr_item['item_name'],
                    pr_item['description'],
                    pr_item['requested_quantity'],
                    pr_item['unit'],
                    pr_item['estimated_unit_price'] or 0,
                    pr_item['total_estimated_amount'] or 0,
                    pr_item['notes']
                )
                
                db_manager.execute_query(po_item_query, po_item_params)
            
            # Update Purchase Request status to 'processing'
            update_pr_query = """
                UPDATE purchase_requests 
                SET status = 'processing', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """
            db_manager.execute_query(update_pr_query, (purchase_request_id,))
            
            self.logger.info(f"Purchase Order {po_number} created successfully from PR {purchase_request['request_number']}")
            
            return {
                'success': True,
                'po_id': po_id,
                'po_number': po_number,
                'total_amount': total_amount,
                'items_count': len(pr_items)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating PO from PR {purchase_request_id}: {e}")
            raise e
    
    def _generate_po_number(self, tenant_id):
        """Generate a unique PO number"""
        try:
            # Get tenant site code
            tenant_query = "SELECT site_code FROM tenants WHERE id = ?"
            tenant_result = db_manager.execute_query(tenant_query, (tenant_id,))
            site_code = tenant_result[0]['site_code'] if tenant_result else 'HUB'
            
            # Get next sequence number for today
            today = datetime.now().strftime('%Y%m%d')
            sequence_query = """
                SELECT COUNT(*) as count 
                FROM purchase_orders 
                WHERE po_number LIKE ? AND DATE(created_at) = DATE('now')
            """
            sequence_result = db_manager.execute_query(sequence_query, (f"PO-{site_code}-{today}-%",))
            sequence = (sequence_result[0]['count'] if sequence_result else 0) + 1
            
            return f"PO-{site_code}-{today}-{sequence:03d}"
            
        except Exception as e:
            self.logger.error(f"Error generating PO number: {e}")
            return f"PO-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def send_purchase_order(self, po_id, user_id):
        """Mark purchase order as sent to supplier"""
        try:
            # Update PO status
            update_query = """
                UPDATE purchase_orders 
                SET status = 'sent', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND status = 'draft'
            """
            
            result = db_manager.execute_query(update_query, (po_id,))
            
            if result:
                self.logger.info(f"Purchase Order {po_id} sent to supplier")
                return {'success': True, 'message': 'Purchase order sent successfully'}
            else:
                return {'success': False, 'error': 'Purchase order not found or already sent'}
                
        except Exception as e:
            self.logger.error(f"Error sending PO {po_id}: {e}")
            raise e
    
    def confirm_purchase_order(self, po_id, user_id, confirmation_details=None):
        """Confirm purchase order receipt from supplier"""
        try:
            # Update PO status
            update_query = """
                UPDATE purchase_orders 
                SET status = 'confirmed', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND status = 'sent'
            """
            
            result = db_manager.execute_query(update_query, (po_id,))
            
            if result:
                self.logger.info(f"Purchase Order {po_id} confirmed by supplier")
                return {'success': True, 'message': 'Purchase order confirmed successfully'}
            else:
                return {'success': False, 'error': 'Purchase order not found or not in sent status'}
                
        except Exception as e:
            self.logger.error(f"Error confirming PO {po_id}: {e}")
            raise e
    
    def get_purchase_order_with_items(self, po_id):
        """Get complete purchase order details with items"""
        try:
            # Get PO details
            po_query = """
                SELECT po.*, 
                       t.name as tenant_name, t.site_code,
                       pr.request_number as pr_number,
                       u.username as created_by_username
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                LEFT JOIN users u ON po.created_by = u.id
                WHERE po.id = ?
            """
            
            po_results = db_manager.execute_query(po_query, (po_id,))
            if not po_results:
                return None
            
            purchase_order = po_results[0]
            
            # Get PO items
            items_query = """
                SELECT * FROM purchase_order_items 
                WHERE purchase_order_id = ?
                ORDER BY id
            """
            po_items = db_manager.execute_query(items_query, (po_id,))
            
            purchase_order['items'] = po_items
            
            return purchase_order
            
        except Exception as e:
            self.logger.error(f"Error getting PO {po_id}: {e}")
            raise e
    
    def get_purchase_orders_for_tenant(self, tenant_id, status=None):
        """Get all purchase orders for a tenant"""
        try:
            query = """
                SELECT po.*, 
                       t.name as tenant_name, t.site_code,
                       pr.request_number as pr_number,
                       u.username as created_by_username,
                       COUNT(poi.id) as items_count
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                LEFT JOIN users u ON po.created_by = u.id
                LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                WHERE po.tenant_id = ?
            """
            params = [tenant_id]
            
            if status:
                query += " AND po.status = ?"
                params.append(status)
            
            query += " GROUP BY po.id ORDER BY po.created_at DESC"
            
            return db_manager.execute_query(query, tuple(params))
            
        except Exception as e:
            self.logger.error(f"Error getting POs for tenant {tenant_id}: {e}")
            raise e
