#!/usr/bin/env python3
"""
Check current procurement workflow status and guide next steps
"""
import sys
import os

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def check_approved_requests():
    """Check approved purchase requests and their status"""
    try:
        print("🔍 Checking Approved Purchase Requests")
        print("=" * 50)
        
        from database_manager import db_manager
        
        # Get approved purchase requests
        approved_query = """
            SELECT pr.*, 
                   rt.name as requesting_tenant_name,
                   ht.name as hub_tenant_name
            FROM purchase_requests pr
            LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
            LEFT JOIN tenants ht ON pr.hub_tenant_id = ht.id
            WHERE pr.status = 'approved'
            ORDER BY pr.approved_at DESC
        """
        
        approved_requests = db_manager.execute_query(approved_query)
        
        if not approved_requests:
            print("❌ No approved purchase requests found")
            return False
        
        print(f"✅ Found {len(approved_requests)} approved purchase requests:")
        
        for req in approved_requests:
            print(f"\n📋 Request ID: {req['id']}")
            print(f"   Number: {req['request_number']}")
            print(f"   From: {req['requesting_tenant_name']}")
            print(f"   To: {req['hub_tenant_name']}")
            print(f"   Amount: ${req['total_estimated_amount']}")
            print(f"   Approved: {req['approved_at']}")
            
            # Check if PO exists for this request
            po_query = "SELECT * FROM purchase_orders WHERE purchase_request_id = ?"
            pos = db_manager.execute_query(po_query, (req['id'],))
            
            if pos:
                po = pos[0]
                print(f"   ✅ PO Generated: {po['po_number']} (Status: {po['status']})")
                
                # Check delivery notes for this PO
                dn_query = "SELECT * FROM delivery_notes WHERE purchase_order_id = ?"
                dns = db_manager.execute_query(dn_query, (po['id'],))
                
                if dns:
                    dn = dns[0]
                    print(f"   ✅ Delivery Note: {dn['delivery_number']} (Status: {dn['status']})")
                else:
                    print(f"   ⚠️  No delivery note created yet")
            else:
                print(f"   ❌ No PO generated yet - THIS IS THE NEXT STEP!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking approved requests: {e}")
        return False

def check_purchase_orders():
    """Check current purchase orders status"""
    try:
        print("\n🔍 Checking Purchase Orders")
        print("=" * 50)
        
        from database_manager import db_manager
        
        # Get all purchase orders
        po_query = """
            SELECT po.*, 
                   pr.request_number,
                   rt.name as requesting_tenant_name
            FROM purchase_orders po
            LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
            LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
            ORDER BY po.created_at DESC
        """
        
        purchase_orders = db_manager.execute_query(po_query)
        
        if not purchase_orders:
            print("❌ No purchase orders found")
            return False
        
        print(f"✅ Found {len(purchase_orders)} purchase orders:")
        
        for po in purchase_orders:
            print(f"\n📄 PO ID: {po['id']}")
            print(f"   Number: {po['po_number']}")
            print(f"   Status: {po['status']}")
            print(f"   Amount: ${po['total_amount']}")
            print(f"   From Request: {po['request_number']}")
            print(f"   For: {po['requesting_tenant_name']}")
            
            if po['status'] == 'draft':
                print(f"   ⚠️  Next Step: Send PO to supplier")
            elif po['status'] == 'sent':
                print(f"   ⚠️  Next Step: Confirm PO from supplier")
            elif po['status'] == 'confirmed':
                print(f"   ⚠️  Next Step: Create delivery note")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking purchase orders: {e}")
        return False

def check_delivery_notes():
    """Check current delivery notes status"""
    try:
        print("\n🔍 Checking Delivery Notes")
        print("=" * 50)
        
        from database_manager import db_manager
        
        # Get all delivery notes
        dn_query = """
            SELECT dn.*, 
                   po.po_number,
                   ft.name as from_tenant_name,
                   tt.name as to_tenant_name
            FROM delivery_notes dn
            LEFT JOIN purchase_orders po ON dn.purchase_order_id = po.id
            LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
            LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
            ORDER BY dn.created_at DESC
        """
        
        delivery_notes = db_manager.execute_query(dn_query)
        
        if not delivery_notes:
            print("❌ No delivery notes found")
            return False
        
        print(f"✅ Found {len(delivery_notes)} delivery notes:")
        
        for dn in delivery_notes:
            print(f"\n📦 DN ID: {dn['id']}")
            print(f"   Number: {dn['delivery_number']}")
            print(f"   Status: {dn['status']}")
            print(f"   From: {dn['from_tenant_name']} → To: {dn['to_tenant_name']}")
            print(f"   PO: {dn['po_number']}")
            
            if dn['status'] == 'prepared':
                print(f"   ⚠️  Next Step: Mark as dispatched")
            elif dn['status'] == 'dispatched':
                print(f"   ⚠️  Next Step: Confirm delivery receipt")
            elif dn['status'] == 'delivered':
                print(f"   ✅ Workflow complete!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking delivery notes: {e}")
        return False

def check_available_endpoints():
    """Check what procurement endpoints are available"""
    try:
        print("\n🔍 Available Procurement Endpoints")
        print("=" * 50)
        
        from app import app
        
        procurement_routes = []
        for rule in app.url_map.iter_rules():
            if 'procurement' in rule.rule:
                methods = [m for m in rule.methods if m not in ['HEAD', 'OPTIONS']]
                procurement_routes.append({
                    'rule': rule.rule,
                    'methods': methods,
                    'endpoint': rule.endpoint
                })
        
        # Group by functionality
        po_routes = [r for r in procurement_routes if 'purchase-order' in r['rule']]
        pr_routes = [r for r in procurement_routes if 'purchase-request' in r['rule']]
        dn_routes = [r for r in procurement_routes if 'delivery-note' in r['rule']]
        
        print("📄 Purchase Request Routes:")
        for route in pr_routes:
            print(f"  {route['rule']} [{', '.join(route['methods'])}]")
        
        print("\n📄 Purchase Order Routes:")
        for route in po_routes:
            print(f"  {route['rule']} [{', '.join(route['methods'])}]")
        
        print("\n📄 Delivery Note Routes:")
        for route in dn_routes:
            print(f"  {route['rule']} [{', '.join(route['methods'])}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking endpoints: {e}")
        return False

def provide_next_steps():
    """Provide specific next steps based on current state"""
    print("\n🚀 NEXT STEPS GUIDE")
    print("=" * 50)
    
    print("Based on your approved purchase requests, here's what to do next:")
    print()
    print("1. 📋 GENERATE PURCHASE ORDER")
    print("   URL: http://localhost:3001/procurement/purchase-orders")
    print("   Action: Click 'Generate PO' button for approved requests")
    print("   API: POST /api/procurement/purchase-requests/{id}/generate-po")
    print()
    print("2. 📤 SEND PO TO SUPPLIER")
    print("   Action: Review generated PO and mark as 'Sent'")
    print("   API: POST /api/procurement/purchase-orders/{id}/send")
    print()
    print("3. ✅ CONFIRM PO FROM SUPPLIER")
    print("   Action: Mark PO as 'Confirmed' when supplier accepts")
    print("   API: POST /api/procurement/purchase-orders/{id}/confirm")
    print()
    print("4. 📦 CREATE DELIVERY NOTE")
    print("   Action: Generate delivery note from confirmed PO")
    print("   API: POST /api/procurement/purchase-orders/{id}/create-delivery")
    print()
    print("5. 🚚 DISPATCH GOODS")
    print("   URL: http://localhost:3001/procurement/deliveries")
    print("   Action: Mark delivery as 'Dispatched'")
    print("   API: POST /api/procurement/delivery-notes/{id}/dispatch")
    print()
    print("6. ✅ CONFIRM DELIVERY")
    print("   Action: Recipient confirms delivery receipt")
    print("   API: POST /api/procurement/delivery-notes/{id}/confirm")

def main():
    """Main function"""
    print("🚀 AVINIRS Procurement Workflow Status Check")
    print("=" * 60)
    
    tests = [
        ("Approved Purchase Requests", check_approved_requests),
        ("Purchase Orders", check_purchase_orders),
        ("Delivery Notes", check_delivery_notes),
        ("Available Endpoints", check_available_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Checking: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    provide_next_steps()
    
    print("\n" + "=" * 60)
    print("📊 Status Check Results:")
    for test_name, success in results:
        status = "✅ FOUND" if success else "❌ NONE"
        print(f"  {test_name}: {status}")

if __name__ == "__main__":
    main()
