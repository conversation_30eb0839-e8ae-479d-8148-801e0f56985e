from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
import uuid
import json
import os
from functools import wraps

# Import utilities
from utils import (
    token_required, read_data, write_data, paginate_results, 
    filter_data_by_tenant, check_tenant_access, require_module_access,
    require_role, get_user_accessible_modules
)

procurement_bp = Blueprint('procurement', __name__)

# Helper function to generate unique numbers
def generate_request_number():
    """Generate unique purchase request number"""
    requests = read_data('purchase_requests.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([r for r in requests if r.get('request_number', '').startswith(f'PR-{today}')]) + 1
    return f'PR-{today}-{count:04d}'

def generate_po_number():
    """Generate unique purchase order number"""
    orders = read_data('purchase_orders.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([o for o in orders if o.get('po_number', '').startswith(f'PO-{today}')]) + 1
    return f'PO-{today}-{count:04d}'

def generate_proforma_number():
    """Generate unique proforma invoice number"""
    invoices = read_data('proforma_invoices.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([i for i in invoices if i.get('proforma_number', '').startswith(f'PF-{today}')]) + 1
    return f'PF-{today}-{count:04d}'

def generate_delivery_number():
    """Generate unique delivery note number"""
    notes = read_data('delivery_notes.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([n for n in notes if n.get('delivery_number', '').startswith(f'DN-{today}')]) + 1
    return f'DN-{today}-{count:04d}'

def generate_transfer_number():
    """Generate unique transfer number"""
    transfers = read_data('inventory_transfers.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([t for t in transfers if t.get('transfer_number', '').startswith(f'TR-{today}')]) + 1
    return f'TR-{today}-{count:04d}'

def generate_transaction_number():
    """Generate unique payment transaction number"""
    transactions = read_data('payment_transactions.json')
    today = datetime.now().strftime('%Y%m%d')
    count = len([t for t in transactions if t.get('transaction_number', '').startswith(f'PT-{today}')]) + 1
    return f'PT-{today}-{count:04d}'

# Procurement Dashboard
@procurement_bp.route('/api/procurement/dashboard', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_procurement_dashboard():
    """Get procurement dashboard metrics"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')
        
        # Get all procurement data
        purchase_requests = read_data('purchase_requests.json')
        purchase_orders = read_data('purchase_orders.json')
        proforma_invoices = read_data('proforma_invoices.json')
        delivery_notes = read_data('delivery_notes.json')
        
        # Apply tenant filtering
        if user_role not in ['admin', 'hub_admin']:
            purchase_requests = [pr for pr in purchase_requests if pr.get('requesting_tenant_id') == user_tenant_id]
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('to_tenant_id') == user_tenant_id]
            delivery_notes = [dn for dn in delivery_notes if dn.get('to_tenant_id') == user_tenant_id]
        
        # Calculate metrics
        dashboard_data = {
            'purchase_requests': {
                'total': len(purchase_requests),
                'pending': len([pr for pr in purchase_requests if pr.get('status') in ['draft', 'submitted', 'reviewing']]),
                'approved': len([pr for pr in purchase_requests if pr.get('status') == 'approved']),
                'fulfilled': len([pr for pr in purchase_requests if pr.get('status') == 'fulfilled'])
            },
            'purchase_orders': {
                'total': len(purchase_orders),
                'pending': len([po for po in purchase_orders if po.get('status') in ['draft', 'sent', 'acknowledged']]),
                'delivered': len([po for po in purchase_orders if po.get('status') in ['delivered', 'partial_delivered']])
            },
            'proforma_invoices': {
                'total': len(proforma_invoices),
                'pending_payment': len([pi for pi in proforma_invoices if pi.get('status') in ['sent', 'approved']]),
                'paid': len([pi for pi in proforma_invoices if pi.get('status') == 'paid'])
            },
            'deliveries': {
                'total': len(delivery_notes),
                'in_transit': len([dn for dn in delivery_notes if dn.get('status') in ['dispatched', 'in_transit']]),
                'delivered': len([dn for dn in delivery_notes if dn.get('status') == 'delivered'])
            }
        }
        
        # Recent activities (last 10)
        recent_requests = sorted(purchase_requests, key=lambda x: x.get('created_at', ''), reverse=True)[:5]
        recent_deliveries = sorted(delivery_notes, key=lambda x: x.get('created_at', ''), reverse=True)[:5]
        
        dashboard_data['recent_activities'] = {
            'requests': recent_requests,
            'deliveries': recent_deliveries
        }
        
        return jsonify(dashboard_data)
        
    except Exception as e:
        return jsonify({'message': f'Error fetching dashboard data: {str(e)}'}), 500

# Purchase Requests Routes
@procurement_bp.route('/api/procurement/purchase-requests', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_requests():
    """Get purchase requests with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')
        
        purchase_requests = read_data('purchase_requests.json')
        
        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            # Franchise users can only see their own requests
            purchase_requests = [pr for pr in purchase_requests if pr.get('requesting_tenant_id') == user_tenant_id]
        
        # Apply filters
        status = request.args.get('status')
        if status:
            purchase_requests = [pr for pr in purchase_requests if pr.get('status') == status]
        
        priority = request.args.get('priority')
        if priority:
            purchase_requests = [pr for pr in purchase_requests if pr.get('priority') == priority]
        
        # Date range filter
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date:
            purchase_requests = [pr for pr in purchase_requests if pr.get('request_date', '') >= start_date]
        if end_date:
            purchase_requests = [pr for pr in purchase_requests if pr.get('request_date', '') <= end_date]
        
        # Sort by creation date (newest first)
        purchase_requests.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)
        
        return paginate_results(purchase_requests, page, per_page)
        
    except Exception as e:
        return jsonify({'message': f'Error fetching purchase requests: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def create_purchase_request():
    """Create a new purchase request"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')

        # Validate required fields
        required_fields = ['items', 'priority', 'required_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'Missing required field: {field}'}), 400

        # Validate items
        if not data['items'] or len(data['items']) == 0:
            return jsonify({'message': 'At least one item is required'}), 400

        for item in data['items']:
            if not all(key in item for key in ['item_name', 'requested_quantity', 'unit']):
                return jsonify({'message': 'Each item must have item_name, requested_quantity, and unit'}), 400

        # Load existing requests
        purchase_requests = read_data('purchase_requests.json')

        # Generate new ID and request number
        new_id = 1
        if purchase_requests:
            new_id = max(pr['id'] for pr in purchase_requests) + 1

        request_number = generate_request_number()

        # Calculate total estimated amount
        total_estimated_amount = sum(
            float(item.get('estimated_unit_price', 0)) * int(item.get('requested_quantity', 0))
            for item in data['items']
        )

        # Create new purchase request
        new_request = {
            'id': new_id,
            'request_number': request_number,
            'requesting_tenant_id': user_tenant_id,
            'hub_tenant_id': 1,  # Mayiladuthurai hub
            'priority': data['priority'],
            'status': 'draft',
            'request_date': datetime.now().strftime('%Y-%m-%d'),
            'required_date': data['required_date'],
            'notes': data.get('notes', ''),
            'total_estimated_amount': total_estimated_amount,
            'items': data['items'],
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        purchase_requests.append(new_request)
        write_data('purchase_requests.json', purchase_requests)

        return jsonify(new_request), 201

    except Exception as e:
        return jsonify({'message': f'Error creating purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_request(request_id):
    """Get a specific purchase request"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        purchase_requests = read_data('purchase_requests.json')
        purchase_request = next((pr for pr in purchase_requests if pr['id'] == request_id), None)

        if not purchase_request:
            return jsonify({'message': 'Purchase request not found'}), 404

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request.get('requesting_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        return jsonify(purchase_request)

    except Exception as e:
        return jsonify({'message': f'Error fetching purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>', methods=['PUT'])
@token_required
@require_module_access('PROCUREMENT')
def update_purchase_request(request_id):
    """Update a purchase request"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        purchase_requests = read_data('purchase_requests.json')
        request_index = next((i for i, pr in enumerate(purchase_requests) if pr['id'] == request_id), None)

        if request_index is None:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = purchase_requests[request_index]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request.get('requesting_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

            # Franchise users can only edit draft requests
            if purchase_request.get('status') != 'draft':
                return jsonify({'message': 'Cannot edit submitted requests'}), 403

        # Update allowed fields
        updatable_fields = ['priority', 'required_date', 'notes', 'items']
        for field in updatable_fields:
            if field in data:
                purchase_request[field] = data[field]

        # Recalculate total if items changed
        if 'items' in data:
            total_estimated_amount = sum(
                float(item.get('estimated_unit_price', 0)) * int(item.get('requested_quantity', 0))
                for item in data['items']
            )
            purchase_request['total_estimated_amount'] = total_estimated_amount

        purchase_request['updated_at'] = datetime.now().isoformat()

        purchase_requests[request_index] = purchase_request
        write_data('purchase_requests.json', purchase_requests)

        return jsonify(purchase_request)

    except Exception as e:
        return jsonify({'message': f'Error updating purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>/submit', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def submit_purchase_request(request_id):
    """Submit a purchase request for approval"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        purchase_requests = read_data('purchase_requests.json')
        request_index = next((i for i, pr in enumerate(purchase_requests) if pr['id'] == request_id), None)

        if request_index is None:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = purchase_requests[request_index]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request.get('requesting_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        # Can only submit draft requests
        if purchase_request.get('status') != 'draft':
            return jsonify({'message': 'Only draft requests can be submitted'}), 400

        # Update status
        purchase_request['status'] = 'submitted'
        purchase_request['updated_at'] = datetime.now().isoformat()

        purchase_requests[request_index] = purchase_request
        write_data('purchase_requests.json', purchase_requests)

        # TODO: Send notification to hub admin

        return jsonify({'message': 'Purchase request submitted successfully', 'request': purchase_request})

    except Exception as e:
        return jsonify({'message': f'Error submitting purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>/approve', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def approve_purchase_request(request_id):
    """Approve a purchase request and trigger inventory check"""
    try:
        data = request.get_json()
        user = request.current_user

        purchase_requests = read_data('purchase_requests.json')
        request_index = next((i for i, pr in enumerate(purchase_requests) if pr['id'] == request_id), None)

        if request_index is None:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = purchase_requests[request_index]

        # Can only approve submitted requests
        if purchase_request.get('status') != 'submitted':
            return jsonify({'message': 'Only submitted requests can be approved'}), 400

        # Update status and approval info
        purchase_request['status'] = 'approved'
        purchase_request['approved_by'] = user.get('id')
        purchase_request['approved_at'] = datetime.now().isoformat()
        purchase_request['approval_notes'] = data.get('approval_notes', '')
        purchase_request['updated_at'] = datetime.now().isoformat()

        purchase_requests[request_index] = purchase_request
        write_data('purchase_requests.json', purchase_requests)

        # Trigger inventory check and proforma invoice generation
        result = check_inventory_and_create_proforma(purchase_request)

        return jsonify({
            'message': 'Purchase request approved successfully',
            'request': purchase_request,
            'inventory_check_result': result
        })

    except Exception as e:
        return jsonify({'message': f'Error approving purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>/reject', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def reject_purchase_request(request_id):
    """Reject a purchase request"""
    try:
        data = request.get_json()
        user = request.current_user

        purchase_requests = read_data('purchase_requests.json')
        request_index = next((i for i, pr in enumerate(purchase_requests) if pr['id'] == request_id), None)

        if request_index is None:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = purchase_requests[request_index]

        # Can only reject submitted requests
        if purchase_request.get('status') != 'submitted':
            return jsonify({'message': 'Only submitted requests can be rejected'}), 400

        # Update status and rejection info
        purchase_request['status'] = 'rejected'
        purchase_request['approved_by'] = user.get('id')
        purchase_request['approved_at'] = datetime.now().isoformat()
        purchase_request['approval_notes'] = data.get('rejection_reason', '')
        purchase_request['updated_at'] = datetime.now().isoformat()

        purchase_requests[request_index] = purchase_request
        write_data('purchase_requests.json', purchase_requests)

        # TODO: Send notification to requesting franchise

        return jsonify({'message': 'Purchase request rejected', 'request': purchase_request})

    except Exception as e:
        return jsonify({'message': f'Error rejecting purchase request: {str(e)}'}), 500

# Core Business Logic Functions
def check_inventory_and_create_proforma(purchase_request):
    """Check hub inventory availability and create proforma invoice or purchase order"""
    try:
        hub_tenant_id = 1  # Mayiladuthurai hub
        requesting_tenant_id = purchase_request['requesting_tenant_id']

        # Load hub inventory
        inventory = read_data('inventory.json')
        hub_inventory = [item for item in inventory if item.get('tenant_id') == hub_tenant_id]

        # Check availability for each requested item
        available_items = []
        unavailable_items = []

        for req_item in purchase_request['items']:
            item_name = req_item['item_name']
            requested_qty = int(req_item['requested_quantity'])

            # Find matching inventory item
            inventory_item = next(
                (inv for inv in hub_inventory if inv['name'].lower() == item_name.lower()),
                None
            )

            if inventory_item and inventory_item.get('quantity', 0) >= requested_qty:
                # Item available in sufficient quantity
                available_items.append({
                    'request_item': req_item,
                    'inventory_item': inventory_item,
                    'available_quantity': inventory_item.get('quantity', 0)
                })
            else:
                # Item not available or insufficient quantity
                unavailable_items.append({
                    'request_item': req_item,
                    'inventory_item': inventory_item,
                    'available_quantity': inventory_item.get('quantity', 0) if inventory_item else 0,
                    'shortage': requested_qty - (inventory_item.get('quantity', 0) if inventory_item else 0)
                })

        result = {
            'available_items': available_items,
            'unavailable_items': unavailable_items,
            'proforma_invoice': None,
            'purchase_orders': []
        }

        # Create proforma invoice for available items
        if available_items:
            proforma_invoice = create_proforma_invoice(purchase_request, available_items)
            result['proforma_invoice'] = proforma_invoice

        # Create purchase orders for unavailable items
        if unavailable_items:
            purchase_orders = create_purchase_orders_for_unavailable_items(unavailable_items, purchase_request)
            result['purchase_orders'] = purchase_orders

        return result

    except Exception as e:
        print(f"Error in inventory check: {str(e)}")
        return {'error': str(e)}

def create_proforma_invoice(purchase_request, available_items):
    """Create proforma invoice for available items"""
    try:
        # Load tenant configuration for payment terms
        tenants = read_data('tenants.json')
        requesting_tenant = next(
            (t for t in tenants if t['id'] == purchase_request['requesting_tenant_id']),
            None
        )

        if not requesting_tenant:
            raise Exception("Requesting tenant not found")

        # Load existing proforma invoices
        proforma_invoices = read_data('proforma_invoices.json')

        # Generate new ID and proforma number
        new_id = 1
        if proforma_invoices:
            new_id = max(pi['id'] for pi in proforma_invoices) + 1

        proforma_number = generate_proforma_number()

        # Calculate amounts
        subtotal = 0
        invoice_items = []

        for item_data in available_items:
            req_item = item_data['request_item']
            inv_item = item_data['inventory_item']

            quantity = int(req_item['requested_quantity'])
            unit_price = float(inv_item.get('selling_price', 0))
            total_price = quantity * unit_price
            subtotal += total_price

            invoice_items.append({
                'inventory_item_id': inv_item['id'],
                'item_name': req_item['item_name'],
                'item_description': req_item.get('item_description', ''),
                'quantity': quantity,
                'unit': req_item['unit'],
                'unit_price': unit_price,
                'total_price': total_price,
                'notes': req_item.get('notes', '')
            })

        # Calculate tax and total
        tax_rate = 18.0  # Default GST rate
        tax_amount = subtotal * (tax_rate / 100)
        total_amount = subtotal + tax_amount

        # Determine payment terms and due date
        payment_terms = requesting_tenant.get('default_payment_terms', 'prepaid')
        credit_period_days = requesting_tenant.get('default_credit_period_days', 30)

        payment_due_date = None
        if payment_terms == 'credit':
            payment_due_date = (datetime.now() + timedelta(days=credit_period_days)).strftime('%Y-%m-%d')

        # Create proforma invoice
        proforma_invoice = {
            'id': new_id,
            'proforma_number': proforma_number,
            'purchase_request_id': purchase_request['id'],
            'from_tenant_id': 1,  # Hub
            'to_tenant_id': purchase_request['requesting_tenant_id'],
            'status': 'draft',
            'invoice_date': datetime.now().strftime('%Y-%m-%d'),
            'valid_until': (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'payment_terms': payment_terms,
            'credit_period_days': credit_period_days,
            'subtotal': subtotal,
            'tax_rate': tax_rate,
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'paid_amount': 0,
            'balance_amount': total_amount,
            'payment_due_date': payment_due_date,
            'notes': f'Proforma invoice for purchase request {purchase_request["request_number"]}',
            'items': invoice_items,
            'created_by': 1,  # System generated
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        proforma_invoices.append(proforma_invoice)
        write_data('proforma_invoices.json', proforma_invoices)

        return proforma_invoice

    except Exception as e:
        print(f"Error creating proforma invoice: {str(e)}")
        return None

def create_purchase_orders_for_unavailable_items(unavailable_items, purchase_request):
    """Create purchase orders to suppliers for unavailable items"""
    try:
        # Load suppliers
        suppliers = read_data('suppliers.json')

        if not suppliers:
            print("No suppliers configured")
            return []

        # Group items by supplier (for now, use first available supplier)
        # In a real implementation, you might have supplier-item mappings
        default_supplier = suppliers[0]

        # Load existing purchase orders
        purchase_orders = read_data('purchase_orders.json')

        # Generate new ID and PO number
        new_id = 1
        if purchase_orders:
            new_id = max(po['id'] for po in purchase_orders) + 1

        po_number = generate_po_number()

        # Calculate amounts
        subtotal = 0
        order_items = []

        for item_data in unavailable_items:
            req_item = item_data['request_item']
            shortage = item_data['shortage']

            # Use estimated price or default
            unit_price = float(req_item.get('estimated_unit_price', 100))  # Default price
            total_price = shortage * unit_price
            subtotal += total_price

            order_items.append({
                'inventory_item_id': None,  # Will be linked when item is received
                'item_name': req_item['item_name'],
                'item_description': req_item.get('item_description', ''),
                'quantity': shortage,
                'unit': req_item['unit'],
                'unit_price': unit_price,
                'total_price': total_price,
                'received_quantity': 0,
                'notes': f'For purchase request {purchase_request["request_number"]}'
            })

        # Calculate tax and total
        tax_rate = 18.0  # Default GST rate
        tax_amount = subtotal * (tax_rate / 100)
        total_amount = subtotal + tax_amount

        # Create purchase order
        purchase_order = {
            'id': new_id,
            'po_number': po_number,
            'supplier_id': default_supplier['id'],
            'tenant_id': 1,  # Hub
            'status': 'draft',
            'order_date': datetime.now().strftime('%Y-%m-%d'),
            'expected_delivery_date': (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
            'subtotal': subtotal,
            'tax_rate': tax_rate,
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'payment_terms': '30 days',
            'delivery_address': 'AVINI Labs Mayiladuthurai, Main Hub',
            'notes': f'Purchase order for items not available in hub inventory. Related to purchase request {purchase_request["request_number"]}',
            'items': order_items,
            'purchase_request_id': purchase_request['id'],  # Link to original request
            'created_by': 1,  # System generated
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        purchase_orders.append(purchase_order)
        write_data('purchase_orders.json', purchase_orders)

        return [purchase_order]

    except Exception as e:
        print(f"Error creating purchase orders: {str(e)}")
        return []

# Proforma Invoice Routes
@procurement_bp.route('/api/procurement/proforma-invoices', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_proforma_invoices():
    """Get proforma invoices with filtering"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        proforma_invoices = read_data('proforma_invoices.json')

        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            # Franchise users can only see invoices addressed to them
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('to_tenant_id') == user_tenant_id]

        # Apply filters
        status = request.args.get('status')
        if status:
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('status') == status]

        payment_terms = request.args.get('payment_terms')
        if payment_terms:
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('payment_terms') == payment_terms]

        # Sort by creation date (newest first)
        proforma_invoices.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)

        return paginate_results(proforma_invoices, page, per_page)

    except Exception as e:
        return jsonify({'message': f'Error fetching proforma invoices: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/proforma-invoices/<int:invoice_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_proforma_invoice(invoice_id):
    """Get a specific proforma invoice"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        proforma_invoices = read_data('proforma_invoices.json')
        proforma_invoice = next((pi for pi in proforma_invoices if pi['id'] == invoice_id), None)

        if not proforma_invoice:
            return jsonify({'message': 'Proforma invoice not found'}), 404

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if proforma_invoice.get('to_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        return jsonify(proforma_invoice)

    except Exception as e:
        return jsonify({'message': f'Error fetching proforma invoice: {str(e)}'}), 500

# Purchase Order Routes
@procurement_bp.route('/api/procurement/purchase-orders', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_orders():
    """Get purchase orders with filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')

        purchase_orders = read_data('purchase_orders.json')

        # Apply tenant-based filtering (only hub can see purchase orders)
        if user_role not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Access denied. Only hub administrators can view purchase orders.'}), 403

        # Apply filters
        status = request.args.get('status')
        if status:
            purchase_orders = [po for po in purchase_orders if po.get('status') == status]

        supplier_id = request.args.get('supplier_id')
        if supplier_id:
            purchase_orders = [po for po in purchase_orders if po.get('supplier_id') == int(supplier_id)]

        # Date range filter
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date:
            purchase_orders = [po for po in purchase_orders if po.get('order_date', '') >= start_date]
        if end_date:
            purchase_orders = [po for po in purchase_orders if po.get('order_date', '') <= end_date]

        # Sort by creation date (newest first)
        purchase_orders.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)

        return paginate_results(purchase_orders, page, per_page)

    except Exception as e:
        return jsonify({'message': f'Error fetching purchase orders: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:order_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_order(order_id):
    """Get a specific purchase order"""
    try:
        user = request.current_user
        user_role = user.get('role')

        # Only hub can view purchase orders
        if user_role not in ['admin', 'hub_admin']:
            return jsonify({'message': 'Access denied'}), 403

        purchase_orders = read_data('purchase_orders.json')
        purchase_order = next((po for po in purchase_orders if po['id'] == order_id), None)

        if not purchase_order:
            return jsonify({'message': 'Purchase order not found'}), 404

        return jsonify(purchase_order)

    except Exception as e:
        return jsonify({'message': f'Error fetching purchase order: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:order_id>/send', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def send_purchase_order(order_id):
    """Send purchase order to supplier"""
    try:
        purchase_orders = read_data('purchase_orders.json')
        order_index = next((i for i, po in enumerate(purchase_orders) if po['id'] == order_id), None)

        if order_index is None:
            return jsonify({'message': 'Purchase order not found'}), 404

        purchase_order = purchase_orders[order_index]

        # Can only send draft orders
        if purchase_order.get('status') != 'draft':
            return jsonify({'message': 'Only draft orders can be sent'}), 400

        # Update status
        purchase_order['status'] = 'sent'
        purchase_order['sent_at'] = datetime.now().isoformat()
        purchase_order['updated_at'] = datetime.now().isoformat()

        purchase_orders[order_index] = purchase_order
        write_data('purchase_orders.json', purchase_orders)

        # TODO: Send email/notification to supplier

        return jsonify({'message': 'Purchase order sent successfully', 'order': purchase_order})

    except Exception as e:
        return jsonify({'message': f'Error sending purchase order: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:order_id>/receive', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def receive_purchase_order_items(order_id):
    """Mark items as received from supplier"""
    try:
        data = request.get_json()
        user = request.current_user

        purchase_orders = read_data('purchase_orders.json')
        order_index = next((i for i, po in enumerate(purchase_orders) if po['id'] == order_id), None)

        if order_index is None:
            return jsonify({'message': 'Purchase order not found'}), 404

        purchase_order = purchase_orders[order_index]

        # Validate received items
        received_items = data.get('received_items', [])
        if not received_items:
            return jsonify({'message': 'No received items specified'}), 400

        # Update received quantities
        for received_item in received_items:
            item_id = received_item.get('item_id')
            received_qty = int(received_item.get('received_quantity', 0))

            # Find the item in the purchase order
            for po_item in purchase_order['items']:
                if po_item.get('id') == item_id:
                    po_item['received_quantity'] = received_qty
                    break

        # Check if order is fully received
        all_received = all(
            item.get('received_quantity', 0) >= item.get('quantity', 0)
            for item in purchase_order['items']
        )

        # Update order status
        if all_received:
            purchase_order['status'] = 'delivered'
        else:
            purchase_order['status'] = 'partial_delivered'

        purchase_order['received_at'] = datetime.now().isoformat()
        purchase_order['received_by'] = user.get('id')
        purchase_order['updated_at'] = datetime.now().isoformat()

        purchase_orders[order_index] = purchase_order
        write_data('purchase_orders.json', purchase_orders)

        # TODO: Update inventory with received items
        # TODO: Generate delivery notes for pending franchise requests

        return jsonify({'message': 'Items received successfully', 'order': purchase_order})

    except Exception as e:
        return jsonify({'message': f'Error receiving items: {str(e)}'}), 500

# Delivery Note Routes
@procurement_bp.route('/api/procurement/delivery-notes', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_delivery_notes():
    """Get delivery notes with filtering"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        delivery_notes = read_data('delivery_notes.json')

        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            # Franchise users can only see deliveries to them
            delivery_notes = [dn for dn in delivery_notes if dn.get('to_tenant_id') == user_tenant_id]

        # Apply filters
        status = request.args.get('status')
        if status:
            delivery_notes = [dn for dn in delivery_notes if dn.get('status') == status]

        delivery_type = request.args.get('delivery_type')
        if delivery_type:
            delivery_notes = [dn for dn in delivery_notes if dn.get('delivery_type') == delivery_type]

        # Date range filter
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date:
            delivery_notes = [dn for dn in delivery_notes if dn.get('delivery_date', '') >= start_date]
        if end_date:
            delivery_notes = [dn for dn in delivery_notes if dn.get('delivery_date', '') <= end_date]

        # Sort by creation date (newest first)
        delivery_notes.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)

        return paginate_results(delivery_notes, page, per_page)

    except Exception as e:
        return jsonify({'message': f'Error fetching delivery notes: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:note_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_delivery_note(note_id):
    """Get a specific delivery note"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        delivery_notes = read_data('delivery_notes.json')
        delivery_note = next((dn for dn in delivery_notes if dn['id'] == note_id), None)

        if not delivery_note:
            return jsonify({'message': 'Delivery note not found'}), 404

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if delivery_note.get('to_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        return jsonify(delivery_note)

    except Exception as e:
        return jsonify({'message': f'Error fetching delivery note: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/delivery-notes', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def create_delivery_note():
    """Create a new delivery note"""
    try:
        data = request.get_json()
        user = request.current_user

        # Validate required fields
        required_fields = ['to_tenant_id', 'delivery_type', 'items']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'Missing required field: {field}'}), 400

        # Load existing delivery notes
        delivery_notes = read_data('delivery_notes.json')

        # Generate new ID and delivery number
        new_id = 1
        if delivery_notes:
            new_id = max(dn['id'] for dn in delivery_notes) + 1

        delivery_number = generate_delivery_number()

        # Calculate total amount
        total_amount = sum(
            float(item.get('unit_price', 0)) * int(item.get('quantity', 0))
            for item in data['items']
        )

        # Create new delivery note
        new_delivery_note = {
            'id': new_id,
            'delivery_number': delivery_number,
            'from_tenant_id': 1,  # Hub
            'to_tenant_id': data['to_tenant_id'],
            'delivery_type': data['delivery_type'],  # 'hub_to_franchise' or 'supplier_to_hub'
            'status': 'prepared',
            'delivery_date': data.get('delivery_date', datetime.now().strftime('%Y-%m-%d')),
            'expected_delivery_date': data.get('expected_delivery_date'),
            'delivery_address': data.get('delivery_address', ''),
            'total_amount': total_amount,
            'notes': data.get('notes', ''),
            'items': data['items'],
            'purchase_request_id': data.get('purchase_request_id'),
            'purchase_order_id': data.get('purchase_order_id'),
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        delivery_notes.append(new_delivery_note)
        write_data('delivery_notes.json', delivery_notes)

        return jsonify(new_delivery_note), 201

    except Exception as e:
        return jsonify({'message': f'Error creating delivery note: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:note_id>/dispatch', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def dispatch_delivery_note(note_id):
    """Mark delivery note as dispatched"""
    try:
        data = request.get_json()
        user = request.current_user

        delivery_notes = read_data('delivery_notes.json')
        note_index = next((i for i, dn in enumerate(delivery_notes) if dn['id'] == note_id), None)

        if note_index is None:
            return jsonify({'message': 'Delivery note not found'}), 404

        delivery_note = delivery_notes[note_index]

        # Can only dispatch prepared notes
        if delivery_note.get('status') != 'prepared':
            return jsonify({'message': 'Only prepared delivery notes can be dispatched'}), 400

        # Update status
        delivery_note['status'] = 'dispatched'
        delivery_note['dispatched_at'] = datetime.now().isoformat()
        delivery_note['dispatched_by'] = user.get('id')
        delivery_note['tracking_number'] = data.get('tracking_number', '')
        delivery_note['carrier'] = data.get('carrier', '')
        delivery_note['updated_at'] = datetime.now().isoformat()

        delivery_notes[note_index] = delivery_note
        write_data('delivery_notes.json', delivery_notes)

        # TODO: Send notification to receiving franchise

        return jsonify({'message': 'Delivery note dispatched successfully', 'delivery_note': delivery_note})

    except Exception as e:
        return jsonify({'message': f'Error dispatching delivery note: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:note_id>/confirm', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def confirm_delivery_note(note_id):
    """Confirm delivery receipt"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        delivery_notes = read_data('delivery_notes.json')
        note_index = next((i for i, dn in enumerate(delivery_notes) if dn['id'] == note_id), None)

        if note_index is None:
            return jsonify({'message': 'Delivery note not found'}), 404

        delivery_note = delivery_notes[note_index]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if delivery_note.get('to_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        # Can only confirm dispatched or in_transit notes
        if delivery_note.get('status') not in ['dispatched', 'in_transit']:
            return jsonify({'message': 'Only dispatched deliveries can be confirmed'}), 400

        # Update status
        delivery_note['status'] = 'delivered'
        delivery_note['delivered_at'] = datetime.now().isoformat()
        delivery_note['received_by'] = user.get('id')
        delivery_note['receiver_signature'] = data.get('signature', '')
        delivery_note['delivery_notes'] = data.get('delivery_notes', '')
        delivery_note['updated_at'] = datetime.now().isoformat()

        delivery_notes[note_index] = delivery_note
        write_data('delivery_notes.json', delivery_notes)

        # TODO: Update inventory with delivered items
        # TODO: Generate final invoice if payment terms are post-delivery

        return jsonify({'message': 'Delivery confirmed successfully', 'delivery_note': delivery_note})

    except Exception as e:
        return jsonify({'message': f'Error confirming delivery: {str(e)}'}), 500

# Payment Transaction Routes
@procurement_bp.route('/api/procurement/payment-transactions', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_payment_transactions():
    """Get payment transactions with filtering"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        payment_transactions = read_data('payment_transactions.json')

        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            # Franchise users can only see their own transactions
            payment_transactions = [pt for pt in payment_transactions if pt.get('payer_tenant_id') == user_tenant_id]

        # Apply filters
        status = request.args.get('status')
        if status:
            payment_transactions = [pt for pt in payment_transactions if pt.get('status') == status]

        payment_method = request.args.get('payment_method')
        if payment_method:
            payment_transactions = [pt for pt in payment_transactions if pt.get('payment_method') == payment_method]

        # Date range filter
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date:
            payment_transactions = [pt for pt in payment_transactions if pt.get('payment_date', '') >= start_date]
        if end_date:
            payment_transactions = [pt for pt in payment_transactions if pt.get('payment_date', '') <= end_date]

        # Sort by creation date (newest first)
        payment_transactions.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)

        return paginate_results(payment_transactions, page, per_page)

    except Exception as e:
        return jsonify({'message': f'Error fetching payment transactions: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/payment-transactions', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def create_payment_transaction():
    """Create a new payment transaction"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Validate required fields
        required_fields = ['proforma_invoice_id', 'payment_amount', 'payment_method']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'Missing required field: {field}'}), 400

        # Load proforma invoice to validate
        proforma_invoices = read_data('proforma_invoices.json')
        proforma_invoice = next(
            (pi for pi in proforma_invoices if pi['id'] == data['proforma_invoice_id']),
            None
        )

        if not proforma_invoice:
            return jsonify({'message': 'Proforma invoice not found'}), 404

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if proforma_invoice.get('to_tenant_id') != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        # Validate payment amount
        payment_amount = float(data['payment_amount'])
        balance_amount = float(proforma_invoice.get('balance_amount', 0))

        if payment_amount <= 0:
            return jsonify({'message': 'Payment amount must be greater than zero'}), 400

        if payment_amount > balance_amount:
            return jsonify({'message': 'Payment amount cannot exceed balance amount'}), 400

        # Load existing transactions
        payment_transactions = read_data('payment_transactions.json')

        # Generate new ID and transaction number
        new_id = 1
        if payment_transactions:
            new_id = max(pt['id'] for pt in payment_transactions) + 1

        transaction_number = generate_transaction_number()

        # Create new payment transaction
        new_transaction = {
            'id': new_id,
            'transaction_number': transaction_number,
            'proforma_invoice_id': data['proforma_invoice_id'],
            'payer_tenant_id': proforma_invoice['to_tenant_id'],
            'payee_tenant_id': proforma_invoice['from_tenant_id'],
            'payment_amount': payment_amount,
            'payment_method': data['payment_method'],
            'payment_date': data.get('payment_date', datetime.now().strftime('%Y-%m-%d')),
            'reference_number': data.get('reference_number', ''),
            'notes': data.get('notes', ''),
            'status': 'completed',  # For now, assume all payments are immediately completed
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        payment_transactions.append(new_transaction)
        write_data('payment_transactions.json', payment_transactions)

        # Update proforma invoice
        proforma_invoice['paid_amount'] = float(proforma_invoice.get('paid_amount', 0)) + payment_amount
        proforma_invoice['balance_amount'] = float(proforma_invoice.get('total_amount', 0)) - proforma_invoice['paid_amount']

        if proforma_invoice['balance_amount'] <= 0:
            proforma_invoice['status'] = 'paid'
        else:
            proforma_invoice['status'] = 'partial_paid'

        proforma_invoice['updated_at'] = datetime.now().isoformat()

        # Update proforma invoices
        proforma_index = next((i for i, pi in enumerate(proforma_invoices) if pi['id'] == data['proforma_invoice_id']), None)
        if proforma_index is not None:
            proforma_invoices[proforma_index] = proforma_invoice
            write_data('proforma_invoices.json', proforma_invoices)

        return jsonify(new_transaction), 201

    except Exception as e:
        return jsonify({'message': f'Error creating payment transaction: {str(e)}'}), 500

# Inventory Transfer Routes
@procurement_bp.route('/api/procurement/inventory-transfers', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_inventory_transfers():
    """Get inventory transfers with filtering"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        inventory_transfers = read_data('inventory_transfers.json')

        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            # Franchise users can only see transfers involving them
            inventory_transfers = [
                it for it in inventory_transfers
                if it.get('from_tenant_id') == user_tenant_id or it.get('to_tenant_id') == user_tenant_id
            ]

        # Apply filters
        status = request.args.get('status')
        if status:
            inventory_transfers = [it for it in inventory_transfers if it.get('status') == status]

        transfer_type = request.args.get('transfer_type')
        if transfer_type:
            inventory_transfers = [it for it in inventory_transfers if it.get('transfer_type') == transfer_type]

        # Sort by creation date (newest first)
        inventory_transfers.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)

        return paginate_results(inventory_transfers, page, per_page)

    except Exception as e:
        return jsonify({'message': f'Error fetching inventory transfers: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/inventory-transfers/<int:transfer_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_inventory_transfer(transfer_id):
    """Get a specific inventory transfer"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        inventory_transfers = read_data('inventory_transfers.json')
        inventory_transfer = next((it for it in inventory_transfers if it['id'] == transfer_id), None)

        if not inventory_transfer:
            return jsonify({'message': 'Inventory transfer not found'}), 404

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if (inventory_transfer.get('from_tenant_id') != user_tenant_id and
                inventory_transfer.get('to_tenant_id') != user_tenant_id):
                return jsonify({'message': 'Access denied'}), 403

        return jsonify(inventory_transfer)

    except Exception as e:
        return jsonify({'message': f'Error fetching inventory transfer: {str(e)}'}), 500

# Reporting and Analytics Routes
@procurement_bp.route('/api/procurement/reports/summary', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_procurement_summary_report():
    """Get procurement summary report"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Date range parameters
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        # Load all procurement data
        purchase_requests = read_data('purchase_requests.json')
        purchase_orders = read_data('purchase_orders.json')
        proforma_invoices = read_data('proforma_invoices.json')
        delivery_notes = read_data('delivery_notes.json')
        payment_transactions = read_data('payment_transactions.json')

        # Apply tenant filtering
        if user_role not in ['admin', 'hub_admin']:
            purchase_requests = [pr for pr in purchase_requests if pr.get('requesting_tenant_id') == user_tenant_id]
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('to_tenant_id') == user_tenant_id]
            delivery_notes = [dn for dn in delivery_notes if dn.get('to_tenant_id') == user_tenant_id]
            payment_transactions = [pt for pt in payment_transactions if pt.get('payer_tenant_id') == user_tenant_id]

        # Filter by date range
        purchase_requests = [pr for pr in purchase_requests if start_date <= pr.get('request_date', '') <= end_date]
        purchase_orders = [po for po in purchase_orders if start_date <= po.get('order_date', '') <= end_date]
        proforma_invoices = [pi for pi in proforma_invoices if start_date <= pi.get('invoice_date', '') <= end_date]
        delivery_notes = [dn for dn in delivery_notes if start_date <= dn.get('delivery_date', '') <= end_date]
        payment_transactions = [pt for pt in payment_transactions if start_date <= pt.get('payment_date', '') <= end_date]

        # Calculate metrics
        report_data = {
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'purchase_requests': {
                'total_count': len(purchase_requests),
                'total_estimated_amount': sum(float(pr.get('total_estimated_amount', 0)) for pr in purchase_requests),
                'by_status': {},
                'by_priority': {}
            },
            'purchase_orders': {
                'total_count': len(purchase_orders),
                'total_amount': sum(float(po.get('total_amount', 0)) for po in purchase_orders),
                'by_status': {}
            },
            'proforma_invoices': {
                'total_count': len(proforma_invoices),
                'total_amount': sum(float(pi.get('total_amount', 0)) for pi in proforma_invoices),
                'total_paid': sum(float(pi.get('paid_amount', 0)) for pi in proforma_invoices),
                'total_outstanding': sum(float(pi.get('balance_amount', 0)) for pi in proforma_invoices),
                'by_status': {}
            },
            'deliveries': {
                'total_count': len(delivery_notes),
                'total_amount': sum(float(dn.get('total_amount', 0)) for dn in delivery_notes),
                'by_status': {}
            },
            'payments': {
                'total_count': len(payment_transactions),
                'total_amount': sum(float(pt.get('payment_amount', 0)) for pt in payment_transactions),
                'by_method': {}
            }
        }

        # Calculate status breakdowns
        for pr in purchase_requests:
            status = pr.get('status', 'unknown')
            priority = pr.get('priority', 'unknown')
            report_data['purchase_requests']['by_status'][status] = report_data['purchase_requests']['by_status'].get(status, 0) + 1
            report_data['purchase_requests']['by_priority'][priority] = report_data['purchase_requests']['by_priority'].get(priority, 0) + 1

        for po in purchase_orders:
            status = po.get('status', 'unknown')
            report_data['purchase_orders']['by_status'][status] = report_data['purchase_orders']['by_status'].get(status, 0) + 1

        for pi in proforma_invoices:
            status = pi.get('status', 'unknown')
            report_data['proforma_invoices']['by_status'][status] = report_data['proforma_invoices']['by_status'].get(status, 0) + 1

        for dn in delivery_notes:
            status = dn.get('status', 'unknown')
            report_data['deliveries']['by_status'][status] = report_data['deliveries']['by_status'].get(status, 0) + 1

        for pt in payment_transactions:
            method = pt.get('payment_method', 'unknown')
            report_data['payments']['by_method'][method] = report_data['payments']['by_method'].get(method, 0) + 1

        return jsonify(report_data)

    except Exception as e:
        return jsonify({'message': f'Error generating summary report: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/reports/trends', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_procurement_trends_report():
    """Get procurement trends report"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Date range parameters (default to last 6 months)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)

        # Load procurement data
        purchase_requests = read_data('purchase_requests.json')
        proforma_invoices = read_data('proforma_invoices.json')

        # Apply tenant filtering
        if user_role not in ['admin', 'hub_admin']:
            purchase_requests = [pr for pr in purchase_requests if pr.get('requesting_tenant_id') == user_tenant_id]
            proforma_invoices = [pi for pi in proforma_invoices if pi.get('to_tenant_id') == user_tenant_id]

        # Generate monthly trends
        monthly_trends = {}
        current_date = start_date

        while current_date <= end_date:
            month_key = current_date.strftime('%Y-%m')
            month_start = current_date.strftime('%Y-%m-01')

            # Calculate next month
            if current_date.month == 12:
                next_month = current_date.replace(year=current_date.year + 1, month=1)
            else:
                next_month = current_date.replace(month=current_date.month + 1)
            month_end = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')

            # Filter data for this month
            month_requests = [pr for pr in purchase_requests if month_start <= pr.get('request_date', '') <= month_end]
            month_invoices = [pi for pi in proforma_invoices if month_start <= pi.get('invoice_date', '') <= month_end]

            monthly_trends[month_key] = {
                'month': month_key,
                'requests_count': len(month_requests),
                'requests_amount': sum(float(pr.get('total_estimated_amount', 0)) for pr in month_requests),
                'invoices_count': len(month_invoices),
                'invoices_amount': sum(float(pi.get('total_amount', 0)) for pi in month_invoices),
                'payments_received': sum(float(pi.get('paid_amount', 0)) for pi in month_invoices)
            }

            current_date = next_month

        return jsonify({
            'trends': list(monthly_trends.values()),
            'period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        })

    except Exception as e:
        return jsonify({'message': f'Error generating trends report: {str(e)}'}), 500
