#!/usr/bin/env python3
"""
AVINI Labs Accounting Module Migration Script
Creates all accounting tables and initial data
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_manager import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AccountingModuleMigration:
    """Migration class for creating accounting module tables and data"""
    
    def __init__(self):
        self.db = db_manager
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.schema_file = os.path.join(self.base_dir, 'create_accounting_tables.sql')
        self.data_file = os.path.join(self.base_dir, 'accounting_initial_data.sql')
    
    def check_prerequisites(self) -> bool:
        """Check if required tables exist"""
        try:
            # Check if core tables exist
            required_tables = ['tenants', 'users', 'departments']
            existing_tables = self.db.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            existing_table_names = [table['name'] for table in existing_tables]
            
            missing_tables = [table for table in required_tables if table not in existing_table_names]
            
            if missing_tables:
                logger.error(f"Missing required tables: {missing_tables}")
                logger.error("Please run the main database initialization first")
                return False
            
            logger.info("Prerequisites check passed")
            return True
            
        except Exception as e:
            logger.error(f"Error checking prerequisites: {str(e)}")
            return False
    
    def backup_database(self) -> str:
        """Create a backup of the current database"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.db.backup_database(f"{self.db.db_path}.accounting_migration_backup_{timestamp}")
            logger.info(f"Database backed up to: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            raise
    
    def execute_sql_file(self, file_path: str, description: str) -> bool:
        """Execute SQL statements from a file"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"SQL file not found: {file_path}")
                return False

            logger.info(f"Executing {description}...")

            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            # Remove comments and clean up the SQL
            lines = sql_content.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()
                # Skip comment lines and empty lines
                if line.startswith('--') or not line:
                    continue
                # Remove inline comments
                if '--' in line:
                    line = line.split('--')[0].strip()
                if line:
                    cleaned_lines.append(line)

            cleaned_sql = ' '.join(cleaned_lines)

            # Split into individual statements
            statements = [stmt.strip() for stmt in cleaned_sql.split(';') if stmt.strip()]

            # Separate table creation from index creation
            table_statements = []
            index_statements = []

            for statement in statements:
                if statement:
                    if 'CREATE INDEX' in statement.upper():
                        index_statements.append(statement)
                    else:
                        table_statements.append(statement)

            # Execute table creation first
            executed_count = 0
            logger.info(f"Creating tables and constraints... ({len(table_statements)} statements)")
            for i, statement in enumerate(table_statements):
                try:
                    if 'CREATE TABLE' in statement.upper():
                        # Extract table name for logging
                        parts = statement.upper().split('CREATE TABLE IF NOT EXISTS')
                        if len(parts) > 1:
                            table_name = parts[1].split('(')[0].strip()
                            logger.info(f"Creating table: {table_name}")

                    self.db.execute_update(statement)
                    executed_count += 1

                    # Log progress for large files
                    if executed_count % 5 == 0:
                        logger.info(f"Executed {executed_count}/{len(table_statements)} table statements...")

                except Exception as e:
                    logger.error(f"Error executing table statement {i+1}: {statement[:200]}...")
                    logger.error(f"Error: {str(e)}")
                    raise

            # Execute index creation second
            logger.info(f"Creating indexes... ({len(index_statements)} statements)")
            for i, statement in enumerate(index_statements):
                try:
                    self.db.execute_update(statement)
                    executed_count += 1

                    if (i + 1) % 5 == 0:
                        logger.info(f"Created {i + 1}/{len(index_statements)} indexes...")

                except Exception as e:
                    logger.error(f"Error executing index statement {i+1}: {statement[:100]}...")
                    logger.error(f"Error: {str(e)}")
                    # Don't raise for index errors, just log and continue
                    logger.warning(f"Skipping index creation due to error")
                    continue

            logger.info(f"Successfully executed {executed_count} statements from {description}")
            return True

        except Exception as e:
            logger.error(f"Error executing {description}: {str(e)}")
            raise
    
    def verify_tables_created(self) -> bool:
        """Verify that all accounting tables were created"""
        try:
            expected_tables = [
                'chart_of_accounts', 'account_groups', 'account_group_mappings',
                'journal_entries', 'journal_entry_lines',
                'vendors', 'purchase_invoices', 'purchase_invoice_lines',
                'vendor_payments', 'vendor_payment_allocations',
                'customers', 'sales_invoices', 'sales_invoice_lines',
                'customer_payments', 'customer_payment_allocations',
                'bank_accounts', 'cost_centers', 'projects',
                'tax_types', 'tax_accounts',
                'inventory_valuation_methods', 'inventory_accounting_entries',
                'financial_periods', 'financial_reports',
                'accounting_audit_trail',
                'procurement_accounting_mapping', 'billing_accounting_mapping'
            ]
            
            existing_tables = self.db.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            existing_table_names = [table['name'] for table in existing_tables]
            
            missing_tables = [table for table in expected_tables if table not in existing_table_names]
            
            if missing_tables:
                logger.error(f"Missing accounting tables: {missing_tables}")
                return False
            
            logger.info(f"All {len(expected_tables)} accounting tables created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying tables: {str(e)}")
            return False
    
    def verify_initial_data(self) -> bool:
        """Verify that initial data was inserted"""
        try:
            # Check chart of accounts
            accounts = self.db.execute_query(
                "SELECT COUNT(*) as count FROM chart_of_accounts WHERE tenant_id = 1"
            )
            account_count = accounts[0]['count'] if accounts else 0
            
            # Check tax types
            taxes = self.db.execute_query(
                "SELECT COUNT(*) as count FROM tax_types WHERE tenant_id = 1"
            )
            tax_count = taxes[0]['count'] if taxes else 0
            
            # Check cost centers
            cost_centers = self.db.execute_query(
                "SELECT COUNT(*) as count FROM cost_centers WHERE tenant_id = 1"
            )
            cost_center_count = cost_centers[0]['count'] if cost_centers else 0
            
            logger.info(f"Initial data verification:")
            logger.info(f"  - Chart of Accounts: {account_count} accounts")
            logger.info(f"  - Tax Types: {tax_count} tax types")
            logger.info(f"  - Cost Centers: {cost_center_count} cost centers")
            
            if account_count < 50:  # We expect at least 50 accounts
                logger.warning("Fewer accounts than expected in chart of accounts")
                return False
            
            if tax_count < 5:  # We expect at least 5 tax types
                logger.warning("Fewer tax types than expected")
                return False
            
            logger.info("Initial data verification passed")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying initial data: {str(e)}")
            return False
    
    def create_accounting_module_entry(self) -> bool:
        """Add accounting module to the modules JSON file"""
        try:
            import json

            modules_file = os.path.join(self.base_dir, 'data', 'modules.json')

            if not os.path.exists(modules_file):
                logger.warning("Modules JSON file not found, skipping module entry creation")
                return True

            # Read existing modules
            with open(modules_file, 'r', encoding='utf-8') as f:
                modules = json.load(f)

            # Check if accounting module already exists
            existing_module = next((m for m in modules if m.get('code') == 'ACCOUNTING'), None)

            if existing_module:
                logger.info("Accounting module entry already exists")
                return True

            # Get next module ID
            max_id = max((m.get('id', 0) for m in modules), default=0)
            next_id = max_id + 1

            # Create accounting module entry
            accounting_module = {
                'id': next_id,
                'name': 'Accounting & Finance',
                'code': 'ACCOUNTING',
                'description': 'Complete accounting system with general ledger, accounts payable/receivable, and financial reporting',
                'route': '/accounting',
                'icon': 'faCalculator',
                'category': 'finance',
                'is_core': False,
                'is_active': True
            }

            # Add to modules list
            modules.append(accounting_module)

            # Write back to file
            with open(modules_file, 'w', encoding='utf-8') as f:
                json.dump(modules, f, indent=2, ensure_ascii=False)

            logger.info("Created accounting module entry in modules.json")
            return True

        except Exception as e:
            logger.error(f"Error creating accounting module entry: {str(e)}")
            return False
    
    def run_migration(self) -> bool:
        """Run the complete accounting module migration"""
        try:
            logger.info("=" * 60)
            logger.info("AVINI LABS ACCOUNTING MODULE MIGRATION")
            logger.info("=" * 60)
            
            # Step 1: Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Step 2: Create backup
            backup_path = self.backup_database()
            
            # Step 3: Execute schema creation
            if not self.execute_sql_file(self.schema_file, "accounting tables schema"):
                return False
            
            # Step 4: Verify tables created
            if not self.verify_tables_created():
                return False
            
            # Step 5: Execute initial data
            if not self.execute_sql_file(self.data_file, "accounting initial data"):
                return False
            
            # Step 6: Verify initial data
            if not self.verify_initial_data():
                return False
            
            # Step 7: Create module entry
            if not self.create_accounting_module_entry():
                return False
            
            logger.info("=" * 60)
            logger.info("ACCOUNTING MODULE MIGRATION COMPLETED SUCCESSFULLY!")
            logger.info("=" * 60)
            logger.info(f"Database backup created at: {backup_path}")
            logger.info("The accounting module is now ready for use.")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            logger.error("Please restore from backup if needed")
            return False

def main():
    """Main function to run the migration"""
    migration = AccountingModuleMigration()
    
    try:
        success = migration.run_migration()
        if success:
            print("\n✅ Accounting module migration completed successfully!")
            print("You can now use the accounting features in AVINI Labs.")
        else:
            print("\n❌ Accounting module migration failed!")
            print("Please check the logs and try again.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during migration: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
